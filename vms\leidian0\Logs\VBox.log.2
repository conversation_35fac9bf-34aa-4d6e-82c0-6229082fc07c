00:00:00.679746 VirtualBox VM 4.1.34 r2633156352 win.amd64 (Jul 26 2022 16:16:15) release log
00:00:00.679748 Log opened 2025-08-04T15:05:13.888863000Z
00:00:00.679749 Build Type: release
00:00:00.679753 OS Product: Windows 10
00:00:00.679754 OS Release: 10.0.19045
00:00:00.679754 OS Service Pack: 
00:00:00.697727 DMI Product Name: 10NBCTO1WW
00:00:00.702894 DMI Product Version: ThinkCentre M710t-N000
00:00:00.702909 Firmware type: UEFI
00:00:00.703272 Secure Boot: VERR_PRIVILEGE_NOT_HELD
00:00:00.703315 Host RAM: 32675MB (31.9GB) total, 18508MB (18.0GB) available
00:00:00.703318 Executable: C:\Program Files\ldplayer9box\Ld9BoxHeadless.exe
00:00:00.703318 Process ID: 21388
00:00:00.703319 Package type: WINDOWS_64BITS_GENERIC (OSE)
00:00:00.705699 Installed Extension Packs:
00:00:00.705732   None installed!
00:00:00.706541 Console: Machine state changed to 'Starting'
00:00:00.995257 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.995275 SUP: seg #1: R X 0x00001000 LB 0x00109000
00:00:00.995282 SUP: seg #2: R   0x0010a000 LB 0x0004a000
00:00:00.995287 SUP: seg #3: RW  0x00154000 LB 0x00013000
00:00:00.995292 SUP: seg #4: R   0x00167000 LB 0x0000e000
00:00:00.995297 SUP: seg #5: RW  0x00175000 LB 0x00003000
00:00:00.995302 SUP: seg #6: R   0x00178000 LB 0x0000b000
00:00:00.995307 SUP: seg #7: RWX 0x00183000 LB 0x00002000
00:00:00.995312 SUP: seg #8: R   0x00185000 LB 0x00007000
00:00:00.996103 SUP: Loaded Ld9VMMR0.r0 (C:\Program Files\ldplayer9box\Ld9VMMR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.996124 SUP: VMMR0EntryEx located at XXXXXXXXXXXXXXXX and VMMR0EntryFast at XXXXXXXXXXXXXXXX
00:00:00.996129 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9VMMR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.999562 Guest OS type: 'Linux26_64'
00:00:01.001130 fHMForced=true - No raw-mode support in this build!
00:00:01.005307 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\Snapshots' (snapshots) is unknown
00:00:01.005319 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk' is ntfs
00:00:01.006135 File system of 'D:\Program Files\LDPlayer9\system.vmdk' is ntfs
00:00:01.007005 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk' is ntfs
00:00:01.043432 Shared Clipboard: Service loaded
00:00:01.043450 Shared Clipboard: Mode: Off
00:00:01.043552 Shared Clipboard: Service running in headless mode
00:00:01.045501 Drag and drop service loaded
00:00:01.045532 Drag and drop mode: Off
00:00:01.047494 Extradata overrides:
00:00:01.047510   VBoxInternal/Devices/fastpipe/0/PCIBusNo="0"
00:00:01.047554   VBoxInternal/Devices/fastpipe/0/PCIDeviceNo="18"
00:00:01.047590   VBoxInternal/Devices/fastpipe/0/PCIFunctionNo="0"
00:00:01.047639   VBoxInternal/Devices/fastpipe/0/Trusted="1"
00:00:01.047751   VBoxInternal/PDM/Devices/fastpipe/Path="fastpipe.dll"
00:00:01.048055 ************************* CFGM dump *************************
00:00:01.048056 [/] (level 0)
00:00:01.048057   CpuExecutionCap   <integer> = 0x0000000000000064 (100)
00:00:01.048059   EnablePAE         <integer> = 0x0000000000000000 (0)
00:00:01.048060   HMEnabled         <integer> = 0x0000000000000001 (1)
00:00:01.048060   MemBalloonSize    <integer> = 0x0000000000000000 (0)
00:00:01.048061   Name              <string>  = "leidian0" (cb=9)
00:00:01.048062   NumCPUs           <integer> = 0x0000000000000004 (4)
00:00:01.048062   PageFusionAllowed <integer> = 0x0000000000000000 (0)
00:00:01.048063   RamHoleSize       <integer> = 0x0000000020000000 (536 870 912, 512 MB)
00:00:01.048064   RamSize           <integer> = 0x0000000180000000 (6 442 450 944, 6 144 MB, 6.0 GB)
00:00:01.048079   TimerMillies      <integer> = 0x000000000000000a (10)
00:00:01.048079   UUID              <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:01.048086 
00:00:01.048086 [/CPUM/] (level 1)
00:00:01.048102   GuestCpuName       <string>  = "host" (cb=5)
00:00:01.048103   NestedHWVirt       <integer> = 0x0000000000000000 (0)
00:00:01.048103   PortableCpuIdLevel <integer> = 0x0000000000000000 (0)
00:00:01.048104   SpecCtrl           <integer> = 0x0000000000000001 (1)
00:00:01.048104 
00:00:01.048105 [/CPUM/IsaExts/] (level 2)
00:00:01.048105 
00:00:01.048105 [/DBGC/] (level 1)
00:00:01.048106   GlobalInitScript <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-init" (cb=39)
00:00:01.048107   HistoryFile      <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-history" (cb=42)
00:00:01.048107   LocalInitScript  <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/dbgc-init" (cb=50)
00:00:01.048108 
00:00:01.048108 [/DBGF/] (level 1)
00:00:01.048108   Path <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/debug/;D:\Program Files\LDPlayer9\vms\leidian0/;cache*D:\Program Files\LDPlayer9\vms\leidian0/dbgcache/;C:\Users\<USER>\" (cb=159)
00:00:01.048109 
00:00:01.048109 [/Devices/] (level 1)
00:00:01.048110 
00:00:01.048110 [/Devices/8237A/] (level 2)
00:00:01.048110 
00:00:01.048111 [/Devices/8237A/0/] (level 3)
00:00:01.048111   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048112 
00:00:01.048112 [/Devices/GIMDev/] (level 2)
00:00:01.048112 
00:00:01.048113 [/Devices/GIMDev/0/] (level 3)
00:00:01.048113   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048114 
00:00:01.048114 [/Devices/VMMDev/] (level 2)
00:00:01.048114 
00:00:01.048115 [/Devices/VMMDev/0/] (level 3)
00:00:01.048115   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048116   PCIDeviceNo   <integer> = 0x0000000000000004 (4)
00:00:01.048116   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048117   Trusted       <integer> = 0x0000000000000001 (1)
00:00:01.048117 
00:00:01.048117 [/Devices/VMMDev/0/Config/] (level 4)
00:00:01.048118   GuestCoreDumpDir <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\Snapshots" (cb=50)
00:00:01.048119 
00:00:01.048119 [/Devices/VMMDev/0/LUN#0/] (level 4)
00:00:01.048120   Driver <string>  = "HGCM" (cb=5)
00:00:01.048120 
00:00:01.048120 [/Devices/VMMDev/0/LUN#0/Config/] (level 5)
00:00:01.048121   Object <integer> = 0x0000000051b7e1b0 (1 371 005 360)
00:00:01.048122 
00:00:01.048122 [/Devices/VMMDev/0/LUN#999/] (level 4)
00:00:01.048123   Driver <string>  = "MainStatus" (cb=11)
00:00:01.048123 
00:00:01.048123 [/Devices/VMMDev/0/LUN#999/Config/] (level 5)
00:00:01.048124   First   <integer> = 0x0000000000000000 (0)
00:00:01.048125   Last    <integer> = 0x0000000000000000 (0)
00:00:01.048125   papLeds <integer> = 0x0000000051b745a8 (1 370 965 416)
00:00:01.048126 
00:00:01.048126 [/Devices/acpi/] (level 2)
00:00:01.048126 
00:00:01.048127 [/Devices/acpi/0/] (level 3)
00:00:01.048127   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048128   PCIDeviceNo   <integer> = 0x0000000000000007 (7)
00:00:01.048128   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048129   Trusted       <integer> = 0x0000000000000001 (1)
00:00:01.048129 
00:00:01.048129 [/Devices/acpi/0/Config/] (level 4)
00:00:01.048130   CpuHotPlug          <integer> = 0x0000000000000000 (0)
00:00:01.048131   FdcEnabled          <integer> = 0x0000000000000000 (0)
00:00:01.048131   HostBusPciAddress   <integer> = 0x0000000000000000 (0)
00:00:01.048131   HpetEnabled         <integer> = 0x0000000000000000 (0)
00:00:01.048132   IOAPIC              <integer> = 0x0000000000000001 (1)
00:00:01.048146   IocPciAddress       <integer> = 0x0000000000010000 (65 536)
00:00:01.048146   NumCPUs             <integer> = 0x0000000000000004 (4)
00:00:01.048147   Parallel0IoPortBase <integer> = 0x0000000000000000 (0)
00:00:01.048147   Parallel0Irq        <integer> = 0x0000000000000000 (0)
00:00:01.048148   Parallel1IoPortBase <integer> = 0x0000000000000000 (0)
00:00:01.048148   Parallel1Irq        <integer> = 0x0000000000000000 (0)
00:00:01.048149   Serial0IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:01.048149   Serial0Irq          <integer> = 0x0000000000000000 (0)
00:00:01.048149   Serial1IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:01.048150   Serial1Irq          <integer> = 0x0000000000000000 (0)
00:00:01.048150   ShowCpu             <integer> = 0x0000000000000001 (1)
00:00:01.048151   ShowRtc             <integer> = 0x0000000000000000 (0)
00:00:01.048151   SmcEnabled          <integer> = 0x0000000000000000 (0)
00:00:01.048152 
00:00:01.048152 [/Devices/acpi/0/LUN#0/] (level 4)
00:00:01.048152   Driver <string>  = "ACPIHost" (cb=9)
00:00:01.048153 
00:00:01.048153 [/Devices/acpi/0/LUN#0/Config/] (level 5)
00:00:01.048154 
00:00:01.048154 [/Devices/acpi/0/LUN#1/] (level 4)
00:00:01.048169   Driver <string>  = "ACPICpu" (cb=8)
00:00:01.048170 
00:00:01.048170 [/Devices/acpi/0/LUN#1/Config/] (level 5)
00:00:01.048171 
00:00:01.048171 [/Devices/acpi/0/LUN#2/] (level 4)
00:00:01.048171   Driver <string>  = "ACPICpu" (cb=8)
00:00:01.048172 
00:00:01.048172 [/Devices/acpi/0/LUN#2/Config/] (level 5)
00:00:01.048172 
00:00:01.048173 [/Devices/acpi/0/LUN#3/] (level 4)
00:00:01.048173   Driver <string>  = "ACPICpu" (cb=8)
00:00:01.048174 
00:00:01.048174 [/Devices/acpi/0/LUN#3/Config/] (level 5)
00:00:01.048174 
00:00:01.048175 [/Devices/apic/] (level 2)
00:00:01.048175 
00:00:01.048175 [/Devices/apic/0/] (level 3)
00:00:01.048176   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048176 
00:00:01.048177 [/Devices/apic/0/Config/] (level 4)
00:00:01.048177   IOAPIC  <integer> = 0x0000000000000001 (1)
00:00:01.048178   Mode    <integer> = 0x0000000000000003 (3)
00:00:01.048178   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:01.048178 
00:00:01.048179 [/Devices/e1000/] (level 2)
00:00:01.048179 
00:00:01.048179 [/Devices/fastpipe/] (level 2)
00:00:01.048180 
00:00:01.048180 [/Devices/fastpipe/0/] (level 3)
00:00:01.048181   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048181   PCIDeviceNo   <integer> = 0x0000000000000012 (18)
00:00:01.048182   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048182   Trusted       <integer> = 0x0000000000000001 (1)
00:00:01.048182 
00:00:01.048183 [/Devices/i8254/] (level 2)
00:00:01.048184 
00:00:01.048184 [/Devices/i8254/0/] (level 3)
00:00:01.048184 
00:00:01.048184 [/Devices/i8254/0/Config/] (level 4)
00:00:01.048185 
00:00:01.048185 [/Devices/i8259/] (level 2)
00:00:01.048186 
00:00:01.048186 [/Devices/i8259/0/] (level 3)
00:00:01.048186   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048187 
00:00:01.048187 [/Devices/i8259/0/Config/] (level 4)
00:00:01.048187 
00:00:01.048188 [/Devices/ioapic/] (level 2)
00:00:01.048188 
00:00:01.048188 [/Devices/ioapic/0/] (level 3)
00:00:01.048189   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048189 
00:00:01.048189 [/Devices/ioapic/0/Config/] (level 4)
00:00:01.048190   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:01.048190 
00:00:01.048190 [/Devices/mc146818/] (level 2)
00:00:01.048191 
00:00:01.048191 [/Devices/mc146818/0/] (level 3)
00:00:01.048192 
00:00:01.048192 [/Devices/mc146818/0/Config/] (level 4)
00:00:01.048192   UseUTC <integer> = 0x0000000000000001 (1)
00:00:01.048193 
00:00:01.048193 [/Devices/parallel/] (level 2)
00:00:01.048193 
00:00:01.048193 [/Devices/pcarch/] (level 2)
00:00:01.048194 
00:00:01.048194 [/Devices/pcarch/0/] (level 3)
00:00:01.048195   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048195 
00:00:01.048195 [/Devices/pcarch/0/Config/] (level 4)
00:00:01.048196 
00:00:01.048196 [/Devices/pcbios/] (level 2)
00:00:01.048196 
00:00:01.048197 [/Devices/pcbios/0/] (level 3)
00:00:01.048197   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048197 
00:00:01.048198 [/Devices/pcbios/0/Config/] (level 4)
00:00:01.048199   APIC           <integer> = 0x0000000000000001 (1)
00:00:01.048212   BootDevice0    <string>  = "IDE" (cb=4)
00:00:01.048213   BootDevice1    <string>  = "NONE" (cb=5)
00:00:01.048213   BootDevice2    <string>  = "NONE" (cb=5)
00:00:01.048214   BootDevice3    <string>  = "NONE" (cb=5)
00:00:01.048214   FloppyDevice   <string>  = "i82078" (cb=7)
00:00:01.048214   HardDiskDevice <string>  = "piix3ide" (cb=9)
00:00:01.048215   IOAPIC         <integer> = 0x0000000000000001 (1)
00:00:01.048215   McfgBase       <integer> = 0x0000000000000000 (0)
00:00:01.048216   McfgLength     <integer> = 0x0000000000000000 (0)
00:00:01.048216   NumCPUs        <integer> = 0x0000000000000004 (4)
00:00:01.048217   PXEDebug       <integer> = 0x0000000000000000 (0)
00:00:01.048217   UUID           <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:01.048218   UuidLe         <integer> = 0x0000000000000000 (0)
00:00:01.048219 
00:00:01.048219 [/Devices/pcbios/0/Config/NetBoot/] (level 5)
00:00:01.048220 
00:00:01.048220 [/Devices/pcbios/0/Config/NetBoot/0/] (level 6)
00:00:01.048221   NIC           <integer> = 0x0000000000000000 (0)
00:00:01.048236   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048237   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:01.048237   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048238 
00:00:01.048238 [/Devices/pci/] (level 2)
00:00:01.048238 
00:00:01.048238 [/Devices/pci/0/] (level 3)
00:00:01.048239   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048240 
00:00:01.048240 [/Devices/pci/0/Config/] (level 4)
00:00:01.048240   IOAPIC <integer> = 0x0000000000000001 (1)
00:00:01.048241 
00:00:01.048241 [/Devices/pcibridge/] (level 2)
00:00:01.048241 
00:00:01.048242 [/Devices/pckbd/] (level 2)
00:00:01.048242 
00:00:01.048242 [/Devices/pckbd/0/] (level 3)
00:00:01.048243   Trusted <integer> = 0x0000000000000001 (1)
00:00:01.048243 
00:00:01.048243 [/Devices/pckbd/0/Config/] (level 4)
00:00:01.048244 
00:00:01.048244 [/Devices/pckbd/0/LUN#0/] (level 4)
00:00:01.048245   Driver <string>  = "KeyboardQueue" (cb=14)
00:00:01.048245 
00:00:01.048245 [/Devices/pckbd/0/LUN#0/AttachedDriver/] (level 5)
00:00:01.048246   Driver <string>  = "MainKeyboard" (cb=13)
00:00:01.048247 
00:00:01.048247 [/Devices/pckbd/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:01.048248   Object <integer> = 0x0000000051b77d20 (1 370 979 616)
00:00:01.048248 
00:00:01.048249 [/Devices/pckbd/0/LUN#0/Config/] (level 5)
00:00:01.048249   QueueSize <integer> = 0x0000000000000040 (64)
00:00:01.048250 
00:00:01.048250 [/Devices/pckbd/0/LUN#1/] (level 4)
00:00:01.048251   Driver <string>  = "MouseQueue" (cb=11)
00:00:01.048251 
00:00:01.048251 [/Devices/pckbd/0/LUN#1/AttachedDriver/] (level 5)
00:00:01.048252   Driver <string>  = "MainMouse" (cb=10)
00:00:01.048252 
00:00:01.048252 [/Devices/pckbd/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:01.048253   Object <integer> = 0x0000000051b783e0 (1 370 981 344)
00:00:01.048254 
00:00:01.048254 [/Devices/pckbd/0/LUN#1/Config/] (level 5)
00:00:01.048255   QueueSize <integer> = 0x0000000000000080 (128)
00:00:01.048255 
00:00:01.048255 [/Devices/pcnet/] (level 2)
00:00:01.048256 
00:00:01.048256 [/Devices/serial/] (level 2)
00:00:01.048256 
00:00:01.048257 [/Devices/virtio-net/] (level 2)
00:00:01.048257 
00:00:01.048257 [/Devices/virtio-net/0/] (level 3)
00:00:01.048258   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048258   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:01.048259   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048259   Trusted       <integer> = 0x0000000000000001 (1)
00:00:01.048260 
00:00:01.048260 [/Devices/virtio-net/0/Config/] (level 4)
00:00:01.048260   CableConnected <integer> = 0x0000000000000001 (1)
00:00:01.048261   LineSpeed      <integer> = 0x0000000000000000 (0)
00:00:01.048261   MAC            <bytes>   = "00 db 30 ef a7 99" (cb=6)
00:00:01.048262 
00:00:01.048262 [/Devices/virtio-net/0/LUN#0/] (level 4)
00:00:01.048263   Driver <string>  = "IntNet" (cb=7)
00:00:01.048263 
00:00:01.048263 [/Devices/virtio-net/0/LUN#0/Config/] (level 5)
00:00:01.048264   IfPolicyPromisc      <string>  = "deny" (cb=5)
00:00:01.048265   IgnoreConnectFailure <integer> = 0x0000000000000000 (0)
00:00:01.048265   Network              <string>  = "HostInterfaceNetworking-Realtek PCIe GBE Family Controller" (cb=59)
00:00:01.048266   Trunk                <string>  = "\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}" (cb=47)
00:00:01.048279   TrunkType            <integer> = 0x0000000000000003 (3)
00:00:01.048280 
00:00:01.048280 [/Devices/virtio-net/0/LUN#999/] (level 4)
00:00:01.048280   Driver <string>  = "MainStatus" (cb=11)
00:00:01.048281 
00:00:01.048281 [/Devices/virtio-net/0/LUN#999/Config/] (level 5)
00:00:01.048282   First   <integer> = 0x0000000000000000 (0)
00:00:01.048282   Last    <integer> = 0x0000000000000000 (0)
00:00:01.048282   papLeds <integer> = 0x0000000051b74488 (1 370 965 128)
00:00:01.048283 
00:00:01.048283 [/Devices/virtio-scsi/] (level 2)
00:00:01.048284 
00:00:01.048284 [/Devices/virtio-scsi/0/] (level 3)
00:00:01.048284   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:01.048285   PCIDeviceNo   <integer> = 0x000000000000000f (15)
00:00:01.048285   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:01.048286   Trusted       <integer> = 0x0000000000000001 (1)
00:00:01.048286 
00:00:01.048286 [/Devices/virtio-scsi/0/Config/] (level 4)
00:00:01.048287   Bootable   <integer> = 0x0000000000000001 (1)
00:00:01.048287   NumTargets <integer> = 0x0000000000000003 (3)
00:00:01.048288 
00:00:01.048303 [/Devices/virtio-scsi/0/LUN#0/] (level 4)
00:00:01.048303   Driver <string>  = "SCSI" (cb=5)
00:00:01.048304 
00:00:01.048304 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/] (level 5)
00:00:01.048305   Driver <string>  = "VD" (cb=3)
00:00:01.048305 
00:00:01.048305 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:01.048306   Format    <string>  = "VMDK" (cb=5)
00:00:01.048306   Mountable <integer> = 0x0000000000000000 (0)
00:00:01.048307   Path      <string>  = "D:\Program Files\LDPlayer9\system.vmdk" (cb=39)
00:00:01.048307   ReadOnly  <integer> = 0x0000000000000001 (1)
00:00:01.048308   Type      <string>  = "HardDisk" (cb=9)
00:00:01.048309 
00:00:01.048309 [/Devices/virtio-scsi/0/LUN#1/] (level 4)
00:00:01.048309   Driver <string>  = "SCSI" (cb=5)
00:00:01.048310 
00:00:01.048310 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/] (level 5)
00:00:01.048311   Driver <string>  = "VD" (cb=3)
00:00:01.048311 
00:00:01.048311 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:01.048312   Format    <string>  = "VMDK" (cb=5)
00:00:01.048312   Mountable <integer> = 0x0000000000000000 (0)
00:00:01.048313   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk" (cb=50)
00:00:01.048313   Type      <string>  = "HardDisk" (cb=9)
00:00:01.048313 
00:00:01.048314 [/Devices/virtio-scsi/0/LUN#2/] (level 4)
00:00:01.048314   Driver <string>  = "SCSI" (cb=5)
00:00:01.048315 
00:00:01.048315 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/] (level 5)
00:00:01.048315   Driver <string>  = "VD" (cb=3)
00:00:01.048316 
00:00:01.048316 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/Config/] (level 6)
00:00:01.048317   Format    <string>  = "VMDK" (cb=5)
00:00:01.048317   Mountable <integer> = 0x0000000000000000 (0)
00:00:01.048317   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk" (cb=52)
00:00:01.048318   Type      <string>  = "HardDisk" (cb=9)
00:00:01.048318 
00:00:01.048318 [/Devices/virtio-scsi/0/LUN#999/] (level 4)
00:00:01.048319   Driver <string>  = "MainStatus" (cb=11)
00:00:01.048319 
00:00:01.048319 [/Devices/virtio-scsi/0/LUN#999/Config/] (level 5)
00:00:01.048320   DeviceInstance        <string>  = "virtio-scsi/0" (cb=14)
00:00:01.048321   First                 <integer> = 0x0000000000000000 (0)
00:00:01.048321   Last                  <integer> = 0x0000000000000002 (2)
00:00:01.048322   pConsole              <integer> = 0x0000000051b731b0 (1 370 960 304)
00:00:01.048322   papLeds               <integer> = 0x0000000051b73c90 (1 370 963 088)
00:00:01.048323   pmapMediumAttachments <integer> = 0x0000000051b745c8 (1 370 965 448)
00:00:01.048324 
00:00:01.048324 [/EM/] (level 1)
00:00:01.048324   TripleFaultReset <integer> = 0x0000000000000000 (0)
00:00:01.048325 
00:00:01.048325 [/GIM/] (level 1)
00:00:01.048325   Provider <string>  = "KVM" (cb=4)
00:00:01.048326 
00:00:01.048326 [/HM/] (level 1)
00:00:01.048327   64bitEnabled       <integer> = 0x0000000000000001 (1)
00:00:01.048327   EnableLargePages   <integer> = 0x0000000000000001 (1)
00:00:01.048328   EnableNestedPaging <integer> = 0x0000000000000001 (1)
00:00:01.048328   EnableUX           <integer> = 0x0000000000000001 (1)
00:00:01.048329   EnableVPID         <integer> = 0x0000000000000001 (1)
00:00:01.048329   Exclusive          <integer> = 0x0000000000000000 (0)
00:00:01.048330   HMForced           <integer> = 0x0000000000000001 (1)
00:00:01.048330   IBPBOnVMEntry      <integer> = 0x0000000000000000 (0)
00:00:01.048331   IBPBOnVMExit       <integer> = 0x0000000000000000 (0)
00:00:01.048331   L1DFlushOnSched    <integer> = 0x0000000000000000 (0)
00:00:01.048332   L1DFlushOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:01.048332   MDSClearOnSched    <integer> = 0x0000000000000000 (0)
00:00:01.048332   MDSClearOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:01.048333   SpecCtrlByHost     <integer> = 0x0000000000000000 (0)
00:00:01.048333   UseNEMInstead      <integer> = 0x0000000000000000 (0)
00:00:01.048334 
00:00:01.048334 [/MM/] (level 1)
00:00:01.048348   CanUseLargerHeap <integer> = 0x0000000000000000 (0)
00:00:01.048348 
00:00:01.048348 [/NEM/] (level 1)
00:00:01.048348   Allow64BitGuests <integer> = 0x0000000000000001 (1)
00:00:01.048349 
00:00:01.048349 [/PDM/] (level 1)
00:00:01.048349 
00:00:01.048350 [/PDM/AsyncCompletion/] (level 2)
00:00:01.048350 
00:00:01.048350 [/PDM/AsyncCompletion/File/] (level 3)
00:00:01.048351 
00:00:01.048351 [/PDM/AsyncCompletion/File/BwGroups/] (level 4)
00:00:01.048352 
00:00:01.048352 [/PDM/BlkCache/] (level 2)
00:00:01.048352   CacheSize <integer> = 0x0000000000500000 (5 242 880, 5 MB)
00:00:01.048353 
00:00:01.048353 [/PDM/Devices/] (level 2)
00:00:01.048354 
00:00:01.048354 [/PDM/Devices/fastpipe/] (level 3)
00:00:01.048354   Path <string>  = "fastpipe.dll" (cb=13)
00:00:01.048355 
00:00:01.048355 [/PDM/Drivers/] (level 2)
00:00:01.048356 
00:00:01.048356 [/PDM/Drivers/VBoxC/] (level 3)
00:00:01.048372   Path <string>  = "VBoxC" (cb=6)
00:00:01.048372 
00:00:01.048373 [/PDM/NetworkShaper/] (level 2)
00:00:01.048373 
00:00:01.048373 [/PDM/NetworkShaper/BwGroups/] (level 3)
00:00:01.048374 
00:00:01.048374 [/TM/] (level 1)
00:00:01.048374   UTCOffset <integer> = 0x0000000000000000 (0)
00:00:01.048375 
00:00:01.048375 ********************* End of CFGM dump **********************
00:00:01.049608 HM: HMR3Init: VT-x w/ nested paging and unrestricted guest execution hw support
00:00:01.050221 MM: cbHyperHeap=0x840000 (8650752)
00:00:01.063716 CPUM: fXStateHostMask=0x7; initial: 0x7; host XCR0=0x1f
00:00:01.070295 CPUM: Matched host CPU INTEL 0x6/0x9e/0x9 Intel_Core7_KabyLake with CPU DB entry 'Intel Core i7-6700K' (INTEL 0x6/0x5e/0x3 Intel_Core7_Skylake)
00:00:01.071338 CPUM: MXCSR_MASK=0xffff (host: 0xffff)
00:00:01.071359 CPUM: Microcode revision 0x000000B4
00:00:01.071739 CPUM: Changing leaf 13[0]: EBX=0x440 -> 0x340, ECX=0x440 -> 0x340
00:00:01.071932 CPUM: MSR/CPUID reconciliation insert: 0x0000010b IA32_FLUSH_CMD
00:00:01.071969 CPUM: SetGuestCpuIdFeature: Enabled Speculation Control.
00:00:01.073945 PGM: Host paging mode: AMD64+NX
00:00:01.073961 PGM: PGMPool: cMaxPages=3328 (u64MaxPages=3110)
00:00:01.073969 PGM: pgmR3PoolInit: cMaxPages=0xd00 cMaxUsers=0x1a00 cMaxPhysExts=0x1a00 fCacheEnable=true 
00:00:01.111899 TM: GIP - u32Mode=3 (Invariant) u32UpdateHz=93 u32UpdateIntervalNS=10734900 enmUseTscDelta=2 (Practically Zero) fGetGipCpu=0x1b cCpus=8
00:00:01.111966 TM: GIP - u64CpuHz=3 600 001 324 (0xd693a92c)  SUPGetCpuHzFromGip => 3 600 001 324
00:00:01.111992 TM: GIP - CPU: iCpuSet=0x0 idCpu=0x0 idApic=0x0 iGipCpu=0x6 i64TSCDelta=0 enmState=3 u64CpuHz=3600001113(*) cErrors=0
00:00:01.111998 TM: GIP - CPU: iCpuSet=0x1 idCpu=0x1 idApic=0x1 iGipCpu=0x7 i64TSCDelta=0 enmState=3 u64CpuHz=3600001264(*) cErrors=0
00:00:01.112004 TM: GIP - CPU: iCpuSet=0x2 idCpu=0x2 idApic=0x2 iGipCpu=0x3 i64TSCDelta=0 enmState=3 u64CpuHz=3599955467(*) cErrors=0
00:00:01.112017 TM: GIP - CPU: iCpuSet=0x3 idCpu=0x3 idApic=0x3 iGipCpu=0x4 i64TSCDelta=0 enmState=3 u64CpuHz=3600000949(*) cErrors=0
00:00:01.112025 TM: GIP - CPU: iCpuSet=0x4 idCpu=0x4 idApic=0x4 iGipCpu=0x5 i64TSCDelta=0 enmState=3 u64CpuHz=3600001155(*) cErrors=0
00:00:01.112047 TM: GIP - CPU: iCpuSet=0x5 idCpu=0x5 idApic=0x5 iGipCpu=0x0 i64TSCDelta=0 enmState=3 u64CpuHz=3600001324(*) cErrors=0
00:00:01.112052 TM: GIP - CPU: iCpuSet=0x6 idCpu=0x6 idApic=0x6 iGipCpu=0x2 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:01.112057 TM: GIP - CPU: iCpuSet=0x7 idCpu=0x7 idApic=0x7 iGipCpu=0x1 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:01.112109 TM: cTSCTicksPerSecond=3 600 001 324 (0xd693a92c) enmTSCMode=1 (VirtTscEmulated)
00:00:01.112112 TM: TSCTiedToExecution=false TSCNotTiedToHalt=false
00:00:01.113757 EMR3Init: fIemExecutesAll=false fGuruOnTripleFault=true 
00:00:01.114681 IEM: TargetCpu=CURRENT, Microarch=Intel_Core7_KabyLake
00:00:01.115551 GIM: Using provider 'KVM' (Implementation version: 0)
00:00:01.115569 CPUM: SetGuestCpuIdFeature: Enabled Hypervisor Present bit
00:00:01.115671 AIOMgr: Default manager type is 'Async'
00:00:01.115678 AIOMgr: Default file backend is 'NonBuffered'
00:00:01.116392 BlkCache: Cache successfully initialized. Cache size is 5242880 bytes
00:00:01.116413 BlkCache: Cache commit interval is 10000 ms
00:00:01.116420 BlkCache: Cache commit threshold is 2621440 bytes
00:00:01.129823 fastpipe::VBoxDevicesRegister: u32Version=0x60001 pCallbacks->u32Version=0xffe30010
00:00:01.130711 PcBios: [SMP] BIOS with 4 CPUs
00:00:01.130940 PcBios: Using the 386+ BIOS image.
00:00:01.131621 PcBios: MPS table at 000e1300
00:00:01.135304 PcBios: fCheckShutdownStatusForSoftReset=true   fClearShutdownStatusOnHardReset=true 
00:00:01.144492 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:01.144511 SUP: seg #1: R X 0x00001000 LB 0x0001f000
00:00:01.144517 SUP: seg #2: R   0x00020000 LB 0x0000c000
00:00:01.144522 SUP: seg #3: RW  0x0002c000 LB 0x00001000
00:00:01.144527 SUP: seg #4: R   0x0002d000 LB 0x00002000
00:00:01.144533 SUP: seg #5: RW  0x0002f000 LB 0x00001000
00:00:01.144538 SUP: seg #6: R   0x00030000 LB 0x00001000
00:00:01.144543 SUP: seg #7: RWX 0x00031000 LB 0x00001000
00:00:01.144548 SUP: seg #8: R   0x00032000 LB 0x00002000
00:00:01.144603 SUP: Loaded Ld9BoxDDR0.r0 (C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:01.144612 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0=0xXXXXXXXXXXXXXXXX
00:00:01.145808 CPUM: SetGuestCpuIdFeature: Enabled xAPIC
00:00:01.145824 CPUM: SetGuestCpuIdFeature: Enabled x2APIC
00:00:01.146567 IOAPIC: Using implementation 2.0! Chipset type ICH9
00:00:01.146651 PIT: mode=3 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:01.147298 VMMDev: cbDefaultBudget: 535 351 424 (1fe8d080)
00:00:01.155360 Shared Folders service loaded
00:00:01.158048 Guest Control service loaded
00:00:01.161237 VIRTIOSCSI0: Targets=3 Bootable=true  (unimplemented) R0Enabled=true  RCEnabled=false
00:00:01.163372 DrvVD: Flushes will be ignored
00:00:01.163428 DrvVD: Async flushes will be passed to the disk
00:00:01.165053 VD: VDInit finished with VINF_SUCCESS
00:00:01.165290 VD: Opening the disk took 653665 ns
00:00:01.165825 DrvVD: Flushes will be ignored
00:00:01.165840 DrvVD: Async flushes will be passed to the disk
00:00:01.179445 VD: Opening the disk took 12687319 ns
00:00:01.179566 DrvVD: Flushes will be ignored
00:00:01.179579 DrvVD: Async flushes will be passed to the disk
00:00:01.197459 VD: Opening the disk took 16364504 ns
00:00:01.197894 IntNet#0: szNetwork={HostInterfaceNetworking-Realtek PCIe GBE Family Controller} enmTrunkType=3 szTrunk={\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}} fFlags=0x8000 cbRecv=325632 cbSend=196608 fIgnoreConnectFailure=false
00:00:01.209877 fastpipe: load host successs mod=00000000050e0390, path=C:\Program Files\ldplayer9box\libOpenglRender.dll
00:00:01.209932 fastpipe: GetFunctionAddr success mod=00000000050e0390, lpszFuncName=OnLoad
00:00:01.212422 fastpipe: load host successs mod=0000000005206c80, path=C:\Program Files\ldplayer9box\host_manager.dll
00:00:01.212473 fastpipe: GetFunctionAddr success mod=0000000005206c80, lpszFuncName=OnLoad
00:00:01.215297 PGM: The CPU physical address width is 39 bits
00:00:01.215323 PGM: PGMR3InitFinalize: 4 MB PSE mask 0000007fffffffff -> VINF_SUCCESS
00:00:01.215499 TM: TMR3InitFinalize: fTSCModeSwitchAllowed=true 
00:00:01.215788 VMM: Thread-context hooks unavailable
00:00:01.215799 VMM: RTThreadPreemptIsPending() can be trusted
00:00:01.215805 VMM: Kernel preemption is possible
00:00:01.219321 HM: fWorldSwitcher=0x0 (fIbpbOnVmExit=false fIbpbOnVmEntry=false fL1dFlushOnVmEntry=false); fL1dFlushOnSched=false fMdsClearOnVmEntry=false
00:00:01.219347 HM: Using VT-x implementation 3.0
00:00:01.219354 HM: Max resume loops                  = 8192
00:00:01.219355 HM: Host CR4                          = 0x370678
00:00:01.219356 HM: Host EFER                         = 0xd01
00:00:01.219357 HM: MSR_IA32_SMM_MONITOR_CTL          = 0x0
00:00:01.219357 HM: MSR_IA32_FEATURE_CONTROL          = 0x5
00:00:01.219358 HM:   LOCK
00:00:01.219358 HM:   VMXON
00:00:01.219358 HM: MSR_IA32_VMX_BASIC                = 0xda040000000004
00:00:01.219359 HM:   VMCS id                           = 0x4
00:00:01.219361 HM:   VMCS size                         = 1024 bytes
00:00:01.219362 HM:   VMCS physical address limit       = None
00:00:01.219362 HM:   VMCS memory type                  = Write Back (WB)
00:00:01.219363 HM:   Dual-monitor treatment support    = true 
00:00:01.219363 HM:   OUTS & INS instruction-info       = true 
00:00:01.219364 HM:   Supports true-capability MSRs     = true 
00:00:01.219364 HM:   VM-entry Xcpt error-code optional = false
00:00:01.219366 HM: MSR_IA32_VMX_PINBASED_CTLS        = 0x7f00000016
00:00:01.219366 HM:   EXT_INT_EXIT
00:00:01.219367 HM:   NMI_EXIT
00:00:01.219367 HM:   VIRTUAL_NMI
00:00:01.219367 HM:   PREEMPT_TIMER
00:00:01.219367 HM:   POSTED_INT (must be cleared)
00:00:01.219368 HM: MSR_IA32_VMX_PROCBASED_CTLS       = 0xfff9fffe0401e172
00:00:01.219368 HM:   INT_WINDOW_EXIT
00:00:01.219369 HM:   USE_TSC_OFFSETTING
00:00:01.219369 HM:   HLT_EXIT
00:00:01.219369 HM:   INVLPG_EXIT
00:00:01.219369 HM:   MWAIT_EXIT
00:00:01.219370 HM:   RDPMC_EXIT
00:00:01.219370 HM:   RDTSC_EXIT
00:00:01.219370 HM:   CR3_LOAD_EXIT (must be set)
00:00:01.219370 HM:   CR3_STORE_EXIT (must be set)
00:00:01.219371 HM:   CR8_LOAD_EXIT
00:00:01.219371 HM:   CR8_STORE_EXIT
00:00:01.219371 HM:   USE_TPR_SHADOW
00:00:01.219371 HM:   NMI_WINDOW_EXIT
00:00:01.219372 HM:   MOV_DR_EXIT
00:00:01.219372 HM:   UNCOND_IO_EXIT
00:00:01.219372 HM:   USE_IO_BITMAPS
00:00:01.219372 HM:   MONITOR_TRAP_FLAG
00:00:01.219373 HM:   USE_MSR_BITMAPS
00:00:01.219373 HM:   MONITOR_EXIT
00:00:01.219373 HM:   PAUSE_EXIT
00:00:01.219373 HM:   USE_SECONDARY_CTLS
00:00:01.219375 HM: MSR_IA32_VMX_PROCBASED_CTLS2      = 0x5ffcff00000000
00:00:01.219376 HM:   VIRT_APIC_ACCESS
00:00:01.219376 HM:   EPT
00:00:01.219376 HM:   DESC_TABLE_EXIT
00:00:01.219376 HM:   RDTSCP
00:00:01.219377 HM:   VIRT_X2APIC_MODE
00:00:01.219377 HM:   VPID
00:00:01.219377 HM:   WBINVD_EXIT
00:00:01.219377 HM:   UNRESTRICTED_GUEST
00:00:01.219378 HM:   APIC_REG_VIRT (must be cleared)
00:00:01.219378 HM:   VIRT_INT_DELIVERY (must be cleared)
00:00:01.219378 HM:   PAUSE_LOOP_EXIT
00:00:01.219378 HM:   RDRAND_EXIT
00:00:01.219379 HM:   INVPCID
00:00:01.219379 HM:   VMFUNC
00:00:01.219634 HM:   VMCS_SHADOWING
00:00:01.219635 HM:   ENCLS_EXIT
00:00:01.219635 HM:   RDSEED_EXIT
00:00:01.219635 HM:   PML
00:00:01.219636 HM:   EPT_VE
00:00:01.219636 HM:   CONCEAL_VMX_FROM_PT
00:00:01.219636 HM:   XSAVES_XRSTORS
00:00:01.219636 HM:   MODE_BASED_EPT_PERM
00:00:01.219637 HM:   SPPTP_EPT (must be cleared)
00:00:01.219637 HM:   PT_EPT (must be cleared)
00:00:01.219637 HM:   TSC_SCALING (must be cleared)
00:00:01.219637 HM:   USER_WAIT_PAUSE (must be cleared)
00:00:01.219638 HM:   ENCLV_EXIT (must be cleared)
00:00:01.219638 HM: MSR_IA32_VMX_ENTRY_CTLS           = 0x3ffff000011ff
00:00:01.219640 HM:   LOAD_DEBUG (must be set)
00:00:01.219640 HM:   IA32E_MODE_GUEST
00:00:01.219641 HM:   ENTRY_TO_SMM
00:00:01.219641 HM:   DEACTIVATE_DUAL_MON
00:00:01.219641 HM:   LOAD_PERF_MSR
00:00:01.219642 HM:   LOAD_PAT_MSR
00:00:01.219642 HM:   LOAD_EFER_MSR
00:00:01.219643 HM:   LOAD_BNDCFGS_MSR
00:00:01.219643 HM:   CONCEAL_VMX_FROM_PT
00:00:01.219643 HM:   LOAD_RTIT_CTL_MSR (must be cleared)
00:00:01.219644 HM: MSR_IA32_VMX_EXIT_CTLS            = 0x1ffffff00036dff
00:00:01.219645 HM:   SAVE_DEBUG (must be set)
00:00:01.219645 HM:   HOST_ADDR_SPACE_SIZE
00:00:01.219646 HM:   LOAD_PERF_MSR
00:00:01.219646 HM:   ACK_EXT_INT
00:00:01.219646 HM:   SAVE_PAT_MSR
00:00:01.219647 HM:   LOAD_PAT_MSR
00:00:01.219647 HM:   SAVE_EFER_MSR
00:00:01.219647 HM:   LOAD_EFER_MSR
00:00:01.219648 HM:   SAVE_PREEMPT_TIMER
00:00:01.219648 HM:   CLEAR_BNDCFGS_MSR
00:00:01.219648 HM:   CONCEAL_VMX_FROM_PT
00:00:01.219649 HM:   CLEAR_RTIT_CTL_MSR (must be cleared)
00:00:01.219649 HM: MSR_IA32_VMX_TRUE_PINBASED_CTLS   = 0x7f00000016
00:00:01.219650 HM: MSR_IA32_VMX_TRUE_PROCBASED_CTLS  = 0xfff9fffe04006172
00:00:01.219650 HM: MSR_IA32_VMX_TRUE_ENTRY_CTLS      = 0x3ffff000011fb
00:00:01.219651 HM: MSR_IA32_VMX_TRUE_EXIT_CTLS       = 0x1ffffff00036dfb
00:00:01.219658 HM: MSR_IA32_VMX_MISC                 = 0x7004c1e7
00:00:01.219661 HM:   PREEMPT_TIMER_TSC                 = 0x7
00:00:01.219662 HM:   EXIT_SAVE_EFER_LMA                = true 
00:00:01.219663 HM:   ACTIVITY_STATES                   = 0x7 ( HLT SHUTDOWN SIPI_WAIT )
00:00:01.219664 HM:   INTEL_PT                          = true 
00:00:01.219664 HM:   SMM_READ_SMBASE_MSR               = true 
00:00:01.219665 HM:   CR3_TARGET                        = 0x4
00:00:01.219665 HM:   MAX_MSR                           = 0x0 ( 512 )
00:00:01.219665 HM:   VMXOFF_BLOCK_SMI                  = true 
00:00:01.219666 HM:   VMWRITE_ALL                       = true 
00:00:01.219666 HM:   ENTRY_INJECT_SOFT_INT             = 0x1
00:00:01.219667 HM:   MSEG_ID                           = 0x0
00:00:01.219667 HM: MSR_IA32_VMX_VMCS_ENUM            = 0x2e
00:00:01.219667 HM:   HIGHEST_IDX                       = 0x17
00:00:01.219668 HM: MSR_IA32_VMX_EPT_VPID_CAP         = 0xf0106734141
00:00:01.219668 HM:   RWX_X_ONLY
00:00:01.219669 HM:   PAGE_WALK_LENGTH_4
00:00:01.219669 HM:   EMT_UC
00:00:01.219669 HM:   EMT_WB
00:00:01.219669 HM:   PDE_2M
00:00:01.219670 HM:   PDPTE_1G
00:00:01.219670 HM:   INVEPT
00:00:01.219670 HM:   EPT_ACCESS_DIRTY
00:00:01.219670 HM:   ADVEXITINFO_EPT
00:00:01.219671 HM:   INVEPT_SINGLE_CONTEXT
00:00:01.219671 HM:   INVEPT_ALL_CONTEXTS
00:00:01.219671 HM:   INVVPID
00:00:01.219671 HM:   INVVPID_INDIV_ADDR
00:00:01.219671 HM:   INVVPID_SINGLE_CONTEXT
00:00:01.219672 HM:   INVVPID_ALL_CONTEXTS
00:00:01.219672 HM:   INVVPID_SINGLE_CONTEXT_RETAIN_GLOBALS
00:00:01.219672 HM: MSR_IA32_VMX_VMFUNC               = 0x1
00:00:01.219673 HM:   EPTP_SWITCHING
00:00:01.219673 HM: MSR_IA32_VMX_CR0_FIXED0           = 0x80000021
00:00:01.219673 HM: MSR_IA32_VMX_CR0_FIXED1           = 0xffffffff
00:00:01.219674 HM: MSR_IA32_VMX_CR4_FIXED0           = 0x2000
00:00:01.219674 HM: MSR_IA32_VMX_CR4_FIXED1           = 0x3767ff
00:00:01.219675 HM: APIC-access page physaddr         = 0x000000009d0d2000
00:00:01.219676 HM: VCPU  0: MSR bitmap physaddr      = 0x00000007312d6000
00:00:01.219677 HM: VCPU  0: VMCS physaddr            = 0x0000000778dd3000
00:00:01.219678 HM: VCPU  1: MSR bitmap physaddr      = 0x00000004ac7da000
00:00:01.219678 HM: VCPU  1: VMCS physaddr            = 0x00000000a50d7000
00:00:01.219679 HM: VCPU  2: MSR bitmap physaddr      = 0x00000002b53de000
00:00:01.219679 HM: VCPU  2: VMCS physaddr            = 0x0000000014fdb000
00:00:01.219680 HM: VCPU  3: MSR bitmap physaddr      = 0x00000003828e2000
00:00:01.219681 HM: VCPU  3: VMCS physaddr            = 0x00000005d6bdf000
00:00:01.219681 HM: Guest support: 32-bit and 64-bit
00:00:01.219693 HM: Supports VMCS EFER fields         = true 
00:00:01.219694 HM: Enabled VMX
00:00:01.219697 CPUM: SetGuestCpuIdFeature: Enabled SYSENTER/EXIT
00:00:01.219698 CPUM: SetGuestCpuIdFeature: Enabled PAE
00:00:01.219698 CPUM: SetGuestCpuIdFeature: Enabled LONG MODE
00:00:01.219699 CPUM: SetGuestCpuIdFeature: Enabled SYSCALL/RET
00:00:01.219699 CPUM: SetGuestCpuIdFeature: Enabled LAHF/SAHF
00:00:01.219700 CPUM: SetGuestCpuIdFeature: Enabled NX
00:00:01.219700 HM: Enabled nested paging
00:00:01.219700 HM:   EPT flush type                  = Single context
00:00:01.219701 HM: Enabled unrestricted guest execution
00:00:01.219702 HM: Enabled large page support
00:00:01.219702 HM: Enabled VPID
00:00:01.219702 HM:   VPID flush type                 = Single context
00:00:01.219703 HM: Enabled VMX-preemption timer (cPreemptTimerShift=7)
00:00:01.219703 HM: VT-x/AMD-V init method: Local
00:00:01.219704 EM: Exit history optimizations: enabled=true  enabled-r0=true  enabled-r0-no-preemption=false
00:00:01.219739 APIC: fPostedIntrsEnabled=false fVirtApicRegsEnabled=false fSupportsTscDeadline=false
00:00:01.219749 TMR3UtcNow: nsNow=1 754 319 914 429 041 200 nsPrev=0 -> cNsDelta=1 754 319 914 429 041 200 (offLag=0 offVirtualSync=0 offVirtualSyncGivenUp=0, NowAgain=1 754 319 914 429 041 200)
00:00:01.219759 VMM: fUsePeriodicPreemptionTimers=false
00:00:01.219818 CPUM: Logical host processors: 8 present, 8 max, 8 online, online mask: 00000000000000ff
00:00:01.219819 CPUM: Physical host cores: 4
00:00:01.219819 ************************* CPUID dump ************************
00:00:01.220103          Raw Standard CPUID Leaves
00:00:01.220104      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:01.220114 Gst: 00000000/0000  00000016 756e6547 6c65746e 49656e69
00:00:01.220118 Hst:                00000016 756e6547 6c65746e 49656e69
00:00:01.220121 Gst: 00000001/0000  000906e9 00040800 d6fa2203 178bfbff
00:00:01.220124 Hst:                000906e9 05100800 7ffafbff bfebfbff
00:00:01.220127 Gst: 00000002/0000  76036301 00f0b5ff 00000000 00c30000
00:00:01.220129 Hst:                76036301 00f0b5ff 00000000 00c30000
00:00:01.220130 Gst: 00000003/0000  00000000 00000000 00000000 00000000
00:00:01.220131 Hst:                00000000 00000000 00000000 00000000
00:00:01.220132 Gst: 00000004/0000  0c000121 01c0003f 0000003f 00000000
00:00:01.220133 Hst:                1c004121 01c0003f 0000003f 00000000
00:00:01.220134 Gst: 00000004/0001  0c000122 01c0003f 0000003f 00000000
00:00:01.220135 Hst:                1c004122 01c0003f 0000003f 00000000
00:00:01.220136 Gst: 00000004/0002  0c000143 00c0003f 000003ff 00000000
00:00:01.220137 Hst:                1c004143 00c0003f 000003ff 00000000
00:00:01.220138 Gst: 00000004/0003  0c000163 03c0003f 00001fff 00000006
00:00:01.220139 Hst:                1c03c163 03c0003f 00001fff 00000006
00:00:01.220140 Gst: 00000004/0004  0c000000 00000000 00000000 00000000
00:00:01.220140 Hst:                00000000 00000000 00000000 00000000
00:00:01.220141 Gst: 00000005/0000  00000000 00000000 00000000 00000000
00:00:01.220141 Hst:                00000040 00000040 00000003 00142120
00:00:01.220142 Gst: 00000006/0000  00000000 00000000 00000000 00000000
00:00:01.220143 Hst:                000027f7 00000002 00000009 00000000
00:00:01.220144 Gst: 00000007/0000  00000000 00842421 00000000 1c000400
00:00:01.220144 Hst:                00000000 029c6fbf 00000000 9c002400
00:00:01.220145 Gst: 00000007/0001  00000000 00000000 00000000 00000000
00:00:01.220146 Hst:                00000000 00000000 00000000 00000000
00:00:01.220146 Gst: 00000008/0000  00000000 00000000 00000000 00000000
00:00:01.220147 Hst:                00000000 00000000 00000000 00000000
00:00:01.220148 Gst: 00000009/0000  00000000 00000000 00000000 00000000
00:00:01.220148 Hst:                00000000 00000000 00000000 00000000
00:00:01.220149 Gst: 0000000a/0000  00000000 00000000 00000000 00000000
00:00:01.220150 Hst:                07300404 00000000 00000000 00000603
00:00:01.220151 Gst: 0000000b/0000  00000000 00000001 00000100 00000000
00:00:01.220152 Hst:                00000001 00000002 00000100 00000005
00:00:01.220152 Gst: 0000000b/0001  00000002 00000004 00000201 00000000
00:00:01.220153 Hst:                00000004 00000008 00000201 00000005
00:00:01.220154 Gst: 0000000b/0002  00000000 00000000 00000002 00000000
00:00:01.220155 Hst:                00000000 00000000 00000002 00000005
00:00:01.220155 Gst: 0000000c/0000  00000000 00000000 00000000 00000000
00:00:01.220156 Hst:                00000000 00000000 00000000 00000000
00:00:01.220157 Gst: 0000000d/0000  00000007 00000340 00000340 00000000
00:00:01.220164 Hst:                0000001f 00000440 00000440 00000000
00:00:01.220165 Gst: 0000000d/0001  00000000 00000440 00000000 00000000
00:00:01.220166 Hst:                0000000f 00000440 00000100 00000000
00:00:01.220167 Gst: 0000000d/0002  00000100 00000240 00000000 00000000
00:00:01.220168 Hst:                00000100 00000240 00000000 00000000
00:00:01.220169 Gst: 0000000d/0003  00000000 00000000 00000000 00000000
00:00:01.220170 Hst:                00000040 000003c0 00000000 00000000
00:00:01.220170 Gst: 0000000d/0004  00000000 00000000 00000000 00000000
00:00:01.220171 Hst:                00000040 00000400 00000000 00000000
00:00:01.220172 Gst: 0000000d/0005  00000000 00000000 00000000 00000000
00:00:01.220173 Hst:                00000000 00000000 00000000 00000000
00:00:01.220174 Gst: 0000000d/0006  00000000 00000000 00000000 00000000
00:00:01.220175 Hst:                00000000 00000000 00000000 00000000
00:00:01.220176 Gst: 0000000d/0007  00000000 00000000 00000000 00000000
00:00:01.220177 Hst:                00000000 00000000 00000000 00000000
00:00:01.220178 Gst: 0000000d/0008  00000000 00000000 00000000 00000000
00:00:01.220178 Hst:                00000080 00000000 00000001 00000000
00:00:01.220180 Gst: 0000000d/0009  00000000 00000000 00000000 00000000
00:00:01.220181 Hst:                00000000 00000000 00000000 00000000
00:00:01.220197 Gst: 0000000e/0000  00000000 00000000 00000000 00000000
00:00:01.220198 Hst:                00000000 00000000 00000000 00000000
00:00:01.220199 Gst: 0000000f/0000  00000000 00000000 00000000 00000000
00:00:01.220200 Hst:                00000000 00000000 00000000 00000000
00:00:01.220201 Gst: 00000010/0000  00000000 00000000 00000000 00000000
00:00:01.220202 Hst:                00000000 00000000 00000000 00000000
00:00:01.220203 Gst: 00000011/0000  00000000 00000000 00000000 00000000
00:00:01.220204 Hst:                00000000 00000000 00000000 00000000
00:00:01.220205 Gst: 00000012/0000  00000000 00000000 00000000 00000000
00:00:01.220206 Hst:                00000000 00000000 00000000 00000000
00:00:01.220206 Gst: 00000013/0000  00000000 00000000 00000000 00000000
00:00:01.220207 Hst:                00000000 00000000 00000000 00000000
00:00:01.220208 Gst: 00000014/0000  00000000 00000000 00000000 00000000
00:00:01.220209 Hst:                00000001 0000000f 00000007 00000000
00:00:01.220210 Hst: 00000015/0000  00000002 0000012c 00000000 00000000
00:00:01.220211 Hst: 00000016/0000  00000e10 00001068 00000064 00000000
00:00:01.220212                                Name: GenuineIntel
00:00:01.220213                            Supports: 0x00000000-0x00000016
00:00:01.220218                              Family:  6 	Extended: 0 	Effective: 6
00:00:01.220219                               Model: 14 	Extended: 9 	Effective: 158
00:00:01.220220                            Stepping: 9
00:00:01.220220                                Type: 0 (primary)
00:00:01.220221                             APIC ID: 0x00
00:00:01.220221                        Logical CPUs: 4
00:00:01.220222                        CLFLUSH Size: 8
00:00:01.220222                            Brand ID: 0x00
00:00:01.220223 Features
00:00:01.220224   Mnemonic - Description                                  = guest (host)
00:00:01.220226   FPU - x87 FPU on Chip                                   = 1 (1)
00:00:01.220229   VME - Virtual 8086 Mode Enhancements                    = 1 (1)
00:00:01.220230   DE - Debugging extensions                               = 1 (1)
00:00:01.220231   PSE - Page Size Extension                               = 1 (1)
00:00:01.220233   TSC - Time Stamp Counter                                = 1 (1)
00:00:01.220234   MSR - Model Specific Registers                          = 1 (1)
00:00:01.220236   PAE - Physical Address Extension                        = 1 (1)
00:00:01.220237   MCE - Machine Check Exception                           = 1 (1)
00:00:01.220238   CX8 - CMPXCHG8B instruction                             = 1 (1)
00:00:01.220240   APIC - APIC On-Chip                                     = 1 (1)
00:00:01.220242   SEP - SYSENTER and SYSEXIT Present                      = 1 (1)
00:00:01.220245   MTRR - Memory Type Range Registers                      = 1 (1)
00:00:01.220246   PGE - PTE Global Bit                                    = 1 (1)
00:00:01.220248   MCA - Machine Check Architecture                        = 1 (1)
00:00:01.220250   CMOV - Conditional Move instructions                    = 1 (1)
00:00:01.220252   PAT - Page Attribute Table                              = 1 (1)
00:00:01.220253   PSE-36 - 36-bit Page Size Extension                     = 1 (1)
00:00:01.220254   PSN - Processor Serial Number                           = 0 (0)
00:00:01.220256   CLFSH - CLFLUSH instruction                             = 1 (1)
00:00:01.220258   DS - Debug Store                                        = 0 (1)
00:00:01.220259   ACPI - Thermal Mon. & Soft. Clock Ctrl.                 = 0 (1)
00:00:01.220260   MMX - Intel MMX Technology                              = 1 (1)
00:00:01.220262   FXSR - FXSAVE and FXRSTOR instructions                  = 1 (1)
00:00:01.220264   SSE - SSE support                                       = 1 (1)
00:00:01.220266   SSE2 - SSE2 support                                     = 1 (1)
00:00:01.220268   SS - Self Snoop                                         = 0 (1)
00:00:01.220270   HTT - Hyper-Threading Technology                        = 1 (1)
00:00:01.220274   TM - Therm. Monitor                                     = 0 (1)
00:00:01.220275   PBE - Pending Break Enabled                             = 0 (1)
00:00:01.220276   SSE3 - SSE3 support                                     = 1 (1)
00:00:01.220278   PCLMUL - PCLMULQDQ support (for AES-GCM)                = 1 (1)
00:00:01.220279   DTES64 - DS Area 64-bit Layout                          = 0 (1)
00:00:01.220280   MONITOR - MONITOR/MWAIT instructions                    = 0 (1)
00:00:01.220281   CPL-DS - CPL Qualified Debug Store                      = 0 (1)
00:00:01.220282   VMX - Virtual Machine Extensions                        = 0 (1)
00:00:01.220283   SMX - Safer Mode Extensions                             = 0 (1)
00:00:01.220284   EST - Enhanced SpeedStep Technology                     = 0 (1)
00:00:01.220285   TM2 - Terminal Monitor 2                                = 0 (1)
00:00:01.220286   SSSE3 - Supplemental Streaming SIMD Extensions 3        = 1 (1)
00:00:01.220287   CNTX-ID - L1 Context ID                                 = 0 (0)
00:00:01.220288   SDBG - Silicon Debug interface                          = 0 (1)
00:00:01.220289   FMA - Fused Multiply Add extensions                     = 0 (1)
00:00:01.220290   CX16 - CMPXCHG16B instruction                           = 1 (1)
00:00:01.220291   TPRUPDATE - xTPR Update Control                         = 0 (1)
00:00:01.220292   PDCM - Perf/Debug Capability MSR                        = 0 (1)
00:00:01.220293   PCID - Process Context Identifiers                      = 1 (1)
00:00:01.220294   DCA - Direct Cache Access                               = 0 (0)
00:00:01.220295   SSE4_1 - SSE4_1 support                                 = 1 (1)
00:00:01.220296   SSE4_2 - SSE4_2 support                                 = 1 (1)
00:00:01.220297   X2APIC - x2APIC support                                 = 1 (1)
00:00:01.220299   MOVBE - MOVBE instruction                               = 1 (1)
00:00:01.220300   POPCNT - POPCNT instruction                             = 1 (1)
00:00:01.220301   TSCDEADL - Time Stamp Counter Deadline                  = 0 (1)
00:00:01.220302   AES - AES instructions                                  = 1 (1)
00:00:01.220304   XSAVE - XSAVE instruction                               = 1 (1)
00:00:01.220306   OSXSAVE - OSXSAVE instruction                           = 0 (1)
00:00:01.220307   AVX - AVX support                                       = 1 (1)
00:00:01.220309   F16C - 16-bit floating point conversion instructions    = 0 (1)
00:00:01.220309   RDRAND - RDRAND instruction                             = 1 (1)
00:00:01.220311   HVP - Hypervisor Present (we're a guest)                = 1 (0)
00:00:01.220312 Structured Extended Feature Flags Enumeration (leaf 7):
00:00:01.220312   Mnemonic - Description                                  = guest (host)
00:00:01.220313   FSGSBASE - RDFSBASE/RDGSBASE/WRFSBASE/WRGSBASE instr.   = 1 (1)
00:00:01.220313   TSCADJUST - Supports MSR_IA32_TSC_ADJUST                = 0 (1)
00:00:01.220314   SGX - Supports Software Guard Extensions                = 0 (1)
00:00:01.220315   BMI1 - Advanced Bit Manipulation extension 1            = 0 (1)
00:00:01.220316   HLE - Hardware Lock Elision                             = 0 (1)
00:00:01.220320   AVX2 - Advanced Vector Extensions 2                     = 1 (1)
00:00:01.220321   FDP_EXCPTN_ONLY - FPU DP only updated on exceptions     = 0 (0)
00:00:01.220322   SMEP - Supervisor Mode Execution Prevention             = 0 (1)
00:00:01.220323   BMI2 - Advanced Bit Manipulation extension 2            = 0 (1)
00:00:01.220325   ERMS - Enhanced REP MOVSB/STOSB instructions            = 0 (1)
00:00:01.220326   INVPCID - INVPCID instruction                           = 1 (1)
00:00:01.220329   RTM - Restricted Transactional Memory                   = 0 (1)
00:00:01.220330   PQM - Platform Quality of Service Monitoring            = 0 (0)
00:00:01.220331   DEPFPU_CS_DS - Deprecates FPU CS, FPU DS values if set  = 1 (1)
00:00:01.220332   MPE - Intel Memory Protection Extensions                = 0 (1)
00:00:01.220333   PQE - Platform Quality of Service Enforcement           = 0 (0)
00:00:01.220333   AVX512F - AVX512 Foundation instructions                = 0 (0)
00:00:01.220334   RDSEED - RDSEED instruction                             = 1 (1)
00:00:01.220335   ADX - ADCX/ADOX instructions                            = 0 (1)
00:00:01.220336   SMAP - Supervisor Mode Access Prevention                = 0 (1)
00:00:01.220337   CLFLUSHOPT - CLFLUSHOPT (Cache Line Flush) instruction  = 1 (1)
00:00:01.220338   INTEL_PT - Intel Processor Trace                        = 0 (1)
00:00:01.220339   AVX512PF - AVX512 Prefetch instructions                 = 0 (0)
00:00:01.220340   AVX512ER - AVX512 Exponential & Reciprocal instructions = 0 (0)
00:00:01.220340   AVX512CD - AVX512 Conflict Detection instructions       = 0 (0)
00:00:01.220341   SHA - Secure Hash Algorithm extensions                  = 0 (0)
00:00:01.220342   PREFETCHWT1 - PREFETCHWT1 instruction                   = 0 (0)
00:00:01.220343   UMIP - User mode insturction prevention                 = 0 (0)
00:00:01.220344   PKU - Protection Key for Usermode pages                 = 0 (0)
00:00:01.220344   OSPKE - CR4.PKU mirror                                  = 0 (0)
00:00:01.220346   MAWAU - Value used by BNDLDX & BNDSTX                   = 0x0 (0x0)
00:00:01.220347   RDPID - Read processor ID support                       = 0 (0)
00:00:01.220348   SGX_LC - Supports SGX Launch Configuration              = 0 (0)
00:00:01.220348   MD_CLEAR - Supports MDS related buffer clearing         = 1 (1)
00:00:01.220349   13 - Reserved                                           = 0 (1)
00:00:01.220351   IBRS_IBPB - IA32_SPEC_CTRL.IBRS and IA32_PRED_CMD.IBPB  = 1 (1)
00:00:01.220351   STIBP - Supports IA32_SPEC_CTRL.STIBP                   = 1 (1)
00:00:01.220352   FLUSH_CMD - Supports IA32_FLUSH_CMD                     = 1 (1)
00:00:01.220353   ARCHCAP - Supports IA32_ARCH_CAP                        = 0 (0)
00:00:01.220354   CORECAP - Supports IA32_CORE_CAP                        = 0 (0)
00:00:01.220355   SSBD - Supports IA32_SPEC_CTRL.SSBD                     = 0 (1)
00:00:01.220356 Processor Extended State Enumeration (leaf 0xd):
00:00:01.220357    XSAVE area cur/max size by XCR0, guest: 0x340/0x340
00:00:01.220357     XSAVE area cur/max size by XCR0, host: 0x440/0x440
00:00:01.220358                    Valid XCR0 bits, guest: 0x00000000`00000007 ( x87 SSE YMM_Hi128 )
00:00:01.220360                     Valid XCR0 bits, host: 0x00000000`0000001f ( x87 SSE YMM_Hi128 BNDREGS BNDCSR )
00:00:01.220361                     XSAVE features, guest:
00:00:01.220362                      XSAVE features, host: XSAVEOPT XSAVEC XGETBC1 XSAVES
00:00:01.220367       XSAVE area cur size XCR0|XSS, guest: 0x440
00:00:01.220367        XSAVE area cur size XCR0|XSS, host: 0x440
00:00:01.220368                Valid IA32_XSS bits, guest: 0x00000000`00000000
00:00:01.220368                 Valid IA32_XSS bits, host: 0x00000100`00000000 ( 40 )
00:00:01.220369   State #2, guest: off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:01.220370   State #2, host:  off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:01.220371   State #3, host:  off=0x03c0, cb=0x0040 IA32_XSS-bit -- BNDREGS
00:00:01.220372   State #4, host:  off=0x0400, cb=0x0040 IA32_XSS-bit -- BNDCSR
00:00:01.220373   State #8, host:  off=0x0000, cb=0x0080 XCR0-bit -- 8
00:00:01.220381          Unknown CPUID Leaves
00:00:01.220381      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:01.220381 Gst: 00000014/0001  00000000 00000000 00000000 00000000
00:00:01.220382 Hst:                02490002 003f3fff 00000000 00000000
00:00:01.220383 Gst: 00000014/0002  00000000 00000000 00000000 00000000
00:00:01.220383 Hst:                00000000 00000000 00000000 00000000
00:00:01.220384 Gst: 00000015/0000  00000000 00000000 00000000 00000000
00:00:01.220384 Hst:                00000002 0000012c 00000000 00000000
00:00:01.220385 Gst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:01.220385 Hst:                00000e10 00001068 00000064 00000000
00:00:01.220386          Raw Hypervisor CPUID Leaves
00:00:01.220386      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:01.220387 Gst: 40000000/0000  40000001 4b4d564b 564b4d56 0000004d
00:00:01.220388 Hst:                00000e10 00001068 00000064 00000000
00:00:01.220389 Gst: 40000001/0000  01000089 00000000 00000000 00000000
00:00:01.220389 Hst:                00000e10 00001068 00000064 00000000
00:00:01.220390          Raw Extended CPUID Leaves
00:00:01.220390      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:01.220390 Gst: 80000000/0000  80000008 00000000 00000000 00000000
00:00:01.220391 Hst:                80000008 00000000 00000000 00000000
00:00:01.220392 Gst: 80000001/0000  00000000 00000000 00000121 28100800
00:00:01.220392 Hst:                00000000 00000000 00000121 2c100800
00:00:01.220393 Gst: 80000002/0000  65746e49 2952286c 726f4320 4d542865
00:00:01.220394 Hst:                65746e49 2952286c 726f4320 4d542865
00:00:01.220395 Gst: 80000003/0000  37692029 3037372d 50432030 20402055
00:00:01.220396 Hst:                37692029 3037372d 50432030 20402055
00:00:01.220397 Gst: 80000004/0000  30362e33 007a4847 00000000 00000000
00:00:01.220397 Hst:                30362e33 007a4847 00000000 00000000
00:00:01.220398 Gst: 80000005/0000  00000000 00000000 00000000 00000000
00:00:01.220398 Hst:                00000000 00000000 00000000 00000000
00:00:01.220399 Gst: 80000006/0000  00000000 00000000 01006040 00000000
00:00:01.220399 Hst:                00000000 00000000 01006040 00000000
00:00:01.220400 Gst: 80000007/0000  00000000 00000000 00000000 00000100
00:00:01.220400 Hst:                00000000 00000000 00000000 00000100
00:00:01.220401 Gst: 80000008/0000  00003027 00000000 00000000 00000000
00:00:01.220401 Hst:                00003027 00000000 00000000 00000000
00:00:01.220402 Ext Name:                        
00:00:01.220402 Ext Supports:                    0x80000000-0x80000008
00:00:01.220403 Family:                          0  	Extended: 0 	Effective: 0
00:00:01.220403 Model:                           0  	Extended: 0 	Effective: 0
00:00:01.220404 Stepping:                        0
00:00:01.220404 Brand ID:                        0x000
00:00:01.220404 Ext Features
00:00:01.220405   Mnemonic - Description                                  = guest (host)
00:00:01.220405   FPU - x87 FPU on Chip                                   = 0 (0)
00:00:01.220406   VME - Virtual 8086 Mode Enhancements                    = 0 (0)
00:00:01.220406   DE - Debugging extensions                               = 0 (0)
00:00:01.220407   PSE - Page Size Extension                               = 0 (0)
00:00:01.220408   TSC - Time Stamp Counter                                = 0 (0)
00:00:01.220408   MSR - K86 Model Specific Registers                      = 0 (0)
00:00:01.220409   PAE - Physical Address Extension                        = 0 (0)
00:00:01.220409   MCE - Machine Check Exception                           = 0 (0)
00:00:01.220410   CX8 - CMPXCHG8B instruction                             = 0 (0)
00:00:01.220411   APIC - APIC On-Chip                                     = 0 (0)
00:00:01.220411   SEP - SYSCALL/SYSRET                                    = 1 (1)
00:00:01.220425   MTRR - Memory Type Range Registers                      = 0 (0)
00:00:01.220426   PGE - PTE Global Bit                                    = 0 (0)
00:00:01.220426   MCA - Machine Check Architecture                        = 0 (0)
00:00:01.220427   CMOV - Conditional Move instructions                    = 0 (0)
00:00:01.220427   PAT - Page Attribute Table                              = 0 (0)
00:00:01.220428   PSE-36 - 36-bit Page Size Extension                     = 0 (0)
00:00:01.220428   NX - No-Execute/Execute-Disable                         = 1 (1)
00:00:01.220429   AXMMX - AMD Extensions to MMX instructions              = 0 (0)
00:00:01.220430   MMX - Intel MMX Technology                              = 0 (0)
00:00:01.220430   FXSR - FXSAVE and FXRSTOR Instructions                  = 0 (0)
00:00:01.220431   FFXSR - AMD fast FXSAVE and FXRSTOR instructions        = 0 (0)
00:00:01.220431   Page1GB - 1 GB large page                               = 0 (1)
00:00:01.220432   RDTSCP - RDTSCP instruction                             = 1 (1)
00:00:01.220432   LM - AMD64 Long Mode                                    = 1 (1)
00:00:01.220433   3DNOWEXT - AMD Extensions to 3DNow                      = 0 (0)
00:00:01.220434   3DNOW - AMD 3DNow                                       = 0 (0)
00:00:01.220450   LahfSahf - LAHF/SAHF support in 64-bit mode             = 1 (1)
00:00:01.220451   CmpLegacy - Core multi-processing legacy mode           = 0 (0)
00:00:01.220451   SVM - AMD Secure Virtual Machine extensions             = 0 (0)
00:00:01.220452   EXTAPIC - AMD Extended APIC registers                   = 0 (0)
00:00:01.220452   CR8L - AMD LOCK MOV CR0 means MOV CR8                   = 0 (0)
00:00:01.220453   ABM - AMD Advanced Bit Manipulation                     = 1 (1)
00:00:01.220453   SSE4A - SSE4A instructions                              = 0 (0)
00:00:01.220454   MISALIGNSSE - AMD Misaligned SSE mode                   = 0 (0)
00:00:01.220454   3DNOWPRF - AMD PREFETCH and PREFETCHW instructions      = 1 (1)
00:00:01.220455   OSVW - AMD OS Visible Workaround                        = 0 (0)
00:00:01.220455   IBS - Instruct Based Sampling                           = 0 (0)
00:00:01.220456   XOP - Extended Operation support                        = 0 (0)
00:00:01.220457   SKINIT - SKINIT, STGI, and DEV support                  = 0 (0)
00:00:01.220457   WDT - AMD Watchdog Timer support                        = 0 (0)
00:00:01.220458   LWP - Lightweight Profiling support                     = 0 (0)
00:00:01.220458   FMA4 - Four operand FMA instruction support             = 0 (0)
00:00:01.220459   NodeId - NodeId in MSR C001_100C                        = 0 (0)
00:00:01.220459   TBM - Trailing Bit Manipulation instructions            = 0 (0)
00:00:01.220460   TOPOEXT - Topology Extensions                           = 0 (0)
00:00:01.220461   PRFEXTCORE - Performance Counter Extensions support     = 0 (0)
00:00:01.220461   PRFEXTNB - NB Performance Counter Extensions support    = 0 (0)
00:00:01.220462   DATABPEXT - Data-access Breakpoint Extension            = 0 (0)
00:00:01.220462   PERFTSC - Performance Time Stamp Counter                = 0 (0)
00:00:01.220463   PCX_L2I - L2I/L3 Performance Counter Extensions         = 0 (0)
00:00:01.220463   MWAITX - MWAITX and MONITORX instructions               = 0 (0)
00:00:01.220464 Full Name:                       "Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz"
00:00:01.220465 TLB 2/4M Instr/Uni:              res0     0 entries
00:00:01.220465 TLB 2/4M Data:                   res0     0 entries
00:00:01.220466 TLB 4K Instr/Uni:                res0     0 entries
00:00:01.220466 TLB 4K Data:                     res0     0 entries
00:00:01.220467 L1 Instr Cache Line Size:        0 bytes
00:00:01.220467 L1 Instr Cache Lines Per Tag:    0
00:00:01.220468 L1 Instr Cache Associativity:    res0  
00:00:01.220468 L1 Instr Cache Size:             0 KB
00:00:01.220468 L1 Data Cache Line Size:         0 bytes
00:00:01.220469 L1 Data Cache Lines Per Tag:     0
00:00:01.220469 L1 Data Cache Associativity:     res0  
00:00:01.220469 L1 Data Cache Size:              0 KB
00:00:01.220470 L2 TLB 2/4M Instr/Uni:           off       0 entries
00:00:01.220470 L2 TLB 2/4M Data:                off       0 entries
00:00:01.220471 L2 TLB 4K Instr/Uni:             off       0 entries
00:00:01.220471 L2 TLB 4K Data:                  off       0 entries
00:00:01.220472 L2 Cache Line Size:              0 bytes
00:00:01.220472 L2 Cache Lines Per Tag:          0
00:00:01.220472 L2 Cache Associativity:          off   
00:00:01.220472 L2 Cache Size:                   0 KB
00:00:01.220473   TS - Temperature Sensor                                 = 0 (0)
00:00:01.220474   FID - Frequency ID control                              = 0 (0)
00:00:01.220474   VID - Voltage ID control                                = 0 (0)
00:00:01.220475   TscInvariant - Invariant Time Stamp Counter             = 1 (1)
00:00:01.220475   CBP - Core Performance Boost                            = 0 (0)
00:00:01.220476   EffFreqRO - Read-only Effective Frequency Interface     = 0 (0)
00:00:01.220477   ProcFdbkIf - Processor Feedback Interface               = 0 (0)
00:00:01.220477   ProcPwrRep - Core power reporting interface support     = 0 (0)
00:00:01.220478 Physical Address Width:          39 bits
00:00:01.220478 Virtual Address Width:           48 bits
00:00:01.220479 Guest Physical Address Width:    0 bits
00:00:01.220479 Physical Core Count:             1
00:00:01.220480 
00:00:01.220480 ******************** End of CPUID dump **********************
00:00:01.220481 *********************** VT-x features ***********************
00:00:01.220482 Nested hardware virtualization - VMX features
00:00:01.220483   Mnemonic - Description                                  = guest (host)
00:00:01.220483   VMX - Virtual-Machine Extensions                        = 0 (1)
00:00:01.220484   InsOutInfo - INS/OUTS instruction info.                 = 0 (1)
00:00:01.220484   ExtIntExit - External interrupt exiting                 = 0 (1)
00:00:01.220484   NmiExit - NMI exiting                                   = 0 (1)
00:00:01.220485   VirtNmi - Virtual NMIs                                  = 0 (1)
00:00:01.220485   PreemptTimer - VMX preemption timer                     = 0 (1)
00:00:01.220485   PostedInt - Posted interrupts                           = 0 (0)
00:00:01.220486   IntWindowExit - Interrupt-window exiting                = 0 (1)
00:00:01.220486   TscOffsetting - TSC offsetting                          = 0 (1)
00:00:01.220486   HltExit - HLT exiting                                   = 0 (1)
00:00:01.220487   InvlpgExit - INVLPG exiting                             = 0 (1)
00:00:01.220487   MwaitExit - MWAIT exiting                               = 0 (1)
00:00:01.220723   RdpmcExit - RDPMC exiting                               = 0 (1)
00:00:01.220724   RdtscExit - RDTSC exiting                               = 0 (1)
00:00:01.220725   Cr3LoadExit - CR3-load exiting                          = 0 (1)
00:00:01.220726   Cr3StoreExit - CR3-store exiting                        = 0 (1)
00:00:01.220726   Cr8LoadExit  - CR8-load exiting                         = 0 (1)
00:00:01.220727   Cr8StoreExit - CR8-store exiting                        = 0 (1)
00:00:01.220727   UseTprShadow - Use TPR shadow                           = 0 (1)
00:00:01.220727   NmiWindowExit - NMI-window exiting                      = 0 (1)
00:00:01.220728   MovDRxExit - Mov-DR exiting                             = 0 (1)
00:00:01.220728   UncondIoExit - Unconditional I/O exiting                = 0 (1)
00:00:01.220729   UseIoBitmaps - Use I/O bitmaps                          = 0 (1)
00:00:01.220733   MonitorTrapFlag - Monitor Trap Flag                     = 0 (1)
00:00:01.220733   UseMsrBitmaps - MSR bitmaps                             = 0 (1)
00:00:01.220733   MonitorExit - MONITOR exiting                           = 0 (1)
00:00:01.220734   PauseExit - PAUSE exiting                               = 0 (1)
00:00:01.220734   SecondaryExecCtl - Activate secondary controls          = 0 (1)
00:00:01.220734   VirtApic - Virtualize-APIC accesses                     = 0 (1)
00:00:01.220735   Ept - Extended Page Tables                              = 0 (1)
00:00:01.220735   DescTableExit - Descriptor-table exiting                = 0 (1)
00:00:01.220735   Rdtscp - Enable RDTSCP                                  = 0 (1)
00:00:01.220736   VirtX2ApicMode - Virtualize-x2APIC mode                 = 0 (1)
00:00:01.220736   Vpid - Enable VPID                                      = 0 (1)
00:00:01.220736   WbinvdExit - WBINVD exiting                             = 0 (1)
00:00:01.220737   UnrestrictedGuest - Unrestricted guest                  = 0 (1)
00:00:01.220737   ApicRegVirt - APIC-register virtualization              = 0 (0)
00:00:01.220737   VirtIntDelivery - Virtual-interrupt delivery            = 0 (0)
00:00:01.220738   PauseLoopExit - PAUSE-loop exiting                      = 0 (1)
00:00:01.220738   RdrandExit - RDRAND exiting                             = 0 (1)
00:00:01.220739   Invpcid - Enable INVPCID                                = 0 (1)
00:00:01.220740   VmFuncs - Enable VM Functions                           = 0 (1)
00:00:01.220740   VmcsShadowing - VMCS shadowing                          = 0 (1)
00:00:01.220741   RdseedExiting - RDSEED exiting                          = 0 (1)
00:00:01.220741   PML - Page-Modification Log (PML)                       = 0 (1)
00:00:01.220742   EptVe - EPT violations can cause #VE                    = 0 (1)
00:00:01.220743   XsavesXRstors - Enable XSAVES/XRSTORS                   = 0 (1)
00:00:01.220743   EntryLoadDebugCtls - Load debug controls on VM-entry    = 0 (1)
00:00:01.220744   Ia32eModeGuest - IA-32e mode guest                      = 0 (1)
00:00:01.220744   EntryLoadEferMsr - Load IA32_EFER MSR on VM-entry       = 0 (1)
00:00:01.220745   EntryLoadPatMsr - Load IA32_PAT MSR on VM-entry         = 0 (1)
00:00:01.220745   ExitSaveDebugCtls - Save debug controls on VM-exit      = 0 (1)
00:00:01.220746   HostAddrSpaceSize - Host address-space size             = 0 (1)
00:00:01.220746   ExitAckExtInt - Acknowledge interrupt on VM-exit        = 0 (1)
00:00:01.220747   ExitSavePatMsr - Save IA32_PAT MSR on VM-exit           = 0 (1)
00:00:01.220747   ExitLoadPatMsr - Load IA32_PAT MSR on VM-exit           = 0 (1)
00:00:01.220747   ExitSaveEferMsr - Save IA32_EFER MSR on VM-exit         = 0 (1)
00:00:01.220748   ExitLoadEferMsr - Load IA32_EFER MSR on VM-exit         = 0 (1)
00:00:01.220749   SavePreemptTimer - Save VMX-preemption timer            = 0 (1)
00:00:01.220749   ExitSaveEferLma - Save IA32_EFER.LMA on VM-exit         = 0 (1)
00:00:01.220750   IntelPt - Intel PT (Processor Trace) in VMX operation   = 0 (1)
00:00:01.220750   VmwriteAll - VMWRITE to any supported VMCS field        = 0 (1)
00:00:01.220750   EntryInjectSoftInt - Inject softint. with 0-len instr.  = 0 (1)
00:00:01.220751 
00:00:01.220751 ******************* End of VT-x features ********************
00:00:01.221270 VMEmt: Halt method global1 (5)
00:00:01.221385 VMEmt: HaltedGlobal1 config: cNsSpinBlockThresholdCfg=50000
00:00:01.221444 Changing the VM state from 'CREATING' to 'CREATED'
00:00:01.223271 SharedFolders host service: Adding host mapping
00:00:01.223292     Host path 'C:/Users/<USER>/Documents/leidian9/Applications', map name 'Applications', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:01.223532 SharedFolders host service: Adding host mapping
00:00:01.223550     Host path 'C:\Users\<USER>\AppData\Roaming\leidian9\android_bug', map name 'Bug', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:01.223804 SharedFolders host service: Adding host mapping
00:00:01.223818     Host path 'C:/Users/<USER>/Documents/leidian9/Misc', map name 'Misc', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:01.224051 SharedFolders host service: Adding host mapping
00:00:01.224063     Host path 'C:/Users/<USER>/Documents/leidian9/Pictures', map name 'Pictures', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:01.224308 Changing the VM state from 'CREATED' to 'POWERING_ON'
00:00:01.224485 virtioCoreVirtqAvailBufCount: Driver not ready or queue controlq not enabled
00:00:01.225398 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<0> not enabled
00:00:01.225426 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<1> not enabled
00:00:01.225741 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<2> not enabled
00:00:01.225844 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<3> not enabled
00:00:01.225892 Changing the VM state from 'POWERING_ON' to 'RUNNING'
00:00:01.225910 Console: Machine state changed to 'Running'
00:00:01.230507 VMMDev: Guest Log: BIOS: VirtualBox 6.1.34
00:00:01.230796 PCI: Setting up resources and interrupts
00:00:01.230923 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:01.253451 VMMDev: Guest Log: CPUID EDX: 0x178bfbff
00:00:01.253918 VMMDev: Guest Log: BIOS: No PCI IDE controller, not probing IDE
00:00:01.256599 VMMDev: Guest Log: BIOS: SCSI 0-ID#0: LCHS=326/255/63 0x0000000000503f2a sectors
00:00:01.257882 VMMDev: Guest Log: BIOS: SCSI 1-ID#1: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:01.258860 VMMDev: Guest Log: BIOS: SCSI 2-ID#2: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:01.261771 PIT: mode=2 count=0x48d3 (18643) - 64.00 Hz (ch=0)
00:00:01.261862 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:01.262257 VMMDev: Guest Log: BIOS: Boot : bseqnr=1, bootseq=0002
00:00:01.262845 VMMDev: Guest Log: BIOS: Booting from Hard Disk...
00:00:01.266312 VMMDev: Guest Log: int13_harddisk_ext: function 41, unmapped device for ELDL=83
00:00:01.267024 VMMDev: Guest Log: int13_harddisk: function 08, unmapped device for ELDL=83
00:00:01.485154 VMMDev: Guest Log: BIOS: KBD: unsupported int 16h function 03
00:00:01.485481 VMMDev: Guest Log: BIOS: AX=0305 BX=0000 CX=0000 DX=0000 
00:00:01.717886 GIM: KVM: VCPU  0: Enabled system-time struct. at 0x000000019ff7c000 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x69524695 uVirtNanoTS=0x1d4184ad TscKHz=3600000
00:00:01.717988 TM: Switching TSC mode from 'VirtTscEmulated' to 'RealTscOffset'
00:00:01.718809 VBoxHeadless: starting event loop
00:00:02.012501 GIM: KVM: Enabled wall-clock struct. at 0x00000000010c32a8 - u32Sec=1754319915 u32Nano=221293141 uVersion=2
00:00:02.036934 PIT: mode=2 count=0xf89 (3977) - 300.02 Hz (ch=0)
00:00:02.048243 APIC0: Switched mode to x2APIC
00:00:02.153587 PIT: mode=0 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:02.184526 APIC1: Switched mode to x2APIC
00:00:02.184538 GIM: KVM: VCPU  1: Enabled system-time struct. at 0x000000019ff7c040 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0xcd74c235 uVirtNanoTS=0x39123492 TscKHz=3600000
00:00:02.198606 APIC2: Switched mode to x2APIC
00:00:02.198611 GIM: KVM: VCPU  2: Enabled system-time struct. at 0x000000019ff7c080 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0xd07a3353 uVirtNanoTS=0x39e90cdc TscKHz=3600000
00:00:02.214245 APIC3: Switched mode to x2APIC
00:00:02.214257 GIM: KVM: VCPU  3: Enabled system-time struct. at 0x000000019ff7c0c0 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0xd3d52aaf uVirtNanoTS=0x3ad7a6e1 TscKHz=3600000
00:00:03.581450 VMMDev: Guest Additions information report: Version 6.1.36 r152435 '6.1.36'
00:00:03.581503 VMMDev: Guest Additions information report: Interface = 0x00010004 osType = 0x00053100 (Linux >= 2.6, 64-bit)
00:00:03.582067 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:03.582191 VMMDev: vmmDevReqHandler_HeartbeatConfigure: No change (fHeartbeatActive=false)
00:00:03.582214 VMMDev: Heartbeat flatline timer set to trigger after 4 000 000 000 ns
00:00:03.582494 VMMDev: Guest Log: vgdrvHeartbeatInit: Setting up heartbeat to trigger every 2000 milliseconds
00:00:03.583134 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:03.583230 VMMDev: Guest Log: vboxguest: Successfully loaded version 6.1.36 r152435
00:00:03.584147 VMMDev: Guest Log: vboxguest: misc device minor 53, IRQ 20, I/O port d020, MMIO at 00000000f0000000 (size 0x400000)
00:00:03.587129 VMMDev: Guest Log: vboxsf: g_fHostFeatures=0x8000000f g_fSfFeatures=0x1 g_uSfLastFunction=29
00:00:03.587833 VMMDev: Guest Log: vboxsf: Successfully loaded version 6.1.36 r152435 on 4.4.146 SMP preempt mod_unload modversions  (LINUX_VERSION_CODE=0x40492)
00:02:05.980314 Console: Machine state changed to 'Stopping'
00:02:05.981594 Console::powerDown(): A request to power off the VM has been issued (mMachineState=Stopping, InUninit=0)
00:02:05.981845 Changing the VM state from 'RUNNING' to 'POWERING_OFF'
00:02:05.981874 ****************** Guest state at power off for VCpu 3 ******************
00:02:05.981881 Guest CPUM (VCPU 3) state: 
00:02:05.981885 rax=0000000000000003 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:02:05.981887 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:02:05.981889 r10=00000000ffff317c r11=0000000000000001 r12=0000000000000000 r13=0000000000000000
00:02:05.981890 r14=ffff880198970000 r15=ffff880198974000
00:02:05.981891 rip=ffffffff80239162 rsp=ffff880198973ef8 rbp=ffff880198974000 iopl=0         nv up ei pl zr na pe nc
00:02:05.981893 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:02:05.981894 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.981895 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.981896 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.981896 gs={0000 base=ffff88019fd80000 limit=ffffffff flags=0001c000}
00:02:05.981897 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:02:05.981898 cr0=0000000080050033 cr2=00007ffff7ae35c0 cr3=00000000db0b0000 cr4=00000000000606b0
00:02:05.981900 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:02:05.981901 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:02:05.981902 gdtr=ffff88019fd8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:02:05.981903 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:02:05.981904 tr  ={0040 base=ffff88019fd84840 limit=00002087 flags=0000008b}
00:02:05.981905 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:02:05.981906 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:02:05.981909 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:02:05.981910 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:02:05.981911 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981913 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981914 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981916 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981917 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981918 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981919 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981921 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.981922 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'0000005f
00:02:05.981923 YMM1 =00000000'00000000'00000000'00000000'00000000'005ac6a3'00676e69'6e6e7572
00:02:05.981925 YMM2 =00000000'00000000'00000000'00000000'6c617620'79747265'706f7250'002e6f72
00:02:05.981927 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981928 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981929 YMM5 =00000000'00000000'00000000'00000000'30396562'25127465'72636573'5f656c62
00:02:05.981931 YMM6 =00000000'00000000'00000000'00000000'613a3638'653a3736'30613a62'3836613a
00:02:05.981932 YMM7 =00000000'00000000'00000000'00000000'3437623a'31353139'3a623962'353a3066
00:02:05.981934 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:02:05.981935 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:02:05.981936 YMM10=00000000'00000000'00000000'00000000'c1661174'00000000'00000000'00000000
00:02:05.981938 YMM11=00000000'00000000'00000000'00000000'ca62c1d6'ca62c1d6'ca62c1d6'ca62c1d6
00:02:05.981939 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981941 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981942 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981943 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.981945 EFER         =0000000000000d01
00:02:05.981945 PAT          =0007040600070406
00:02:05.981946 STAR         =0023001000000000
00:02:05.981947 CSTAR        =ffffffff809e4e70
00:02:05.981947 LSTAR        =ffffffff809e3690
00:02:05.981948 SFMASK       =0000000000047700
00:02:05.981948 KERNELGSBASE =0000000000000000
00:02:05.981950 ***
00:02:05.982475 VCPU[3] hardware virtualization state:
00:02:05.982476 fLocalForcedActions          = 0x0
00:02:05.982477 No/inactive hwvirt state
00:02:05.982479 ***
00:02:05.982483 Guest paging mode (VCPU #3):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:02:05.982484 Shadow paging mode (VCPU #3): EPT
00:02:05.982487 Host paging mode:             AMD64+NX
00:02:05.982488 ***
00:02:05.982488 ************** End of Guest state at power off for VCpu 3 ***************
00:02:05.982532 ****************** Guest state at power off for VCpu 2 ******************
00:02:05.982536 Guest CPUM (VCPU 2) state: 
00:02:05.982537 rax=0000000000000002 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:02:05.982539 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:02:05.982540 r10=00000000ffff315d r11=0000000000000246 r12=0000000000000000 r13=0000000000000000
00:02:05.982542 r14=ffff880198964000 r15=ffff880198968000
00:02:05.982543 rip=ffffffff80239162 rsp=ffff880198967ef8 rbp=ffff880198968000 iopl=0         nv up ei pl zr na pe nc
00:02:05.982545 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:02:05.982545 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982546 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982547 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982548 gs={0000 base=ffff88019fd00000 limit=ffffffff flags=0001c000}
00:02:05.982549 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:02:05.982550 cr0=0000000080050033 cr2=00007fff58c8b400 cr3=00000000b420f000 cr4=00000000000606b0
00:02:05.982551 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:02:05.982552 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:02:05.982553 gdtr=ffff88019fd0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:02:05.982555 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:02:05.982556 tr  ={0040 base=ffff88019fd04840 limit=00002087 flags=0000008b}
00:02:05.982557 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:02:05.982558 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:02:05.982559 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa1 MXCSR_MASK=0000ffff
00:02:05.982560 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:02:05.982561 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982562 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982564 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982565 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982567 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982568 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982570 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982571 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982572 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982573 YMM1 =00000000'00000000'00000000'00000000'3f800000'00000000'00000000'00000000
00:02:05.982575 YMM2 =00000000'00000000'00000000'00000000'00000000'00000001'00000000'00000000
00:02:05.982576 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982577 YMM4 =00000000'00000000'00000000'00000000'00000000'00000040'00000000'00000008
00:02:05.982578 YMM5 =00000000'00000000'00000000'00000000'00000000'00000030'3f800000'000003da
00:02:05.982579 YMM6 =00000000'00000000'00000000'00000000'00000000'00000040'00000000'00000038
00:02:05.982581 YMM7 =00000000'00000000'00000000'00000000'00000000'00000001'00000000'00000000
00:02:05.982582 YMM8 =00000000'00000000'00000000'00000000'00000001'00000003'00000001'00000002
00:02:05.982583 YMM9 =00000000'00000000'00000000'00000000'b879b7ff'687bf461'635f3093'bedf62e0
00:02:05.982584 YMM10=00000000'00000000'00000000'00000000'e0c5adcc'00000000'00000000'00000000
00:02:05.982586 YMM11=00000000'00000000'00000000'00000000'ca62c1d6'ca62c1d6'ca62c1d6'ca62c1d6
00:02:05.982587 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982588 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982589 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982590 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'66663238'65316534
00:02:05.982592 EFER         =0000000000000d01
00:02:05.982592 PAT          =0007040600070406
00:02:05.982592 STAR         =0023001000000000
00:02:05.982593 CSTAR        =ffffffff809e4e70
00:02:05.982594 LSTAR        =ffffffff809e3690
00:02:05.982594 SFMASK       =0000000000047700
00:02:05.982595 KERNELGSBASE =0000000000000000
00:02:05.982596 ***
00:02:05.982597 VCPU[2] hardware virtualization state:
00:02:05.982598 fLocalForcedActions          = 0x0
00:02:05.982598 No/inactive hwvirt state
00:02:05.982599 ***
00:02:05.982601 Guest paging mode (VCPU #2):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:02:05.982602 Shadow paging mode (VCPU #2): EPT
00:02:05.982602 Host paging mode:             AMD64+NX
00:02:05.982603 ***
00:02:05.982604 ************** End of Guest state at power off for VCpu 2 ***************
00:02:05.982653 ****************** Guest state at power off for VCpu 1 ******************
00:02:05.982657 Guest CPUM (VCPU 1) state: 
00:02:05.982658 rax=0000000000000001 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:02:05.982660 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:02:05.982661 r10=00000000ffff3179 r11=0000000000000001 r12=0000000000000000 r13=0000000000000000
00:02:05.982662 r14=ffff880198960000 r15=ffff880198964000
00:02:05.982663 rip=ffffffff80239162 rsp=ffff880198963ef8 rbp=ffff880198964000 iopl=0         nv up ei pl zr na pe nc
00:02:05.982665 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:02:05.982666 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982666 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982667 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982668 gs={0000 base=ffff88019fc80000 limit=ffffffff flags=0001c000}
00:02:05.982669 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:02:05.982669 cr0=0000000080050033 cr2=00007ffff723102d cr3=00000000daeec000 cr4=00000000000606b0
00:02:05.982671 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:02:05.982671 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:02:05.982672 gdtr=ffff88019fc8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:02:05.982674 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:02:05.982674 tr  ={0040 base=ffff88019fc84840 limit=00002087 flags=0000008b}
00:02:05.982675 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:02:05.982676 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:02:05.982677 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:02:05.982678 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:02:05.982679 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982681 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982682 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982683 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982684 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982685 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982686 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982687 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982688 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'000003e8'00000594
00:02:05.982689 YMM1 =00000000'00000000'00000000'00000000'00690074'00630065'006e006e'006f0043
00:02:05.982690 YMM2 =00000000'00000000'00000000'00000000'0061006c'00700073'00690044'002e0069
00:02:05.982691 YMM3 =00000000'00000000'00000000'00000000'00750067'002e0064'0069006f'00720064
00:02:05.982692 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982693 YMM5 =00000000'00000000'00000000'00000000'ffffffff'00000000'00000000'00000000
00:02:05.982694 YMM6 =00000000'00000000'00000000'00000000'ffffffff'00000000'00000000'ffffffff
00:02:05.982695 YMM7 =00000000'00000000'00000000'00000000'ffffffff'00000000'00000000'ffffffff
00:02:05.982696 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:02:05.982697 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:02:05.982697 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982698 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982699 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982700 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982701 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982701 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982702 EFER         =0000000000000d01
00:02:05.982703 PAT          =0007040600070406
00:02:05.982703 STAR         =0023001000000000
00:02:05.982704 CSTAR        =ffffffff809e4e70
00:02:05.982704 LSTAR        =ffffffff809e3690
00:02:05.982705 SFMASK       =0000000000047700
00:02:05.982705 KERNELGSBASE =0000000000000000
00:02:05.982707 ***
00:02:05.982708 VCPU[1] hardware virtualization state:
00:02:05.982708 fLocalForcedActions          = 0x0
00:02:05.982709 No/inactive hwvirt state
00:02:05.982710 ***
00:02:05.982711 Guest paging mode (VCPU #1):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:02:05.982712 Shadow paging mode (VCPU #1): EPT
00:02:05.982713 Host paging mode:             AMD64+NX
00:02:05.982714 ***
00:02:05.982714 ************** End of Guest state at power off for VCpu 1 ***************
00:02:05.982758 ****************** Guest state at power off for VCpu 0 ******************
00:02:05.982761 Guest CPUM (VCPU 0) state: 
00:02:05.982762 rax=0000000000000000 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:02:05.982764 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:02:05.982765 r10=00000000ffff3189 r11=0000000000000246 r12=0000000000000000 r13=0000000000000000
00:02:05.982766 r14=ffffffff80e00000 r15=ffffffff80e04000
00:02:05.982767 rip=ffffffff80239162 rsp=ffffffff80e03f08 rbp=ffffffff80e04000 iopl=0         nv up ei pl zr na pe nc
00:02:05.982769 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:02:05.982770 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982771 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982771 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:02:05.982772 gs={0000 base=ffff88019fc00000 limit=ffffffff flags=0001c000}
00:02:05.982773 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:02:05.982774 cr0=0000000080050033 cr2=00000000004ddb47 cr3=00000000daeec000 cr4=00000000000606b0
00:02:05.982775 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:02:05.982776 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:02:05.982777 gdtr=ffff88019fc0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:02:05.982778 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:02:05.982779 tr  ={0040 base=ffff88019fc04840 limit=00002087 flags=0000008b}
00:02:05.982780 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:02:05.982781 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:02:05.982782 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:02:05.982783 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:02:05.982784 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982785 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982787 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982788 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982789 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982790 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982791 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982792 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:02:05.982793 YMM0 =00000000'00000000'00000000'00000000'00000000'32242e80'00000000'6890cca8
00:02:05.982795 YMM1 =00000000'00000000'00000000'00000000'403dffff'ffff1194'd7fc0000'00000000
00:02:05.982796 YMM2 =00000000'00000000'00000000'00000000'00000000'0000003c'00000000'00000072
00:02:05.982797 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982798 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000006
00:02:05.982798 YMM5 =00000000'00000000'00000000'00000000'3d20636e'7566202c'73676966'6e6f4372
00:02:05.982800 YMM6 =00000000'00000000'00000000'00000000'77647261'683a3a64'696f7264'6e612620
00:02:05.982801 YMM7 =00000000'00000000'00000000'00000000'65726f74'73676966'6e6f633a'3a657261
00:02:05.982802 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:02:05.982803 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:02:05.982804 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982804 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982805 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982806 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982807 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982808 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:02:05.982809 EFER         =0000000000000d01
00:02:05.982809 PAT          =0007040600070406
00:02:05.982810 STAR         =0023001000000000
00:02:05.982810 CSTAR        =ffffffff809e4e70
00:02:05.982811 LSTAR        =ffffffff809e3690
00:02:05.982811 SFMASK       =0000000000047700
00:02:05.982812 KERNELGSBASE =0000000000000000
00:02:05.982813 ***
00:02:05.982814 VCPU[0] hardware virtualization state:
00:02:05.982815 fLocalForcedActions          = 0x0
00:02:05.982815 No/inactive hwvirt state
00:02:05.982816 ***
00:02:05.982818 Guest paging mode (VCPU #0):  AMD64+NX (changed 3700 times), A20 enabled (changed 2 times)
00:02:05.982819 Shadow paging mode (VCPU #0): EPT
00:02:05.982819 Host paging mode:             AMD64+NX
00:02:05.982820 ***
00:02:05.982821 Active Timers (pVM=00000000529b0000)
00:02:05.982822 pTimerR3         offNext  offPrev  offSched Clock               Time             Expire HzHint State                     Description
00:02:05.982824 0000000002cb2650 ffffff80 00000000 00000000 Real           109921918          109921940      0 2-ACTIVE                  EMT Yielder
00:02:05.982826 0000000002cb25d0 00000000 00000080 00000000 Real           109921918          109922612      0 2-ACTIVE                  CPU Load Timer
00:02:05.982828 0000000002cb1d10 00000000 00000000 00000000 Virt        124756345150       128368553213      0 2-ACTIVE                  Heartbeat flatlined
00:02:05.982831 0000000002cad6e0 ffffff80 00000000 00000000 VrSy        124755992562       124763526992     79 2-ACTIVE                  APIC Timer 1
00:02:05.982835 0000000002cad660 000003f0 00000080 00000000 VrSy        124755996242       124766654998     63 2-ACTIVE                  APIC Timer 0
00:02:05.982837 0000000002cada50 fffffd90 fffffc10 00000000 VrSy        124755998729       124990000000      0 2-ACTIVE                  MC146818 RTC (CMOS) - Second
00:02:05.982856 0000000002cad7e0 00004b70 00000270 00000000 VrSy        124756017858       125029768597      2 2-ACTIVE                  APIC Timer 3
00:02:05.982862 0000000002cb2350 00000000 ffffb490 00000000 VrSy        124756023496       599932015941      0 2-ACTIVE                  ACPI PM Timer
00:02:05.982866 ***
00:02:05.982868 Guest GDT (GCAddr=ffff88019fc0c000 limit=7f):
00:02:05.982874 0008 - 0000ffff 00cf9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 32-bit 
00:02:05.982875 0010 - 0000ffff 00af9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 16-bit 
00:02:05.982877 0018 - 0000ffff 00cf9300 - base=00000000 limit=ffffffff dpl=0 DataRW Accessed Present Page 32-bit 
00:02:05.982877 0020 - 0000ffff 00cffb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 32-bit 
00:02:05.982878 0028 - 0000ffff 00cff300 - base=00000000 limit=ffffffff dpl=3 DataRW Accessed Present Page 32-bit 
00:02:05.982879 0030 - 0000ffff 00affb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 16-bit 
00:02:05.982881 0040 - 48402087 9f008bc0 - base=9fc04840 limit=00002087 dpl=0 TSS32Busy Present 16-bit 
00:02:05.982883 0078 - 00000000 0040f500 - base=00000000 limit=00000000 dpl=3 DataDownRO Accessed Present 32-bit 
00:02:05.982884 ************** End of Guest state at power off ***************
00:02:06.090699 PDMR3PowerOff: after   107 ms, 1 loops: 1 async tasks - virtio-scsi/0
00:02:06.189857 PDMR3PowerOff: 206 942 890 ns run time
00:02:06.189891 Changing the VM state from 'POWERING_OFF' to 'OFF'
00:02:06.191512 Changing the VM state from 'OFF' to 'DESTROYING'
00:02:06.191589 ************************* Statistics *************************
00:02:06.191608 /CPUM/MSR-Totals/Reads              68435 times
00:02:06.191619 /CPUM/MSR-Totals/ReadsRaisingGP         0 times
00:02:06.191627 /CPUM/MSR-Totals/ReadsUnknown           0 times
00:02:06.191637 /CPUM/MSR-Totals/Writes            987956 times
00:02:06.191647 /CPUM/MSR-Totals/WritesRaisingGP        0 times
00:02:06.191656 /CPUM/MSR-Totals/WritesToIgnoredBits        4 times
00:02:06.191666 /CPUM/MSR-Totals/WritesUnknown          0 times
00:02:06.191674 /Devices/8237A/DmaRun                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.191683 /Devices/VMMDev/BalloonChunks           0 count
00:02:06.191691 /Devices/VMMDev/FastIrqAckR3            0 count
00:02:06.191701 /Devices/VMMDev/FastIrqAckRZ           76 count
00:02:06.191707 /Devices/VMMDev/HGCM-Guest/BudgetAvailable 535351424 bytes
00:02:06.191717 /Devices/VMMDev/HGCM-Guest/BudgetConfig 535351424 bytes
00:02:06.191729 /Devices/VMMDev/HGCM-Guest/cTotalMessages        0 count
00:02:06.191742 /Devices/VMMDev/HGCM-Guest/cbHeapTotal        0 bytes
00:02:06.191753 /Devices/VMMDev/HGCM-Legacy/BudgetAvailable 535351424 bytes
00:02:06.191766 /Devices/VMMDev/HGCM-Legacy/BudgetConfig 535351424 bytes
00:02:06.191776 /Devices/VMMDev/HGCM-Legacy/cTotalMessages        0 count
00:02:06.191784 /Devices/VMMDev/HGCM-Legacy/cbHeapTotal        0 bytes
00:02:06.191793 /Devices/VMMDev/HGCM-OtherDrv/BudgetAvailable 535351424 bytes
00:02:06.191802 /Devices/VMMDev/HGCM-OtherDrv/BudgetConfig 535351424 bytes
00:02:06.191810 /Devices/VMMDev/HGCM-OtherDrv/cTotalMessages       77 count
00:02:06.191818 /Devices/VMMDev/HGCM-OtherDrv/cbHeapTotal   309433 bytes
00:02:06.191825 /Devices/VMMDev/HGCM-Reserved1/BudgetAvailable 535351424 bytes
00:02:06.191833 /Devices/VMMDev/HGCM-Reserved1/BudgetConfig 535351424 bytes
00:02:06.191841 /Devices/VMMDev/HGCM-Reserved1/cTotalMessages        0 count
00:02:06.191849 /Devices/VMMDev/HGCM-Reserved1/cbHeapTotal        0 bytes
00:02:06.191856 /Devices/VMMDev/HGCM-Root/BudgetAvailable 535351424 bytes
00:02:06.191864 /Devices/VMMDev/HGCM-Root/BudgetConfig 535351424 bytes
00:02:06.191872 /Devices/VMMDev/HGCM-Root/cTotalMessages        0 count
00:02:06.191880 /Devices/VMMDev/HGCM-Root/cbHeapTotal        0 bytes
00:02:06.191888 /Devices/VMMDev/HGCM-System/BudgetAvailable 535351424 bytes
00:02:06.191895 /Devices/VMMDev/HGCM-System/BudgetConfig 535351424 bytes
00:02:06.191903 /Devices/VMMDev/HGCM-System/cTotalMessages        0 count
00:02:06.191910 /Devices/VMMDev/HGCM-System/cbHeapTotal        0 bytes
00:02:06.191918 /Devices/VMMDev/HGCM-User/BudgetAvailable 535351424 bytes
00:02:06.191926 /Devices/VMMDev/HGCM-User/BudgetConfig 535351424 bytes
00:02:06.191933 /Devices/VMMDev/HGCM-User/cTotalMessages        0 count
00:02:06.191946 /Devices/VMMDev/HGCM-User/cbHeapTotal        0 bytes
00:02:06.191956 /Devices/VMMDev/HGCM-VBoxGuest/BudgetAvailable 535351424 bytes
00:02:06.191964 /Devices/VMMDev/HGCM-VBoxGuest/BudgetConfig 535351424 bytes
00:02:06.191972 /Devices/VMMDev/HGCM-VBoxGuest/cTotalMessages        0 count
00:02:06.191979 /Devices/VMMDev/HGCM-VBoxGuest/cbHeapTotal        0 bytes
00:02:06.191987 /Devices/VMMDev/LargeReqBufAllocs        0 count
00:02:06.191994 /Devices/VMMDev/SlowIrqAck              0 count
00:02:06.192002 /Devices/mc146818/Irq                   0 times
00:02:06.192010 /Devices/mc146818/TimerCB               0 times
00:02:06.192017 /Devices/virtio-net#0/Interrupts/Raised      163 times
00:02:06.192025 /Devices/virtio-net#0/Interrupts/Skipped      498 times
00:02:06.192032 /Devices/virtio-net#0/Packets/ReceiveGSO        0 count
00:02:06.192040 /Devices/virtio-net#0/Packets/Transmit      472 count
00:02:06.192047 /Devices/virtio-net#0/Packets/Transmit-Csum      452 count
00:02:06.192055 /Devices/virtio-net#0/Packets/Transmit-Gso        0 count
00:02:06.192062 /Devices/virtio-net#0/ReceiveBytes    30102 bytes
00:02:06.192070 /Devices/virtio-net#0/TransmitBytes    42229 bytes
00:02:06.192077 /Devices/virtio-scsi#0/DescChainsAllocated    50601 count
00:02:06.192085 /Devices/virtio-scsi#0/DescChainsFreed    50601 count
00:02:06.192092 /Devices/virtio-scsi#0/DescChainsSegsIn   370986 count
00:02:06.192100 /Devices/virtio-scsi#0/DescChainsSegsOut    87635 count
00:02:06.192108 /Drivers/IntNet-0/BadFrames             0 count
00:02:06.192115 /Drivers/IntNet-0/Bytes/Received    54182 bytes
00:02:06.192123 /Drivers/IntNet-0/Bytes/Sent        42229 bytes
00:02:06.192130 /Drivers/IntNet-0/Overflows/Recv        0 count
00:02:06.192138 /Drivers/IntNet-0/Overflows/Sent        0 count
00:02:06.192145 /Drivers/IntNet-0/Packets/Lost          0 count
00:02:06.192153 /Drivers/IntNet-0/Packets/Received      302 count
00:02:06.192161 /Drivers/IntNet-0/Packets/Received-Gso        0 count
00:02:06.192168 /Drivers/IntNet-0/Packets/Sent        472 count
00:02:06.192176 /Drivers/IntNet-0/Packets/Sent-Gso        0 count
00:02:06.192184 /Drivers/IntNet-0/Packets/Sent-R0        0 count
00:02:06.192192 /Drivers/IntNet-0/Recv1                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192201 /Drivers/IntNet-0/Recv2                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192210 /Drivers/IntNet-0/Reserved              0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192218 /Drivers/IntNet-0/Send1            106130 ticks/call (    50093466 ticks,     472 times, max    769506, min   13224)
00:02:06.192227 /Drivers/IntNet-0/Send2            102314 ticks/call (    48292362 ticks,     472 times, max    759674, min   12572)
00:02:06.192236 /Drivers/IntNet-0/XmitProcessRing        0 count
00:02:06.192243 /Drivers/IntNet-0/XmitWakeup-R0         0 count
00:02:06.192251 /Drivers/IntNet-0/XmitWakeup-R3         0 count
00:02:06.192258 /Drivers/IntNet-0/YieldNok              0 count
00:02:06.192266 /Drivers/IntNet-0/YieldOk               0 count
00:02:06.192278 /EM/CPU0/ExitHashing/Used               0 times
00:02:06.192286 /EM/CPU0/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192296 /EM/CPU0/ExitOpt/ExecInstructions        0 times
00:02:06.192303 /EM/CPU0/ExitOpt/ExecSavedExit          0 times
00:02:06.192311 /EM/CPU0/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192319 /EM/CPU0/ExitOpt/ProbeInstructions        0 times
00:02:06.192326 /EM/CPU0/ExitOpt/ProbedExecWithMax        0 times
00:02:06.192334 /EM/CPU0/ExitOpt/ProbedNormal           0 times
00:02:06.192342 /EM/CPU0/ExitOpt/ProbedToRing3          0 times
00:02:06.192352 /EM/CPU1/ExitHashing/Used               0 times
00:02:06.192360 /EM/CPU1/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192370 /EM/CPU1/ExitOpt/ExecInstructions        0 times
00:02:06.192379 /EM/CPU1/ExitOpt/ExecSavedExit          0 times
00:02:06.192386 /EM/CPU1/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192395 /EM/CPU1/ExitOpt/ProbeInstructions        0 times
00:02:06.192402 /EM/CPU1/ExitOpt/ProbedExecWithMax        0 times
00:02:06.192409 /EM/CPU1/ExitOpt/ProbedNormal           0 times
00:02:06.192417 /EM/CPU1/ExitOpt/ProbedToRing3          0 times
00:02:06.192428 /EM/CPU2/ExitHashing/Used               0 times
00:02:06.192436 /EM/CPU2/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192445 /EM/CPU2/ExitOpt/ExecInstructions        0 times
00:02:06.192452 /EM/CPU2/ExitOpt/ExecSavedExit          0 times
00:02:06.192459 /EM/CPU2/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192467 /EM/CPU2/ExitOpt/ProbeInstructions        0 times
00:02:06.192475 /EM/CPU2/ExitOpt/ProbedExecWithMax        0 times
00:02:06.192482 /EM/CPU2/ExitOpt/ProbedNormal           0 times
00:02:06.192489 /EM/CPU2/ExitOpt/ProbedToRing3          0 times
00:02:06.192499 /EM/CPU3/ExitHashing/Used               0 times
00:02:06.192507 /EM/CPU3/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192515 /EM/CPU3/ExitOpt/ExecInstructions        0 times
00:02:06.192523 /EM/CPU3/ExitOpt/ExecSavedExit          0 times
00:02:06.192529 /EM/CPU3/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.192537 /EM/CPU3/ExitOpt/ProbeInstructions        0 times
00:02:06.192544 /EM/CPU3/ExitOpt/ProbedExecWithMax        0 times
00:02:06.192551 /EM/CPU3/ExitOpt/ProbedNormal           0 times
00:02:06.192558 /EM/CPU3/ExitOpt/ProbedToRing3          0 times
00:02:06.192565 /GMM/ChunkTlbHits                    6075 times
00:02:06.192573 /GMM/ChunkTlbMisses                   763 times
00:02:06.192585 /GMM/VM/Allocated/cBasePages       357982 pages
00:02:06.192593 /GMM/VM/Allocated/cFixedPages           0 pages
00:02:06.192601 /GMM/VM/Allocated/cShadowPages          0 pages
00:02:06.192608 /GMM/VM/Reserved/cBasePages       1572958 pages
00:02:06.192616 /GMM/VM/Reserved/cFixedPages         5140 pages
00:02:06.192624 /GMM/VM/Reserved/cShadowPages           1 pages
00:02:06.192632 /GMM/VM/cBalloonedPages                 0 pages
00:02:06.192639 /GMM/VM/cMaxBalloonedPages              0 pages
00:02:06.192646 /GMM/VM/cPrivatePages              357982 pages
00:02:06.192653 /GMM/VM/cReqActuallyBalloonedPages        0 pages
00:02:06.192660 /GMM/VM/cReqBalloonedPages              0 pages
00:02:06.192667 /GMM/VM/cReqDeflatePages                0 pages
00:02:06.192675 /GMM/VM/cShareableModules               0 count
00:02:06.192682 /GMM/VM/cSharedPages                    0 pages
00:02:06.192689 /GMM/VM/enmPolicy                       1 
00:02:06.192696 /GMM/VM/enmPriority                     2 
00:02:06.192703 /GMM/VM/fBallooningEnabled       false    
00:02:06.192711 /GMM/VM/fMayAllocate             true     
00:02:06.192718 /GMM/VM/fSharedPagingEnabled     false    
00:02:06.192726 /GMM/cAllocatedPages               357982 pages
00:02:06.192734 /GMM/cBalloonedPages                    0 pages
00:02:06.192741 /GMM/cChunks                          700 count
00:02:06.192749 /GMM/cDuplicatePages                    0 pages
00:02:06.192757 /GMM/cFreedChunks                       0 count
00:02:06.192764 /GMM/cLeftBehindSharedPages             0 pages
00:02:06.192772 /GMM/cMaxPages                   4294967295 pages
00:02:06.192780 /GMM/cOverCommittedPages                0 pages
00:02:06.192787 /GMM/cReservedPages               1578099 pages
00:02:06.192796 /GMM/cShareableModules                  0 count
00:02:06.192803 /GMM/cSharedPages                       0 pages
00:02:06.192813 /GMM/idFreeGeneration            4611686018427387775 
00:02:06.192978 /GVMM/EMTs                              4 calls
00:02:06.192989 /GVMM/HostCPUs                          8 calls
00:02:06.192997 /GVMM/HostCpus/0                        0 
00:02:06.193005 /GVMM/HostCpus/0/CurTimerHz             0 Hz
00:02:06.193013 /GVMM/HostCpus/0/DesiredHz              0 Hz
00:02:06.193020 /GVMM/HostCpus/0/PPTChanges             0 times
00:02:06.193027 /GVMM/HostCpus/0/PPTStarts              0 times
00:02:06.193035 /GVMM/HostCpus/0/idxCpuSet              0 
00:02:06.193042 /GVMM/HostCpus/1                        1 
00:02:06.193050 /GVMM/HostCpus/1/CurTimerHz             0 Hz
00:02:06.193057 /GVMM/HostCpus/1/DesiredHz              0 Hz
00:02:06.193065 /GVMM/HostCpus/1/PPTChanges             0 times
00:02:06.193072 /GVMM/HostCpus/1/PPTStarts              0 times
00:02:06.193080 /GVMM/HostCpus/1/idxCpuSet              1 
00:02:06.193087 /GVMM/HostCpus/2                        2 
00:02:06.193096 /GVMM/HostCpus/2/CurTimerHz             0 Hz
00:02:06.193103 /GVMM/HostCpus/2/DesiredHz              0 Hz
00:02:06.193111 /GVMM/HostCpus/2/PPTChanges             0 times
00:02:06.193119 /GVMM/HostCpus/2/PPTStarts              0 times
00:02:06.193126 /GVMM/HostCpus/2/idxCpuSet              2 
00:02:06.193133 /GVMM/HostCpus/3                        3 
00:02:06.193141 /GVMM/HostCpus/3/CurTimerHz             0 Hz
00:02:06.193148 /GVMM/HostCpus/3/DesiredHz              0 Hz
00:02:06.193156 /GVMM/HostCpus/3/PPTChanges             0 times
00:02:06.193164 /GVMM/HostCpus/3/PPTStarts              0 times
00:02:06.193172 /GVMM/HostCpus/3/idxCpuSet              3 
00:02:06.193180 /GVMM/HostCpus/4                        4 
00:02:06.193192 /GVMM/HostCpus/4/CurTimerHz             0 Hz
00:02:06.193198 /GVMM/HostCpus/4/DesiredHz              0 Hz
00:02:06.193203 /GVMM/HostCpus/4/PPTChanges             0 times
00:02:06.193208 /GVMM/HostCpus/4/PPTStarts              0 times
00:02:06.193213 /GVMM/HostCpus/4/idxCpuSet              4 
00:02:06.193218 /GVMM/HostCpus/5                        5 
00:02:06.193223 /GVMM/HostCpus/5/CurTimerHz             0 Hz
00:02:06.193228 /GVMM/HostCpus/5/DesiredHz              0 Hz
00:02:06.193233 /GVMM/HostCpus/5/PPTChanges             0 times
00:02:06.193238 /GVMM/HostCpus/5/PPTStarts              0 times
00:02:06.193243 /GVMM/HostCpus/5/idxCpuSet              5 
00:02:06.193248 /GVMM/HostCpus/6                        6 
00:02:06.193253 /GVMM/HostCpus/6/CurTimerHz             0 Hz
00:02:06.193258 /GVMM/HostCpus/6/DesiredHz              0 Hz
00:02:06.193263 /GVMM/HostCpus/6/PPTChanges             0 times
00:02:06.193268 /GVMM/HostCpus/6/PPTStarts              0 times
00:02:06.193273 /GVMM/HostCpus/6/idxCpuSet              6 
00:02:06.193278 /GVMM/HostCpus/7                        7 
00:02:06.193283 /GVMM/HostCpus/7/CurTimerHz             0 Hz
00:02:06.193288 /GVMM/HostCpus/7/DesiredHz              0 Hz
00:02:06.193293 /GVMM/HostCpus/7/PPTChanges             0 times
00:02:06.193298 /GVMM/HostCpus/7/PPTStarts              0 times
00:02:06.193303 /GVMM/HostCpus/7/idxCpuSet              7 
00:02:06.193308 /GVMM/Sum/HaltBlocking             202955 calls
00:02:06.193313 /GVMM/Sum/HaltCalls                202916 calls
00:02:06.193318 /GVMM/Sum/HaltNotBlocking               2 calls
00:02:06.193323 /GVMM/Sum/HaltTimeouts              19942 calls
00:02:06.193328 /GVMM/Sum/HaltWakeUps                   0 calls
00:02:06.193333 /GVMM/Sum/PokeCalls                191125 calls
00:02:06.193338 /GVMM/Sum/PokeNotBusy                2698 calls
00:02:06.193344 /GVMM/Sum/PollCalls                   236 calls
00:02:06.193349 /GVMM/Sum/PollHalts                     0 calls
00:02:06.193354 /GVMM/Sum/PollWakeUps                   0 calls
00:02:06.193359 /GVMM/Sum/WakeUpCalls              183451 calls
00:02:06.193364 /GVMM/Sum/WakeUpNotHalted            2384 calls
00:02:06.193369 /GVMM/Sum/WakeUpWakeUps                 0 calls
00:02:06.193374 /GVMM/VM/HaltBlocking              202955 calls
00:02:06.193381 /GVMM/VM/HaltCalls                 202916 calls
00:02:06.193387 /GVMM/VM/HaltNotBlocking                2 calls
00:02:06.193392 /GVMM/VM/HaltTimeouts               19942 calls
00:02:06.193397 /GVMM/VM/HaltWakeUps                    0 calls
00:02:06.193402 /GVMM/VM/PokeCalls                 191125 calls
00:02:06.193408 /GVMM/VM/PokeNotBusy                 2698 calls
00:02:06.193413 /GVMM/VM/PollCalls                    236 calls
00:02:06.193418 /GVMM/VM/PollHalts                      0 calls
00:02:06.193423 /GVMM/VM/PollWakeUps                    0 calls
00:02:06.193428 /GVMM/VM/WakeUpCalls               183451 calls
00:02:06.193433 /GVMM/VM/WakeUpNotHalted             2384 calls
00:02:06.193438 /GVMM/VM/WakeUpWakeUps                  0 calls
00:02:06.193443 /GVMM/VMs                               1 calls
00:02:06.193449 /HGCM/FailedPageListLocking             0 count
00:02:06.193454 /HGCM/LargeCmdAllocs                    4 count
00:02:06.193459 /HGCM/MsgArrival                    74737 ticks/call (     5530587 ticks,      74 times, max    634708, min   18054)
00:02:06.193465 /HGCM/MsgCompletion                 38363 ticks/call (     2953980 ticks,      77 times, max    106032, min    2550)
00:02:06.193471 /HGCM/MsgTotal                     541727 ticks/call (    40087830 ticks,      74 times, max   1752751, min   37985)
00:02:06.193477 /HM/CPU0/Exit/HostNmiInGC               0 times
00:02:06.193483 /HM/CPU0/Exit/HostNmiInGCIpi            0 times
00:02:06.193488 /HM/CPU0/Exit/Trap/Gst/#AC              0 times
00:02:06.193493 /HM/CPU0/Exit/Trap/Gst/#AC-split-lock        0 times
00:02:06.193498 /HM/CPU0/Switch/Preempting              0 times
00:02:06.193503 /HM/CPU1/Exit/HostNmiInGC               0 times
00:02:06.193508 /HM/CPU1/Exit/HostNmiInGCIpi            0 times
00:02:06.193513 /HM/CPU1/Exit/Trap/Gst/#AC              0 times
00:02:06.193518 /HM/CPU1/Exit/Trap/Gst/#AC-split-lock        0 times
00:02:06.193523 /HM/CPU1/Switch/Preempting              0 times
00:02:06.193528 /HM/CPU2/Exit/HostNmiInGC               0 times
00:02:06.193533 /HM/CPU2/Exit/HostNmiInGCIpi            0 times
00:02:06.193538 /HM/CPU2/Exit/Trap/Gst/#AC              0 times
00:02:06.193543 /HM/CPU2/Exit/Trap/Gst/#AC-split-lock        0 times
00:02:06.193548 /HM/CPU2/Switch/Preempting              0 times
00:02:06.193553 /HM/CPU3/Exit/HostNmiInGC               0 times
00:02:06.193558 /HM/CPU3/Exit/HostNmiInGCIpi            0 times
00:02:06.193563 /HM/CPU3/Exit/Trap/Gst/#AC              0 times
00:02:06.193568 /HM/CPU3/Exit/Trap/Gst/#AC-split-lock        0 times
00:02:06.193573 /HM/CPU3/Switch/Preempting              0 times
00:02:06.193578 /IEM/CPU0/CodeTlb-Misses                0 count
00:02:06.193583 /IEM/CPU0/CodeTlb-PhysRev        ffffffffffff9c00 
00:02:06.193589 /IEM/CPU0/CodeTlb-Revision       fffff38000000000 
00:02:06.193594 /IEM/CPU0/CodeTlb-SlowReads             0 
00:02:06.193599 /IEM/CPU0/DataTlb-Misses                0 count
00:02:06.193604 /IEM/CPU0/DataTlb-PhysRev        ffffffffffff9c00 
00:02:06.193609 /IEM/CPU0/DataTlb-Revision       fffff38000000000 
00:02:06.193614 /IEM/CPU0/cInstructions            217051 count
00:02:06.193620 /IEM/CPU0/cLongJumps                    6 bytes
00:02:06.193625 /IEM/CPU0/cPendingCommit                0 bytes
00:02:06.193630 /IEM/CPU0/cPotentialExits          219938 count
00:02:06.193635 /IEM/CPU0/cRetAspectNotImplemented        0 count
00:02:06.193640 /IEM/CPU0/cRetErrStatuses               0 count
00:02:06.193645 /IEM/CPU0/cRetInfStatuses             108 count
00:02:06.193650 /IEM/CPU0/cRetInstrNotImplemented        0 count
00:02:06.193655 /IEM/CPU0/cbWritten                439990 bytes
00:02:06.193660 /IEM/CPU1/CodeTlb-Misses                0 count
00:02:06.193665 /IEM/CPU1/CodeTlb-PhysRev        ffffffffffff9c00 
00:02:06.193670 /IEM/CPU1/CodeTlb-Revision       fffff38000000000 
00:02:06.193675 /IEM/CPU1/CodeTlb-SlowReads             0 
00:02:06.193680 /IEM/CPU1/DataTlb-Misses                0 count
00:02:06.193686 /IEM/CPU1/DataTlb-PhysRev        ffffffffffff9c00 
00:02:06.193692 /IEM/CPU1/DataTlb-Revision       fffff38000000000 
00:02:06.193697 /IEM/CPU1/cInstructions             12621 count
00:02:06.193702 /IEM/CPU1/cLongJumps                    0 bytes
00:02:06.193708 /IEM/CPU1/cPendingCommit                0 bytes
00:02:06.193713 /IEM/CPU1/cPotentialExits           12854 count
00:02:06.193718 /IEM/CPU1/cRetAspectNotImplemented        0 count
00:02:06.193723 /IEM/CPU1/cRetErrStatuses               0 count
00:02:06.193727 /IEM/CPU1/cRetInfStatuses              84 count
00:02:06.193733 /IEM/CPU1/cRetInstrNotImplemented        0 count
00:02:06.193737 /IEM/CPU1/cbWritten                 25074 bytes
00:02:06.193743 /IEM/CPU2/CodeTlb-Misses                0 count
00:02:06.193748 /IEM/CPU2/CodeTlb-PhysRev        ffffffffffff9c00 
00:02:06.193753 /IEM/CPU2/CodeTlb-Revision       fffff38000000000 
00:02:06.193758 /IEM/CPU2/CodeTlb-SlowReads             0 
00:02:06.193763 /IEM/CPU2/DataTlb-Misses                0 count
00:02:06.193768 /IEM/CPU2/DataTlb-PhysRev        ffffffffffff9c00 
00:02:06.193773 /IEM/CPU2/DataTlb-Revision       fffff38000000000 
00:02:06.193779 /IEM/CPU2/cInstructions             11885 count
00:02:06.193784 /IEM/CPU2/cLongJumps                    0 bytes
00:02:06.193789 /IEM/CPU2/cPendingCommit                0 bytes
00:02:06.193794 /IEM/CPU2/cPotentialExits           12120 count
00:02:06.193799 /IEM/CPU2/cRetAspectNotImplemented        0 count
00:02:06.193804 /IEM/CPU2/cRetErrStatuses               0 count
00:02:06.193809 /IEM/CPU2/cRetInfStatuses              75 count
00:02:06.193814 /IEM/CPU2/cRetInstrNotImplemented        0 count
00:02:06.193818 /IEM/CPU2/cbWritten                 23620 bytes
00:02:06.193824 /IEM/CPU3/CodeTlb-Misses                0 count
00:02:06.193829 /IEM/CPU3/CodeTlb-PhysRev        ffffffffffff9c00 
00:02:06.193834 /IEM/CPU3/CodeTlb-Revision       fffff38000000000 
00:02:06.193840 /IEM/CPU3/CodeTlb-SlowReads             0 
00:02:06.193845 /IEM/CPU3/DataTlb-Misses                0 count
00:02:06.193850 /IEM/CPU3/DataTlb-PhysRev        ffffffffffff9c00 
00:02:06.193855 /IEM/CPU3/DataTlb-Revision       fffff38000000000 
00:02:06.193860 /IEM/CPU3/cInstructions             15112 count
00:02:06.193865 /IEM/CPU3/cLongJumps                    0 bytes
00:02:06.193871 /IEM/CPU3/cPendingCommit                0 bytes
00:02:06.193876 /IEM/CPU3/cPotentialExits           15375 count
00:02:06.193881 /IEM/CPU3/cRetAspectNotImplemented        0 count
00:02:06.193886 /IEM/CPU3/cRetErrStatuses               0 count
00:02:06.193891 /IEM/CPU3/cRetInfStatuses              41 count
00:02:06.193896 /IEM/CPU3/cRetInstrNotImplemented        0 count
00:02:06.193901 /IEM/CPU3/cbWritten                 30142 bytes
00:02:06.193906 /IOM/MmioMappingsStale                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.193912 /MM/HyperHeap/cbFree              8534064 bytes
00:02:06.193918 /MM/HyperHeap/cbHeap              8650432 bytes
00:02:06.193923 /PDM/BlkCache/cbCached                  0 bytes
00:02:06.193928 /PDM/BlkCache/cbCachedFru               0 bytes
00:02:06.193933 /PDM/BlkCache/cbCachedMruIn             0 bytes
00:02:06.193938 /PDM/BlkCache/cbCachedMruOut            0 bytes
00:02:06.193943 /PDM/BlkCache/cbMax               5242880 bytes
00:02:06.193948 /PDM/CritSects/8237A#0Auto/ContentionR3        0 times
00:02:06.193953 /PDM/CritSects/8237A#0Auto/ContentionRZLock        0 times
00:02:06.193958 /PDM/CritSects/8237A#0Auto/ContentionRZUnlock        0 times
00:02:06.193963 /PDM/CritSects/GIMDev#0Auto/ContentionR3        0 times
00:02:06.193968 /PDM/CritSects/GIMDev#0Auto/ContentionRZLock        0 times
00:02:06.193973 /PDM/CritSects/GIMDev#0Auto/ContentionRZUnlock        0 times
00:02:06.193978 /PDM/CritSects/IntNetXmit_0/ContentionR3        0 times
00:02:06.193983 /PDM/CritSects/IntNetXmit_0/ContentionRZLock        0 times
00:02:06.193988 /PDM/CritSects/IntNetXmit_0/ContentionRZUnlock        0 times
00:02:06.193994 /PDM/CritSects/MM-HYPER/ContentionR3        0 times
00:02:06.193999 /PDM/CritSects/MM-HYPER/ContentionRZLock        0 times
00:02:06.194004 /PDM/CritSects/MM-HYPER/ContentionRZUnlock        0 times
00:02:06.194009 /PDM/CritSects/NOP/ContentionR3         0 times
00:02:06.194014 /PDM/CritSects/NOP/ContentionRZLock        0 times
00:02:06.194019 /PDM/CritSects/NOP/ContentionRZUnlock        0 times
00:02:06.194026 /PDM/CritSects/PDM/ContentionR3       148 times
00:02:06.194034 /PDM/CritSects/PDM/ContentionRZLock       15 times
00:02:06.194039 /PDM/CritSects/PDM/ContentionRZUnlock        0 times
00:02:06.194044 /PDM/CritSects/PGM/ContentionR3     21333 times
00:02:06.194049 /PDM/CritSects/PGM/ContentionRZLock     4228 times
00:02:06.194054 /PDM/CritSects/PGM/ContentionRZUnlock        0 times
00:02:06.194059 /PDM/CritSects/TM Timer Lock/ContentionR3     2685 times
00:02:06.194064 /PDM/CritSects/TM Timer Lock/ContentionRZLock        0 times
00:02:06.194069 /PDM/CritSects/TM Timer Lock/ContentionRZUnlock        0 times
00:02:06.194074 /PDM/CritSects/TM VirtualSync Lock/ContentionR3     4557 times
00:02:06.194079 /PDM/CritSects/TM VirtualSync Lock/ContentionRZLock    23755 times
00:02:06.194084 /PDM/CritSects/TM VirtualSync Lock/ContentionRZUnlock        4 times
00:02:06.194090 /PDM/CritSects/VMMDev#0/ContentionR3        2 times
00:02:06.194095 /PDM/CritSects/VMMDev#0/ContentionRZLock        0 times
00:02:06.194100 /PDM/CritSects/VMMDev#0/ContentionRZUnlock        0 times
00:02:06.194105 /PDM/CritSects/VNet0/ContentionR3        0 times
00:02:06.194110 /PDM/CritSects/VNet0/ContentionRZLock        0 times
00:02:06.194115 /PDM/CritSects/VNet0/ContentionRZUnlock        0 times
00:02:06.194121 /PDM/CritSects/acpi#0/ContentionR3        0 times
00:02:06.194126 /PDM/CritSects/acpi#0/ContentionRZLock        0 times
00:02:06.194131 /PDM/CritSects/acpi#0/ContentionRZUnlock        0 times
00:02:06.194137 /PDM/CritSects/fastpipe#0Auto/ContentionR3      144 times
00:02:06.194144 /PDM/CritSects/fastpipe#0Auto/ContentionRZLock        0 times
00:02:06.194150 /PDM/CritSects/fastpipe#0Auto/ContentionRZUnlock        0 times
00:02:06.194155 /PDM/CritSects/mc146818#0Auto/ContentionR3        0 times
00:02:06.194160 /PDM/CritSects/mc146818#0Auto/ContentionRZLock        0 times
00:02:06.194165 /PDM/CritSects/mc146818#0Auto/ContentionRZUnlock        0 times
00:02:06.194170 /PDM/CritSects/pcarch#0Auto/ContentionR3        0 times
00:02:06.194175 /PDM/CritSects/pcarch#0Auto/ContentionRZLock        0 times
00:02:06.194180 /PDM/CritSects/pcarch#0Auto/ContentionRZUnlock        0 times
00:02:06.194185 /PDM/CritSects/pcbios#0Auto/ContentionR3        0 times
00:02:06.194190 /PDM/CritSects/pcbios#0Auto/ContentionRZLock        0 times
00:02:06.194195 /PDM/CritSects/pcbios#0Auto/ContentionRZUnlock        0 times
00:02:06.194199 /PDM/CritSects/pckbd#0Auto/ContentionR3        0 times
00:02:06.194204 /PDM/CritSects/pckbd#0Auto/ContentionRZLock        0 times
00:02:06.194209 /PDM/CritSects/pckbd#0Auto/ContentionRZUnlock        0 times
00:02:06.194215 /PDM/CritSects/pit#0/ContentionR3        0 times
00:02:06.194219 /PDM/CritSects/pit#0/ContentionRZLock        0 times
00:02:06.194224 /PDM/CritSects/pit#0/ContentionRZUnlock        0 times
00:02:06.194229 /PDM/CritSects/virtio-scsi#0Auto/ContentionR3      187 times
00:02:06.194234 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZLock      307 times
00:02:06.194239 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZUnlock        0 times
00:02:06.194244 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterExcl        0 times
00:02:06.194249 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterShared        0 times
00:02:06.194254 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterExcl        0 times
00:02:06.194259 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterShared        0 times
00:02:06.194264 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveExcl        0 times
00:02:06.194269 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveShared        0 times
00:02:06.194274 /PDM/CritSectsRw/IOM Lock/R3EnterExcl      114 times
00:02:06.194280 /PDM/CritSectsRw/IOM Lock/R3EnterShared    75389 times
00:02:06.194285 /PDM/CritSectsRw/IOM Lock/RZEnterExcl        0 times
00:02:06.194290 /PDM/CritSectsRw/IOM Lock/RZEnterShared   501930 times
00:02:06.194295 /PDM/Queue/DevHlp/AllocFailures         0 times
00:02:06.194300 /PDM/Queue/DevHlp/Flush                 0 calls
00:02:06.194305 /PDM/Queue/DevHlp/FlushLeftovers        0 times
00:02:06.194310 /PDM/Queue/DevHlp/Insert                0 calls
00:02:06.194315 /PDM/Queue/DevHlp/cItems                8 count
00:02:06.194320 /PDM/Queue/DevHlp/cbItem               64 bytes
00:02:06.194325 /PDM/Queue/Keyboard/AllocFailures        0 times
00:02:06.194330 /PDM/Queue/Keyboard/Flush               0 calls
00:02:06.194335 /PDM/Queue/Keyboard/FlushLeftovers        0 times
00:02:06.194340 /PDM/Queue/Keyboard/Insert              0 calls
00:02:06.194345 /PDM/Queue/Keyboard/cItems             64 count
00:02:06.194350 /PDM/Queue/Keyboard/cbItem             32 bytes
00:02:06.194355 /PDM/Queue/Mouse/AllocFailures          0 times
00:02:06.194360 /PDM/Queue/Mouse/Flush                  0 calls
00:02:06.194365 /PDM/Queue/Mouse/FlushLeftovers         0 times
00:02:06.194370 /PDM/Queue/Mouse/Insert                 0 calls
00:02:06.194375 /PDM/Queue/Mouse/cItems               128 count
00:02:06.194380 /PDM/Queue/Mouse/cbItem                48 bytes
00:02:06.194385 /PDM/Queue/SCSI-Eject/AllocFailures        0 times
00:02:06.194390 /PDM/Queue/SCSI-Eject/Flush             0 calls
00:02:06.194395 /PDM/Queue/SCSI-Eject/FlushLeftovers        0 times
00:02:06.194400 /PDM/Queue/SCSI-Eject/Insert            0 calls
00:02:06.194405 /PDM/Queue/SCSI-Eject/cItems            1 count
00:02:06.194410 /PDM/Queue/SCSI-Eject/cbItem           40 bytes
00:02:06.194415 /PDM/Queue/SCSI-Eject_1/AllocFailures        0 times
00:02:06.194420 /PDM/Queue/SCSI-Eject_1/Flush           0 calls
00:02:06.194425 /PDM/Queue/SCSI-Eject_1/FlushLeftovers        0 times
00:02:06.194430 /PDM/Queue/SCSI-Eject_1/Insert          0 calls
00:02:06.194435 /PDM/Queue/SCSI-Eject_1/cItems          1 count
00:02:06.194439 /PDM/Queue/SCSI-Eject_1/cbItem         40 bytes
00:02:06.194444 /PDM/Queue/SCSI-Eject_2/AllocFailures        0 times
00:02:06.194449 /PDM/Queue/SCSI-Eject_2/Flush           0 calls
00:02:06.194454 /PDM/Queue/SCSI-Eject_2/FlushLeftovers        0 times
00:02:06.194459 /PDM/Queue/SCSI-Eject_2/Insert          0 calls
00:02:06.194464 /PDM/Queue/SCSI-Eject_2/cItems          1 count
00:02:06.194469 /PDM/Queue/SCSI-Eject_2/cbItem         40 bytes
00:02:06.194474 /PGM/CPU0/cA20Changes                   2 times
00:02:06.194479 /PGM/CPU0/cGuestModeChanges          3700 times
00:02:06.194484 /PGM/CPU1/cA20Changes                   0 times
00:02:06.194489 /PGM/CPU1/cGuestModeChanges             3 times
00:02:06.194494 /PGM/CPU2/cA20Changes                   0 times
00:02:06.194499 /PGM/CPU2/cGuestModeChanges             3 times
00:02:06.194504 /PGM/CPU3/cA20Changes                   0 times
00:02:06.194510 /PGM/CPU3/cGuestModeChanges             3 times
00:02:06.194515 /PGM/ChunkR3Map/Mapped                700 count
00:02:06.194520 /PGM/ChunkR3Map/Unmapped                0 count
00:02:06.194525 /PGM/ChunkR3Map/c                     700 count
00:02:06.194530 /PGM/ChunkR3Map/cMax             4294967295 count
00:02:06.194535 /PGM/LargePage/Recheck                  0 times
00:02:06.194540 /PGM/LargePage/Refused                  2 times
00:02:06.194545 /PGM/LargePage/Reused                  13 times
00:02:06.194551 /PGM/Mmio2QueryAndResetDirtyBitmap        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194557 /PGM/Page/cAllPages               1577974 count
00:02:06.194562 /PGM/Page/cBalloonedPages               0 count
00:02:06.194567 /PGM/Page/cHandyPages                 128 count
00:02:06.194572 /PGM/Page/cLargePages                 176 count
00:02:06.194577 /PGM/Page/cLargePagesDisabled           0 count
00:02:06.194582 /PGM/Page/cMonitoredPages               0 count
00:02:06.194588 /PGM/Page/cPrivatePages            362994 count
00:02:06.194594 /PGM/Page/cPureMmioPages                4 count
00:02:06.194599 /PGM/Page/cReadLockedPages              0 count
00:02:06.194604 /PGM/Page/cReusedSharedPages            0 count
00:02:06.194609 /PGM/Page/cSharedPages                  0 count
00:02:06.194614 /PGM/Page/cWriteLockedPages             0 count
00:02:06.194619 /PGM/Page/cWrittenToPages               0 count
00:02:06.194626 /PGM/Page/cZeroPages              1214976 count
00:02:06.194633 /PGM/Pool/Grow                    8950305 ticks (    17900610 ticks,       2 times, max   9255017, min 8645593)
00:02:06.194642 /PGM/ShMod/Check                        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194650 /PGM/cRelocations                       0 times
00:02:06.194657 /PROF/CPU0/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194662 /PROF/CPU0/EM/ForcedActions        134422 times
00:02:06.194669 /PROF/CPU0/EM/Halted                13931 times
00:02:06.194675 /PROF/CPU0/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194681 /PROF/CPU0/EM/NEMExecuteCalled          0 times
00:02:06.194686 /PROF/CPU0/EM/RAWTotal                  0 times
00:02:06.194690 /PROF/CPU0/EM/REMTotal                  0 times
00:02:06.194696 /PROF/CPU0/EM/RecordedExits        983212 times
00:02:06.194703 /PROF/CPU0/EM/Total              449868456466 ticks/call (449868456466 ticks,       1 times, max 449868456466, min 449868456466)
00:02:06.194712 /PROF/CPU0/VM/Halt/Block          2772316 ns/call ( 34077319894 ticks,   12292 times, max 499928794, min       1)
00:02:06.194720 /PROF/CPU0/VM/Halt/BlockInsomnia  2772316 ns/call ( 34077319894 ticks,   12292 times, max 499928794, min       1)
00:02:06.194729 /PROF/CPU0/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194734 /PROF/CPU0/VM/Halt/BlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194741 /PROF/CPU0/VM/Halt/R0HaltBlock    1342500 ns/call ( 63100202207 ticks,   47002 times, max 500854818, min       3)
00:02:06.194749 /PROF/CPU0/VM/Halt/R0HaltBlockInsomnia  1331872 ns/call ( 62599347389 ticks,   47001 times, max 302753690, min       3)
00:02:06.194754 /PROF/CPU0/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194772 /PROF/CPU0/VM/Halt/R0HaltBlockOverslept   863658 ns/call (      863658 ticks,       1 times, max    863658, min  863658)
00:02:06.194778 /PROF/CPU0/VM/Halt/R0HaltExec       51201 times
00:02:06.194783 /PROF/CPU0/VM/Halt/R0HaltExec/FromBlock    46147 times
00:02:06.194787 /PROF/CPU0/VM/Halt/R0HaltExec/FromSpin     3632 times
00:02:06.194792 /PROF/CPU0/VM/Halt/R0HaltHistoryCounter    65070 times
00:02:06.194797 /PROF/CPU0/VM/Halt/R0HaltHistorySucceeded       33 times
00:02:06.194802 /PROF/CPU0/VM/Halt/R0HaltHistoryToRing3       16 times
00:02:06.194807 /PROF/CPU0/VM/Halt/R0HaltToR3       13869 times
00:02:06.194811 /PROF/CPU0/VM/Halt/R0HaltToR3/FromSpin      124 times
00:02:06.194816 /PROF/CPU0/VM/Halt/R0HaltToR3/Other        0 times
00:02:06.194820 /PROF/CPU0/VM/Halt/R0HaltToR3/PendingFF    12890 times
00:02:06.194825 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitNoInt      167 times
00:02:06.194830 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitPendingFF      688 times
00:02:06.194834 /PROF/CPU0/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:02:06.194839 /PROF/CPU0/VM/Halt/Timers             317 ns/call (     7818608 ticks,   24625 times, max     63445, min       1)
00:02:06.194845 /PROF/CPU0/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194850 /PROF/CPU1/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194855 /PROF/CPU1/EM/ForcedActions         73658 times
00:02:06.194861 /PROF/CPU1/EM/Halted                13994 times
00:02:06.194867 /PROF/CPU1/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194872 /PROF/CPU1/EM/NEMExecuteCalled          0 times
00:02:06.194876 /PROF/CPU1/EM/RAWTotal                  0 times
00:02:06.194881 /PROF/CPU1/EM/REMTotal                  0 times
00:02:06.194886 /PROF/CPU1/EM/RecordedExits        486913 times
00:02:06.194890 /PROF/CPU1/EM/Total              449868588652 ticks/call (449868588652 ticks,       1 times, max 449868588652, min 449868588652)
00:02:06.194896 /PROF/CPU1/VM/Halt/Block          2923256 ns/call ( 36292230551 ticks,   12415 times, max 500830902, min       1)
00:02:06.194902 /PROF/CPU1/VM/Halt/BlockInsomnia  2762892 ns/call ( 34290263748 ticks,   12411 times, max 491464057, min       1)
00:02:06.194907 /PROF/CPU1/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.194912 /PROF/CPU1/VM/Halt/BlockOverslept   491914 ns/call (     1967656 ticks,       4 times, max    831110, min  165615)
00:02:06.194917 /PROF/CPU1/VM/Halt/R0HaltBlock    2060675 ns/call ( 64905081504 ticks,   31497 times, max 500803330, min       3)
00:02:06.194923 /PROF/CPU1/VM/Halt/R0HaltBlockInsomnia  1997367 ns/call ( 62903091380 ticks,   31493 times, max 481498983, min       3)
00:02:06.194928 /PROF/CPU1/VM/Halt/R0HaltBlockOnTime 499978072 ns/call (   499978072 ticks,       1 times, max 499978072, min 499978072)
00:02:06.194934 /PROF/CPU1/VM/Halt/R0HaltBlockOverslept   679374 ns/call (     2038123 ticks,       3 times, max    811588, min  469947)
00:02:06.194939 /PROF/CPU1/VM/Halt/R0HaltExec       33479 times
00:02:06.194944 /PROF/CPU1/VM/Halt/R0HaltExec/FromBlock    30755 times
00:02:06.194948 /PROF/CPU1/VM/Halt/R0HaltExec/FromSpin     1994 times
00:02:06.194953 /PROF/CPU1/VM/Halt/R0HaltHistoryCounter    47425 times
00:02:06.194958 /PROF/CPU1/VM/Halt/R0HaltHistorySucceeded       19 times
00:02:06.194962 /PROF/CPU1/VM/Halt/R0HaltHistoryToRing3       49 times
00:02:06.194967 /PROF/CPU1/VM/Halt/R0HaltToR3       13946 times
00:02:06.194972 /PROF/CPU1/VM/Halt/R0HaltToR3/FromSpin      100 times
00:02:06.194977 /PROF/CPU1/VM/Halt/R0HaltToR3/Other        0 times
00:02:06.194981 /PROF/CPU1/VM/Halt/R0HaltToR3/PendingFF    13104 times
00:02:06.194986 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitNoInt       85 times
00:02:06.194990 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitPendingFF      657 times
00:02:06.194995 /PROF/CPU1/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:02:06.195000 /PROF/CPU1/VM/Halt/Timers             339 ns/call (     8423832 ticks,   24825 times, max     26127, min       1)
00:02:06.195005 /PROF/CPU1/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195010 /PROF/CPU2/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195015 /PROF/CPU2/EM/ForcedActions         77081 times
00:02:06.195020 /PROF/CPU2/EM/Halted                13239 times
00:02:06.195024 /PROF/CPU2/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195029 /PROF/CPU2/EM/NEMExecuteCalled          0 times
00:02:06.195034 /PROF/CPU2/EM/RAWTotal                  0 times
00:02:06.195039 /PROF/CPU2/EM/REMTotal                  0 times
00:02:06.195044 /PROF/CPU2/EM/RecordedExits        442582 times
00:02:06.195048 /PROF/CPU2/EM/Total              449868621558 ticks/call (449868621558 ticks,       1 times, max 449868621558, min 449868621558)
00:02:06.195054 /PROF/CPU2/VM/Halt/Block          2616834 ns/call ( 30889108661 ticks,   11804 times, max 491459387, min       1)
00:02:06.195059 /PROF/CPU2/VM/Halt/BlockInsomnia  2616834 ns/call ( 30889108661 ticks,   11804 times, max 491459387, min       1)
00:02:06.195065 /PROF/CPU2/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195070 /PROF/CPU2/VM/Halt/BlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195076 /PROF/CPU2/VM/Halt/R0HaltBlock    2488892 ns/call ( 70505339707 ticks,   28328 times, max 499890524, min      12)
00:02:06.195082 /PROF/CPU2/VM/Halt/R0HaltBlockInsomnia  2488892 ns/call ( 70505339707 ticks,   28328 times, max 499890524, min      12)
00:02:06.195087 /PROF/CPU2/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195092 /PROF/CPU2/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195097 /PROF/CPU2/VM/Halt/R0HaltExec       30289 times
00:02:06.195102 /PROF/CPU2/VM/Halt/R0HaltExec/FromBlock    27667 times
00:02:06.195106 /PROF/CPU2/VM/Halt/R0HaltExec/FromSpin     1793 times
00:02:06.195112 /PROF/CPU2/VM/Halt/R0HaltHistoryCounter    43493 times
00:02:06.195134 /PROF/CPU2/VM/Halt/R0HaltHistorySucceeded      116 times
00:02:06.195150 /PROF/CPU2/VM/Halt/R0HaltHistoryToRing3      116 times
00:02:06.195156 /PROF/CPU2/VM/Halt/R0HaltToR3       13204 times
00:02:06.195161 /PROF/CPU2/VM/Halt/R0HaltToR3/FromSpin      110 times
00:02:06.195166 /PROF/CPU2/VM/Halt/R0HaltToR3/Other        0 times
00:02:06.195171 /PROF/CPU2/VM/Halt/R0HaltToR3/PendingFF    12433 times
00:02:06.195189 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitNoInt       95 times
00:02:06.195194 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitPendingFF      566 times
00:02:06.195199 /PROF/CPU2/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:02:06.195203 /PROF/CPU2/VM/Halt/Timers             353 ns/call (     8344053 ticks,   23597 times, max     44754, min       1)
00:02:06.195209 /PROF/CPU2/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195215 /PROF/CPU3/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195220 /PROF/CPU3/EM/ForcedActions         92707 times
00:02:06.195225 /PROF/CPU3/EM/Halted                23450 times
00:02:06.195230 /PROF/CPU3/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195235 /PROF/CPU3/EM/NEMExecuteCalled          0 times
00:02:06.195240 /PROF/CPU3/EM/RAWTotal                  0 times
00:02:06.195245 /PROF/CPU3/EM/REMTotal                  0 times
00:02:06.195250 /PROF/CPU3/EM/RecordedExits        507212 times
00:02:06.195255 /PROF/CPU3/EM/Total              449868634646 ticks/call (449868634646 ticks,       1 times, max 449868634646, min 449868634646)
00:02:06.195261 /PROF/CPU3/VM/Halt/Block          2625997 ns/call ( 73937586137 ticks,   28156 times, max 644794181, min       1)
00:02:06.195267 /PROF/CPU3/VM/Halt/BlockInsomnia   737160 ns/call ( 10141854543 ticks,   13758 times, max 156304204, min       1)
00:02:06.195272 /PROF/CPU3/VM/Halt/BlockOnTime    2603514 ns/call (  5217443639 ticks,    2004 times, max 151080000, min   22928)
00:02:06.195278 /PROF/CPU3/VM/Halt/BlockOverslept   461375 ns/call (  5718291676 ticks,   12394 times, max   2785465, min   50016)
00:02:06.195284 /PROF/CPU3/VM/Halt/R0HaltBlock     871256 ns/call ( 27462014936 ticks,   31520 times, max  35157495, min     664)
00:02:06.195290 /PROF/CPU3/VM/Halt/R0HaltBlockInsomnia   423071 ns/call ( 10567470453 ticks,   24978 times, max  16519562, min     664)
00:02:06.195296 /PROF/CPU3/VM/Halt/R0HaltBlockOnTime  1573596 ns/call (  3150340630 ticks,    2002 times, max  22324531, min     906)
00:02:06.195302 /PROF/CPU3/VM/Halt/R0HaltBlockOverslept   349641 ns/call (  1587370342 ticks,    4540 times, max   2676294, min   50049)
00:02:06.195308 /PROF/CPU3/VM/Halt/R0HaltExec       23983 times
00:02:06.195312 /PROF/CPU3/VM/Halt/R0HaltExec/FromBlock    22172 times
00:02:06.195317 /PROF/CPU3/VM/Halt/R0HaltExec/FromSpin     1112 times
00:02:06.195322 /PROF/CPU3/VM/Halt/R0HaltHistoryCounter    47124 times
00:02:06.195327 /PROF/CPU3/VM/Halt/R0HaltHistorySucceeded        1 times
00:02:06.195332 /PROF/CPU3/VM/Halt/R0HaltHistoryToRing3       22 times
00:02:06.195338 /PROF/CPU3/VM/Halt/R0HaltToR3       23141 times
00:02:06.195343 /PROF/CPU3/VM/Halt/R0HaltToR3/FromSpin        4 times
00:02:06.195348 /PROF/CPU3/VM/Halt/R0HaltToR3/Other        0 times
00:02:06.195353 /PROF/CPU3/VM/Halt/R0HaltToR3/PendingFF    12575 times
00:02:06.195358 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitNoInt     6371 times
00:02:06.195363 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitPendingFF     2977 times
00:02:06.195367 /PROF/CPU3/VM/Halt/R0HaltToR3/SmallDelta     1214 times
00:02:06.195372 /PROF/CPU3/VM/Halt/Timers            3308 ns/call (   197465599 ticks,   59692 times, max   1001003, min       2)
00:02:06.195378 /PROF/CPU3/VM/Halt/Yield             3277 ns/call (      773397 ticks,     236 times, max     19248, min       1)
00:02:06.195384 /Public/NetAdapter/0/BytesReceived    30102 bytes
00:02:06.195389 /Public/NetAdapter/0/BytesTransmitted    42229 bytes
00:02:06.195394 /Public/NetAdapter/0/virtio-net         0 
00:02:06.195399 /Public/Storage/VIRTIO-SCSI0/Port0/BytesRead 1540033024 bytes
00:02:06.195404 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufAttempts        0 count
00:02:06.195409 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufSuccess        0 count
00:02:06.195414 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsRead    33527 count
00:02:06.195419 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSubmitted    33527 count
00:02:06.195423 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSucceeded    33527 count
00:02:06.195429 /Public/Storage/VIRTIO-SCSI0/Port1/BytesRead 455743488 bytes
00:02:06.195434 /Public/Storage/VIRTIO-SCSI0/Port1/BytesWritten 197562368 bytes
00:02:06.195438 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufAttempts        0 count
00:02:06.195443 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufSuccess        0 count
00:02:06.195448 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsRead    13935 count
00:02:06.195453 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSubmitted    16563 count
00:02:06.195458 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSucceeded    16563 count
00:02:06.195463 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsWrite     2628 count
00:02:06.195468 /Public/Storage/VIRTIO-SCSI0/Port2/BytesRead  8446976 bytes
00:02:06.195473 /Public/Storage/VIRTIO-SCSI0/Port2/BytesWritten   323584 bytes
00:02:06.195478 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufAttempts        0 count
00:02:06.195483 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufSuccess        0 count
00:02:06.195487 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsRead      340 count
00:02:06.195492 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSubmitted      383 count
00:02:06.195497 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSucceeded      383 count
00:02:06.195502 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsWrite       43 count
00:02:06.195507 /SELM/LoadHidSel/GstReadErrors          0 times
00:02:06.195511 /SELM/LoadHidSel/NoGoodGuest            0 times
00:02:06.195516 /TM/CPU/00/cNsExecuting          20264801520 ns
00:02:06.195521 /TM/CPU/00/cNsHalted             34098311708 ns
00:02:06.195526 /TM/CPU/00/cNsOther              70600312406 ns
00:02:06.195532 /TM/CPU/00/cNsTotal              124963425634 ns
00:02:06.195537 /TM/CPU/00/cPeriodsExecuting       983212 count
00:02:06.195542 /TM/CPU/00/cPeriodsHalted           12333 count
00:02:06.195546 /TM/CPU/00/pctExecuting                 1 %
00:02:06.195551 /TM/CPU/00/pctHalted                   31 %
00:02:06.195556 /TM/CPU/00/pctOther                    67 %
00:02:06.195562 /TM/CPU/01/cNsExecuting          19027048434 ns
00:02:06.195567 /TM/CPU/01/cNsHalted             36315875868 ns
00:02:06.195572 /TM/CPU/01/cNsOther              69620532718 ns
00:02:06.195577 /TM/CPU/01/cNsTotal              124963457020 ns
00:02:06.195582 /TM/CPU/01/cPeriodsExecuting       486913 count
00:02:06.195587 /TM/CPU/01/cPeriodsHalted           12410 count
00:02:06.195592 /TM/CPU/01/pctExecuting                 1 %
00:02:06.195597 /TM/CPU/01/pctHalted                   72 %
00:02:06.195602 /TM/CPU/01/pctOther                    25 %
00:02:06.195607 /TM/CPU/02/cNsExecuting          18886597632 ns
00:02:06.195658 /TM/CPU/02/cNsHalted             30912861919 ns
00:02:06.195665 /TM/CPU/02/cNsOther              75164011436 ns
00:02:06.195671 /TM/CPU/02/cNsTotal              124963470987 ns
00:02:06.195676 /TM/CPU/02/cPeriodsExecuting       442582 count
00:02:06.195681 /TM/CPU/02/cPeriodsHalted           11793 count
00:02:06.195686 /TM/CPU/02/pctExecuting                 1 %
00:02:06.195691 /TM/CPU/02/pctHalted                   64 %
00:02:06.195696 /TM/CPU/02/pctOther                    33 %
00:02:06.195701 /TM/CPU/03/cNsExecuting          18453716976 ns
00:02:06.195706 /TM/CPU/03/cNsHalted             74175291632 ns
00:02:06.195711 /TM/CPU/03/cNsOther              32334461588 ns
00:02:06.195716 /TM/CPU/03/cNsTotal              124963470196 ns
00:02:06.195721 /TM/CPU/03/cPeriodsExecuting       507212 count
00:02:06.195726 /TM/CPU/03/cPeriodsHalted           20161 count
00:02:06.195731 /TM/CPU/03/pctExecuting                 0 %
00:02:06.195736 /TM/CPU/03/pctHalted                   93 %
00:02:06.195741 /TM/CPU/03/pctOther                     6 %
00:02:06.195746 /TM/CPU/pctExecuting                    1 %
00:02:06.195751 /TM/CPU/pctHalted                      63 %
00:02:06.195756 /TM/CPU/pctOther                       34 %
00:02:06.195761 /TM/MaxHzHint                           0 Hz
00:02:06.195766 /TM/PIT/Handler                         0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:02:06.195772 /TM/PIT/Irq                             0 times
00:02:06.195777 /TM/R0/1nsSteps                      2197 times
00:02:06.195782 /TM/R3/1nsSteps                      2781 times
00:02:06.195788 /TM/TSC/offCPU0                  395315709998197 ticks
00:02:06.195793 /TM/TSC/offCPU1                  395315709998197 ticks
00:02:06.195799 /TM/TSC/offCPU2                  395315709998197 ticks
00:02:06.195804 /TM/TSC/offCPU3                  395315709998197 ticks
00:02:06.195809 /TM/VirtualSync/CurrentOffset      354966 ns
00:02:06.195814 ********************* End of statistics **********************
00:02:06.198258 fastpipe: deviceDestruct g_bGuestPowerOff=1
00:02:06.200910 GIM: KVM: Resetting MSRs
00:02:06.203791 Changing the VM state from 'DESTROYING' to 'TERMINATED'
00:02:06.204937 Console: Machine state changed to 'PoweredOff'
00:02:06.205057 VBoxHeadless: processEventQueue: VERR_INTERRUPTED, termination requested
00:02:06.375308 VBoxHeadless: exiting
