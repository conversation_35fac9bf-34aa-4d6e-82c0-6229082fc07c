{"noices": [{"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.jx3m", "fileName": "3603新版本更新提示剑网3.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.xqhy.legendbox", "fileName": "996游戏盒子雷电5加载异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "tv.danmaku.bili", "fileName": "B站哔哩哔哩.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.cc", "fileName": "CC.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.gamehelper.dnf", "fileName": "DNF助手提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.dnftest|com.tencent.tmgp.dnftest.DNFMainActivity", "fileName": "DNF手游按键说明.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "FGO官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "cn.jj", "fileName": "JJ斗地主3.0无法输入数字.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.square_enix.android_googleplay.nierspjp", "fileName": "NieR.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gamamobi.nikke|com.proximabeta.nikke", "fileName": "nikke横屏120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.oppo.market|com.nearme.gamecenter", "fileName": "OPPO商店-游戏中心.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.junhai.yzjq.nearme.gamecenter", "fileName": "oppo框架.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sofunny.Chicken", "fileName": "OPPO游戏中心.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pocket.pocketdestiny", "fileName": "PocketDestiny9141A卡卡死问题修复.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.mobileqq", "fileName": "QQ.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.qqx5", "fileName": "QQ炫舞.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.speedmobile", "fileName": "QQ飞车排位封号.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "QQ飞车登陆卡屏横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "cn.soulapp.android", "fileName": "Soul瞬间无法评论解决方法.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "soul闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.github.tvbox.osc.tk", "fileName": "TVbox配置教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.UCMobile.x86", "fileName": "UC浏览器提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.asiainno.uplive", "fileName": "up直播提示卸载支付宝.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.vivo.game", "fileName": "vivo游戏安装失败.nmp"}, {"packageNameType": 1, "packageNamePattern": "zpp.wjy.xxsq|com.yztc.studio.plugin|de.robv.android.xposed.installer|com.topjohnwu.magisk", "fileName": "xp，抹机王，抹机神器，面具修改system.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yy.yomi", "fileName": "Yo语音.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.duowan.mobile", "fileName": "YY直播.nmp"}, {"packageNameType": 1, "packageNamePattern": "tv.acfundanmaku.video", "fileName": "《A站》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.fatego|com.tencent.tmgp.fgo|com.bilibili.fgo.*", "fileName": "《FGO》闪退卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.huawei.hwid", "fileName": "《HMS Core》华为服务.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.qqmusic", "fileName": "《QQ音乐》打开直播闪退的解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ss.android.article.news", "fileName": "《今日头条》无法扫码登录网页版问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.youku.phone", "fileName": "《优酷》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.hxgame|com.tencent.hxgame.*", "fileName": "《华夏手游》无法登录，iOS.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.wly2|com.tencent.tmgp.wly2.*", "fileName": "《卧龙吟2》高帧率教程.nmp"}, {"packageNameType": 0, "packageNamePattern": "cn.com.iyoyo.tsm.ld", "fileName": "《吞食天下归来》.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.pm02.*", "fileName": "《天谕》横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sina.weibo", "fileName": "《微博》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.smile.gifmaker|com.kuaishou.nebula", "fileName": "《快手》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.g67.*|com.tencent.tmgp.g67", "fileName": "《暗黑破坏神》机型未支持.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.blizzard.diablo.immortal", "fileName": "《暗黑破坏神不朽》国际服闪退解决.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.woodenwolf.mgame", "fileName": "《梦中的你》无法微信登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.qiyi.video|tv.pps.mobile", "fileName": "《爱奇艺》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zqgame.jtzq.*|com.tencent.tmgp.jtzq", "fileName": "《街头足球》按键详解横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.jielan2|com.jl2.*|com.ztgame.jl2.*", "fileName": "《街篮2》横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.aligames.star.sgzhxdl.aligames|com.ali.games.star|com.tencent.tmgp.aligames.sgzhxdl|com.aligames.star.sgzhxdl.*|com.ali.games.star.*", "fileName": "【三国志幻想大陆】如何手刷心仪的初始号.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xy.txgx.*", "fileName": "【三国：天下归心】高帧设置&高画质设置.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.happysugar.m.ad", "fileName": "【米姆米姆哈】运行指南&高帧高画质设置.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.moonton.silverblood.*", "fileName": "【银与绯】部分界面花屏解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.m71.*|com.leiting.xian", "fileName": "一念逍遥官服.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.yqcr", "fileName": "一拳超人：正义执行iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.wyclx.*", "fileName": "一梦江湖登录闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.gwgo", "fileName": "一起来捉妖公告.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.sevensin", "fileName": "七人传奇：光与暗之交战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sincetimes.wlqy.*|com.tencent.tmgp.bilibili.wlqy|com.bilibili.wlqy.*", "fileName": "万灵启源.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.youzu.g36.yz", "fileName": "三十六计-游族.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.phonecoolgame.sanguo.*", "fileName": "三国之乱世为王.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.game.wlws.*", "fileName": "三国志威力无双提示更新横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.aligames.sgzzlb.*", "fileName": "三国志战略版闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bf.sgs.hdexp", "fileName": "三国杀官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.rastar.sqcdjz.*", "fileName": "三国群英传-策定九州.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.nslg.*|com.tencent.tmgp.bilibili.nslg", "fileName": "三国：谋定天下.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.gumballs.leiting", "fileName": "不思议迷宫.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yunchang.blr3.ld", "fileName": "不良人3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jingxiu.civ|com.jingxiu.civ.*|com.tencent.tmgp.eden", "fileName": "世界启元.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fszl7723.*", "fileName": "丰收之路.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ironoakgames.ftk.m3839.*", "fileName": "为例吾王.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.bilibili.fgo.leshi", "fileName": "乐视渠道《FGO》闪退横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.wec", "fileName": "乱世王者iOS登录教程、启动过慢.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tkw", "fileName": "乱世逐鹿.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hero.dna.gf", "fileName": "二重螺旋.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ahlh.sylj.*", "fileName": "亮剑跳转雷电9提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yuwan.cqytj.*", "fileName": "从前有条街.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xfhd.ld", "fileName": "仙凡幻想120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.xianjianshijie.*|com.cmge.xjsj.*", "fileName": "仙剑世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kunlun.mobpal.*|com.tencent.tmgp.kunlunklkl.mobpal", "fileName": "仙剑奇侠传移动版横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xd.ro.roapk", "fileName": "仙境传说RO：守护永恒的爱.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zlongame.cn.ro.*", "fileName": "仙境传说：新启航.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.ro|com.jingxiu.roarcj.*", "fileName": "仙境传说：爱如初见.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xianyud.qs.cn", "fileName": "仙遇启动黑屏闪退问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tomatogames.ys.*", "fileName": "伊苏6：纳比斯汀的方舟.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wepie.weplay", "fileName": "会玩.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fgcs.sf.ld", "fileName": "传世霸业无法更新解决办法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mz.cqsg.*", "fileName": "传奇三国.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.cqtxsy", "fileName": "传奇天下.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pcncn.jj", "fileName": "作业精灵横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.cod", "fileName": "使命召唤手游.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.shihun.android", "fileName": "侍魂公告.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xikg.cly.ld|com.cszr.aligames", "fileName": "侠剑狂歌.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.game.xzjh2.*", "fileName": "修真江湖2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.l10", "fileName": "倩女幽魂.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.happyelements.es2|com.happyelements.es2.*", "fileName": "偶像梦幻祭2.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.mx.browser", "fileName": "傲游浏览器提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.letsgo", "fileName": "元梦之星新.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.humo.yqqsqz.aligames", "fileName": "元气骑士前传（九游）.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ChillyRoom.DungeonShooter|com.knight.union.*|com.chillyroom.knight.*|com.tencent.tmgp.yqqskp", "fileName": "元气骑士提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.lv", "fileName": "光与夜之恋120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.sky", "fileName": "光遇.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.fgame.*", "fileName": "全明星激斗3.0卡屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.fgame.*", "fileName": "全明星激斗60帧横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.qmzg2.*", "fileName": "全民主公2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.tmsk.qj3", "fileName": "全民奇迹2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.crisisfire.android.*", "fileName": "全民枪战2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "公主连结.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xy.slmxssj.*", "fileName": "关于我转生变成史莱姆这档事：新世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.maplem", "fileName": "冒险岛：枫之传说.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.habby.cncapy.*", "fileName": "冒险者日记.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.dzyfg.qwer", "fileName": "冰雪复古3.0无法打字的横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.moba|com.netease.moba.*", "fileName": "决战平安京如何解决按键偏移.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.p2pcncn.hwyad", "fileName": "凌云诺.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.sao.*|com.tencent.tmgp.bilibili.sao", "fileName": "刀剑神域黑衣骑士：王牌.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hero.outerland.soc.*", "fileName": "创造吧！我们的星球.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.lilithgames.hgame.cn|com.tencent.tmgp.lilithgames.afk|com.lilithgames.afk.*", "fileName": "剑与远征横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.lilithgame.igame.*", "fileName": "剑与远征：启程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hntx.gransaga.*", "fileName": "剑与骑士团.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xsj.jxsj3.*|com.jxsj3.branch.*|com.seasun.jxsj3.*|com.tencent.tmgp.seasun.jxsj3|com.jianxia.shijie.san", "fileName": "剑侠世界3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.seasun.jxqy0.*", "fileName": "剑侠情缘·零.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.b2", "fileName": "剑灵2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.seasun.jxp.jsml.xsj|com.jianxia.guilai.damai", "fileName": "剑网1：归来120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.jx3m", "fileName": "剑网3ios登录、卡新手教程横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.seasun.jx3", "fileName": "剑网3无界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xy.sh.yszl1548|com.xy.sh.yszlhf1520", "fileName": "勇士之路.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.qnzl.*", "fileName": "千年之旅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.huawei.appmarket", "fileName": "华为应用市场.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.huawei.gamebox", "fileName": "华为游戏中心.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.fog.*", "fileName": "原力守护者.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.miHoYo.Yuanshen|com.miHoYo.ys.*|com.miHoYo.GenshinImpact", "fileName": "原神.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.Qunar", "fileName": "去哪儿旅行闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.gcg2.ld|com.bilibili.gcg2.aligames|com.bilibili.gcg2.bili", "fileName": "双生视界4.0进副本卡死解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.c2vl.kgamebox", "fileName": "口袋狼人杀4.0进入公会闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.PGSoul.BOG2.*|com.tencent.tmgp.com.tencent.tmgp.bog2", "fileName": "古魂.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sqw.jwdzg.*", "fileName": "叫我大掌柜.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.izhaohe.zhaohe2", "fileName": "召唤与合成2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jydm.yll.*", "fileName": "叶罗丽时辰砂.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.msgame", "fileName": "合金弹头：觉醒.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xjskp.gztv1.*|com.mini.jskp.*", "fileName": "向僵尸开炮.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.KiHan", "fileName": "启动闪退横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ming.yun|com.dpstorm.or|com.dpstorm.or.bilibili", "fileName": "命运神界初始号.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.pubgmhd", "fileName": "和平精英.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jf.voice", "fileName": "哆咪星球.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.harrypotter|com.tencent.tmgp.harrypotter", "fileName": "哈利波特：魔法觉醒.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "喜马拉雅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.moefantasy.clover.*", "fileName": "四叶草剧场开纹理横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kwai.hisense.*", "fileName": "回森.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.sskgame", "fileName": "圣斗士星矢（腾讯）iOS教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.dnfld", "fileName": "地下城与勇士云游.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.dnf", "fileName": "地下城与勇士：起源.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kiloo.subwaysurf", "fileName": "地铁跑酷横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.snake.*", "fileName": "坎公骑冠剑官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.wotb|com.tencent.tmgp.yongyong.tksjsjz|com.netease.wotb.*", "fileName": "坦克世界闪击战匹配过慢.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sqw.cztx.*", "fileName": "城主天下.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.trssj", "fileName": "塔瑞斯世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.blade.bd.*|com.hermes.blade.*|com.tencent.tmgp.hermes.blade", "fileName": "境·界 刀鸣.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gf.wxcncn.hwyad.*", "fileName": "墨剑江湖.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xinxin.mh.*|com.qhooplay.supreme|com.tencent.tmgp.mh|com.qianhuan.mh.*", "fileName": "墨魂.nmp"}, {"packageNameType": 1, "packageNamePattern": "air.com.mfs.fgcs", "fileName": "复古传神.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sofunny.Chicken", "fileName": "大话2口袋版.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.dhxy.*", "fileName": "大话西游.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.dhxy.ewan.game.LD", "fileName": "大话西游雷电渠道停运.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hnr.dxxw.*", "fileName": "大象新闻提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tszz.net.HWTV.HUAWEI", "fileName": "天使之战-华为.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xy.tszz.*|com.xy.tszzsem", "fileName": "天使之战120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.linegames.dcglobal.xsolla", "fileName": "天命之子国际服攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.NextFloor.DestinyChild", "fileName": "天命之子韩服攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zlongame.tdj.*", "fileName": "天地劫：幽城再临.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yyhy.ttmc.*", "fileName": "天天迷城.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.tstl", "fileName": "天龙八部官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gaea.tgyhj.jh.ld", "fileName": "太古妖皇诀.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.intoillusion.journey|com.tencent.tmgp.slsj|com.slsjsy.*", "fileName": "失落四境.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.slxh.*", "fileName": "失落星环.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.chio.*", "fileName": "奇奥英雄传横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.qeeyou.app.accelerator", "fileName": "奇游加速器闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hnjw.ts.*", "fileName": "奇迹世界：起源.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.aobi.*|com.baitian.aobi.czs.*", "fileName": "奥比岛：梦想国度120帧横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.atm.*", "fileName": "奥特曼：集结.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.skls.c6tj9p3a5f|com.pwrd.persona5x.*|com.hpnz5x.le6ky|com.tencent.tmgp.p5x|com.pwrd.p5x.*|tw.com.iwplay.p5x|com.p5xkr.*", "fileName": "女神异闻录：夜幕魅影.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.djsy", "fileName": "妄想山海.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.ft", "fileName": "妖精的尾巴魔导少年iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.syfz.*|com.tencent.tmgp.yongyong.gdxfnew", "fileName": "孤岛先锋提示.nmp"}, {"packageNameType": 0, "packageNamePattern": "cn.xuexi.android", "fileName": "学习强国.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xuexiaoyi.xxy", "fileName": "学小易横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kakaogames.gdts", "fileName": "守望传说Guardian Tales.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.more.dayzsurvival.cn", "fileName": "守望黎明提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wanmei.wmsjjdb.jh.*", "fileName": "完美世界：诸神之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.intiny.hd6.*", "fileName": "完蛋！我被美女包围了！2 - 高帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.intiny.hd6.*", "fileName": "完蛋！我被美女包围了！2.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.jgm", "fileName": "家国梦提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.xxsy", "fileName": "寻仙.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.seayoo.jxfs.*", "fileName": "封神再临.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.trinitigame.miniwarriors2.aligames", "fileName": "小小军团2用3.0会黑屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.xxyg.*", "fileName": "小小蚁国卡登陆，横屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.forest", "fileName": "小森生活iOS.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.skymoons.xmaxc02.*", "fileName": "小猫爱消除.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mg.ec", "fileName": "小芒雷电9白屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "ccom.bxcr.xycq.ld", "fileName": "小鱼传奇 (1).nmp"}, {"packageNameType": 1, "packageNamePattern": "com.Sunborn.SnqxExilium.*|com.Sunborn.SnqxExilium", "fileName": "少女前线2追放.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sunborn.neuralcloud.cn.*", "fileName": "少女前线：云图计划官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sngx.*", "fileName": "少年歌行：风花雪月.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tlzbyy.*", "fileName": "屠龙争霸.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.youzu.shjh.*", "fileName": "山海镜花.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.com.shjh<PERSON><PERSON>ji", "fileName": "山海镜花助手.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.miHoYo.enterprise.NGHSoD", "fileName": "崩坏三.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.miHoYo.HSoDv2Original|com.miHoYo.game2144.mi|com.miHoYo.HSoDv2BiliBiliRelea|com.miHoYo.HSoDv22144.uc|com.miHoYo.HSoDv22144.qihoo", "fileName": "崩坏学园2提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.miHoYo.hkrpg.*|com.HoYoverse.hkrpgoversea|com.miHoYo.hkrpg|com.miHoYo.hkrpgcb", "fileName": "崩坏：星穹铁道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bftzn.*", "fileName": "巴风特之怒.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.caohua.dgqzywm.*", "fileName": "帝国：权杖与文明.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mlahsymx.ld", "fileName": "幻世与冒险.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.hsqsl.*", "fileName": "幻书启示录横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hottagames.hotta.*|com.pwrd.hotta.*|com.tch0.ag.af1u5|com.y.rhw73.jst|com.tencent.tmgp.hotta|com.wanmei.hotta.*|com.games.hotta.*|com.qrsl.hotta.*|com.jade.dynasty.hotta|com.pwrd.cloud.huanta.laohu", "fileName": "幻塔.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hxmjl.*", "fileName": "幻想名将录120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.mergemansion", "fileName": "庄园合合.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.uc", "fileName": "异人之下.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gamebeans.coside", "fileName": "异界事务所攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.yxhs", "fileName": "异象回声.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.road7.ddtdmxandroid.*|com.ddtdmx.rh.yxy", "fileName": "弹弹堂大冒险.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wildgames.journey", "fileName": "归途危机.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tong.lcgame.*", "fileName": "归龙潮.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.yzs.*", "fileName": "影之诗120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.huawei.himovceie", "fileName": "影视仓配置教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.shizhuang.duapp", "fileName": "得物不登录黑屏问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.ylm", "fileName": "御龙在天iOS.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.mm", "fileName": "微信登录转圈.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pandadastudio.ninjamustdie3.*|com.tencent.tmgp.pandadastudio.ninja3", "fileName": "忍者必须死3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.muzhi.rzggl.*", "fileName": "忍者龟：归来120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.pm03.*", "fileName": "忘川风华录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kldlz.dm.*", "fileName": "快来当领主.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.yjzs.*", "fileName": "悠久之树.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sydsj.*", "fileName": "悠星大陆.nmp"}, {"packageNameType": 1, "packageNamePattern": "bubei.tingshu", "fileName": "懒人听书闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.piaxiya.app", "fileName": "戏鲸.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.mm.ddjxw", "fileName": "我是大东家.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.x19|com.netease.mc.*", "fileName": "我的世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.senlin.mywar.ld", "fileName": "我的战争.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.zsgbzqm.*", "fileName": "战三国八阵奇谋闪退横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.nextjoy.stilla", "fileName": "战之刃：幸存者.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kurogame.haru.hero", "fileName": "战双帕弥什官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.dca.zgj.ld", "fileName": "战国纪.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zhanling.*", "fileName": "战灵.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.huanmeng.zhanjian2.*|com.tencent.tmgp.zhanjian2", "fileName": "战舰少女R官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "抖音.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.raziel", "fileName": "拉结尔无法进入游戏提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jfcot", "fileName": "指尖领主.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cyjh.mobileanjian.*", "fileName": "按键精灵.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.l10.wdj.guopan|com.tencent.tmgp.l10|com.netease.hyxd.*|com.netease.rs|com.jinwan.hyxd.xmw|com.netease.hyxdtw|com.netease.ko|com.tencent.tmgp.vgame|com.tlmvb.bt.btg|tw.com.szn.kok|hk.com.szn.kok|com.tencent.wok|com.activision.callofduty.shooter|com.tencent.tmgp.kr.codm|com.garena.game.codm|com.vng.codmvn|com.nexon.v4kr|com.nexon.v4tw", "fileName": "按键错位解决办法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.djcity", "fileName": "掌上道聚城.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.taomee.moleleiting|com.leiting.mole.*|com.taomee.molenew.huawei|com.taomee.molenew.mi", "fileName": "摩尔庄园.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tanwan.srwsly.*|com.tanwan.srws.*", "fileName": "散人无双卡加载界面提示横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.dm", "fileName": "数码宝贝：新世纪.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.xy.dm.ld", "fileName": "数码宝贝：源码长时间挂机闪退横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gnml.wmyzfzb|com.gnml.wmyzfsmzf.gwb", "fileName": "文明与征服120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.dpcq.*", "fileName": "斗破苍穹：巅峰对决.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.dldllhsjqdtyxmz37zgx2.xmz.*", "fileName": "斗罗大陆：猎魂世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.dldllove.*|com.ssr.mainland.channel.tiyan24.*", "fileName": "斗罗大陆：逆转时空.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.dldlhsdj.*", "fileName": "斗罗大陆：魂师对决120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.coco.chukong.llcy|com.coco.chukong.llcy.*", "fileName": "料理次元.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wth.thrud.*", "fileName": "斯露德.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tankionline.*", "fileName": "新3D坦克.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.ccz.*", "fileName": "新三国志曹操传.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.kaichen.xyttlj.*", "fileName": "新倚天屠龙记.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.xfrxxz.*", "fileName": "新凡人修仙传在3.0移速过慢解决横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.jxqy", "fileName": "新剑侠情缘.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.xtlbb", "fileName": "新天龙八部.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.firewick.p42.*|com.firewick.p42", "fileName": "新月同行.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pwrd.xxajh.*|com.tencent.tmgp.xxajh", "fileName": "新笑傲江湖.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wk.wjzj|com.tencent.tmgp.wjzjnew|com.wk.wjzj.*", "fileName": "无尽战记4.0闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.everadventure", "fileName": "无尽远征通知.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yhhy.whhx.*", "fileName": "无悔华夏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.longtugame.wszj.*", "fileName": "无神之界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ahlh.sgzhd.*", "fileName": "时光杂货店.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.hunter3|com.bilibili.hunter3.*", "fileName": "时空猎人3卡cg开设置横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.yongyong.mrzh|com.netease.mrzh.*|com.netease.mrzh.huangjia", "fileName": "明日之后闪退卡顿.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ahlh.mrdh.*", "fileName": "明日大亨.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hypergryph.arknights.*", "fileName": "明日方舟.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "明日方舟引擎升级引导更新.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.j1game.*|com.hermes.j1game", "fileName": "星球：重启PC.nmp"}, {"packageNameType": 1, "packageNamePattern": "cn.haoplay.game.and.elpis.*|cn.haoplay.game.and.elpis", "fileName": "星落.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xhqsshouyou.fm.ld", "fileName": "星骸骑士.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.meelive.ingkee", "fileName": "映客直播.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.DreamPlus.Cqfs.*", "fileName": "春秋封神.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jjwxc.reader", "fileName": "晋江.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.p6game", "fileName": "晶核.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.lj.ayzj.*", "fileName": "暗影战姬120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wanda.sf3.*|com.hahd.aygd3.*", "fileName": "暗影格斗3.nmp"}, {"packageNameType": 1, "packageNamePattern": "cn.xiaochuankeji.tieba", "fileName": "最右.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.chaoxing.mobile.*", "fileName": "最新版学习通闪退&位置签到方法横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.fmgame", "fileName": "最终幻想14水晶世界机型检测提醒.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.singyeagame.castlevaniatest", "fileName": "月夜狂想曲横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.youdao.dict", "fileName": "有道词典闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.weiku.zhaoge.*", "fileName": "朝歌.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gameduchy.jdzdDevelop|com.gameduchy.jdzd.bilibili", "fileName": "机动战队4.0无法打中文.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.jddsaef|com.netease.jddsaef.*|com.netease.g93na", "fileName": "机动都市阿尔法9横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.jddsaef|com.netease.jddsaef.*|com.netease.g93na", "fileName": "机动都市阿尔法横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.jddsaef.*", "fileName": "机动都市阿尔法雷电5异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.alicegearaegis", "fileName": "机甲爱丽丝.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.leiting.zjcs.*|com.m88.idle.*|com.m88.zjcs.*", "fileName": "杖剑传说.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.taiyouxi.tk2.*|com.tencent.tmgp.taiyouxi.tk2|com.taiyouxi.dl2", "fileName": "极无双2横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy4399.zjqz.*", "fileName": "枪战英雄按键优化教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.nexon.maplem.global", "fileName": "枫之谷M.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gzhc.mhwj.*", "fileName": "梦幻无间.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.my|com.netease.my.huawei|com.netease.my.uc|com.netease.my.nearme.gamecenter|com.netease.my.qihoo|com.netease.my.vivo|com.netease.my.mi|com.tencent.tmgp.mhxy.sqsy", "fileName": "梦幻西游.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.my.ewan.game.LD", "fileName": "梦幻西游雷电渠道停运.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.mhzxsy", "fileName": "梦幻诛仙官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.mxdl", "fileName": "梦想新大陆.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.aligames.sxx.*|com.aligames.szgd.*|com.m2.lingxi.szgd.*|com.tencent.tmgp.szgd", "fileName": "森之国度.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.plantsvszombies3.mengxing.*", "fileName": "植物大战僵尸3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ljhy.chun.*", "fileName": "椿之歌.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xunyou.rb", "fileName": "次元姬小说.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yoka.hlsgs.*", "fileName": "欢乐三国杀120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.qqgame.hlddz", "fileName": "欢乐斗地主.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xuejing.smallfish.*", "fileName": "欢乐钓鱼大师.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mlych.starlinear", "fileName": "欢迎来到梦乐园.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jingling.zgzz.*", "fileName": "止戈之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wxdmxsz01.*|com.wxdmxgz01.*", "fileName": "武侠大明星.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pwrd.wlwz.dangle.yueeyou.xmw", "fileName": "武林外传-雷电.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jianghe.qp.*", "fileName": "气泡星球.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yhd.*|com.chd.yhdxfc|com.qlyyd.*", "fileName": "永恒岛官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.yhxmz.cn|com.m37.dtszj.*|com.tencent.tmgp.dtszj", "fileName": "永恒纪元提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "cn.yonghui.hyd", "fileName": "永辉生活.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.qrzd|com.netease.qrzd.*|com.tencent.tmgp.yongyong.yydqrzd", "fileName": "永远的七日之都按键错位提示横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cis.jiangnan.*", "fileName": "江南百景图.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hjwordgames", "fileName": "沪江开心词场.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yqw.llfz.*", "fileName": "流浪方舟关root.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.cqyzs|com.fsyhj.mk4", "fileName": "浮生妖绘卷优化横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ea.gp.apexlegendsmobilefps", "fileName": "海外APEX手游.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.taobao.taobao", "fileName": "淘宝.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.syzjyyh.*", "fileName": "深渊战记.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yongshi.tenojo.ys.*|com.yongshi.tenojo.bilibili", "fileName": "深空之眼.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xd.WuLaLa|com.tencent.xishanju.xj4", "fileName": "游戏异常解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mywl.lhhj.bz|com.tanwan.rxhjly.ld|com.shenghe.wzzs.ld|com.mengzyx.ld|com.shenghe.kbcq.ld", "fileName": "游戏概率性闪退优化横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.ma84.*", "fileName": "游戏王：决斗链接.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.caike.lomo", "fileName": "潮玩宇宙.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.denachina.g13002010.*|com.tencent.tmgp.g13002010|com.tencent.tmgp.g13002010|com.denachina.g13002014.denacn", "fileName": "灌篮高手.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.com.gglslll", "fileName": "灌篮高手助手.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "火影忍者iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.hyrzol", "fileName": "火影忍者：忍者新时代120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hw.hcrglx.*", "fileName": "火柴人归来.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zywl.hcrwx.*", "fileName": "火柴人无限.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.heaven.*", "fileName": "炽焰天穹.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.lanjing.yanyu.lanjing", "fileName": "烟雨江湖3.0无法登陆.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.rxxqxm.ld", "fileName": "热血寻秦.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.bgame|com.hermes.bgame.*", "fileName": "热血街篮.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fengyanyunqi.sh.xcfy.*", "fileName": "烽烟云起.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.supercell.squadbusters", "fileName": "爆裂小队.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.blmn.*|com.jiansheng.blmn.bilibili.*", "fileName": "爆裂魔女按键使用技巧和120帧闪退解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ztgame.fir.qyc|com.ztgame.qyc.*", "fileName": "犬夜叉奈落之战提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.redfox", "fileName": "狐妖小红娘iOS.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.shiyongshi.slsk.*", "fileName": "狩猎时刻.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.koh.*|com.tencent.tmgp.eyou.lszw", "fileName": "猎手之王.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.AVALON|com.netease.lhjx.*|com.tencent.tmgp.lewu.lhjx", "fileName": "猎魂觉醒闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yc.lmyh.*", "fileName": "猎魔永恒横屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mumulmyz.ld", "fileName": "猎魔远征.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.tom.ld", "fileName": "猫和老鼠停运公告.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.saiyun.cat", "fileName": "猫咪公寓2攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tap4fun.ape.cn.*", "fileName": "猿族时代登录异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.stzb.uc", "fileName": "率土之滨九游渠道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.igg.android.lordsmobile_cn", "fileName": "王国纪元120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yingxiong.hero.ewan.game.LD|com.yingxiong.hero.mi|com.yingxiong.gw.Android.hero", "fileName": "王牌战争-文明重启提示.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.yingxiong.hero.*|com.yingxiong.hero|com.tencent.tmgp.wpzz", "fileName": "王牌战争：文明重启显卡异常提示.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.af", "fileName": "王牌战士闪退更新模拟器.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xrsr.wzzs", "fileName": "王者之师4.0闪退横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gzchukai.wzlr.*", "fileName": "王者猎人.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.wznba2", "fileName": "王者美职篮2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.sgame", "fileName": "王者荣耀卡启动.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "王者荣耀提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.gamehelper.smoba", "fileName": "王者营地.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.jiuwan.nszs.ld", "fileName": "玛法英雄.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wodi.who", "fileName": "玩吧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.indra.annulus", "fileName": "环形战争.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ztgame.bob", "fileName": "球球大作战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.dragon.read", "fileName": "番茄免费小说.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xs.fm", "fileName": "番茄畅听.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xzd.run.jskp.*", "fileName": "疾速酷跑.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.baiye", "fileName": "白夜极光.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.gate", "fileName": "白荆回廊.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.baidu.netdisk", "fileName": "百度网盘闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.yysbwp.*|com.tencent.tmgp.yysbwp", "fileName": "百闻牌宽屏+绿屏横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wanmei.zhuxian.ewan.game.LD|com.gbits.atm.qihoo.ewan.game.LD|com.netease.l10.ld|com.netease.gmdl.game.LD|com.ztgame.fir.ewan.game.LD|com.netease.sgrlz.ewan.game.LD|com.wali.ak.ewan.game.LD|com.qzgame.wjmtsj.ewan.game.LD|com.netease.zgz.ewan.game.LD|com.xljx.ewan.game.LD|com.netease.hwfz.ewan.game.LD|com.seasun.jxsj2.game.LD|com.WindValley.SwordSwing.ewan.game.LD|com.hjcj.ewan.game.LD|com.netease.soulofhunter.ewan.game.LD|com.netease.moba.ewan.game.LD|com.huyusoft.jdsg.ewan.game.LD|com.askj.jtyl2.ewan.game.LD|com.hxqst.ewan.game.LD|com.netease.h48.game.LD|com.netease.gd.ewan.game.LD|com.gn.scdl.ewan.game.LD|com.pwrd.lhj.ewan.game.LD|com.netease.gloryofempire.game.LD|com.netease.wecc.game.LD|com.ttjy.game.LD|com.netease.pes.game.LD|com.yingxiong.hqwm.game.LD|com.gaea.gjqt.ld|com.netease.mrzh.game.LD|com.greatefunhy.yzdfw.game.LD|com.zy.fwy.game.LD|com.netease.lztg.ld|com.netease.yqyn.ld|com.netease.lx7.ld|com.netease.hdjy.ld|com.lywl.xbxxz.ld|com.netease.bloomblade.ld|com.hero.lszt.ld|com.tnyoo.z.ld|com.ztgame.qyc.ld|com.jbdfw.game.LD|com.puppetsgame.miracing.game.LD|com.dea.xianxia.game.LD|com.linlong.game.LD|com.tsry.youda.ld|com.netease.nshhelper.ld|com.netease.ms3.ld|com.greatefunhy.sdgs.ld|com.duomi.zxdj.ld|com.maichi.wljj.ld|com.and.dawnofconquest.ld|com.netease.sword.ld|com.yxsoda.dqlm.ld|com.askj.fqcaMUB.yjlc.ld|com.leyo.dyfz.ld|com.netease.dtws.ld|com.xmkjgz.pmxxq.ld|com.planet.yujianjf.ld|com.haoyao.dgame.ld|com.netease.msws.ld|com.netease.lyzd.ld|com.colee.sjba.ld|com.game2.xiaoyao.ld|com.netease.wxzc.ld|com.kaopu.xcb.ld|com.mjxbyx.ld|com.dcgame.fjmz.ld|com.fkdpd.empire.ld|com.netease.pescm.ld|com.caohua.cathunter_client.ld|com.gyyx.dwb.fox.ld", "fileName": "益玩强制实名教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.able.wisdomtree.*", "fileName": "知到提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.YostarJP.BlueArchive", "fileName": "碧蓝档案攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.azurlane|com.bilibili.blhx.*|com.tencent.tmgp.bilibili.blhx", "fileName": "碧蓝航线官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.nvwa.ld", "fileName": "神仙道3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ahlh.syjy.ld", "fileName": "神域纪元.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.lingxigames.stgsl.*", "fileName": "神探诡事录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.road7.sqh5.*", "fileName": "神曲H5120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wanmei.mini.condor.*|com.tencent.tmgp.sdxl", "fileName": "神雕侠侣.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.smdl.qy.*", "fileName": "神魔大陆.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hdhd.cwwsw.ld", "fileName": "称王魏蜀吴.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zqpgzs.kzysqh.*|com.tencent.tmgp.kzysqh", "fileName": "空之要塞：启航.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.cf", "fileName": "穿越火线.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.nextjoy.stilla.ld", "fileName": "站之刃：幸存者.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.stove.epic7.google", "fileName": "第七史詩.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zlongame.cn.epicseven.*|com.tencent.tmgp.zlongame.cn.epicseven", "fileName": "第七史诗.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.stove.epic7.google", "fileName": "第七史诗攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.miaorui.d7hy.ld", "fileName": "第七幻域.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.dwrg", "fileName": "第五人格.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.dwrg.*", "fileName": "第五人格雷电五黑屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.hjol", "fileName": "红警OLiOS+金条问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.heitao.yhdzz.*", "fileName": "约战：精灵再临.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.funnygames.escape.zjy4", "fileName": "纸嫁衣4点击无响应.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.HoYoverse.Nap.*|com.miHoYo.Nap.*", "fileName": "绝区零.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.onethingcloud.android", "fileName": "网心云.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.yanxuan", "fileName": "网易严选.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.cloudmusic", "fileName": "网易云音乐.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.gl", "fileName": "网易大神.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jg.qyfhl.*|com.sgfyl.tt0528|com.wpjs.tt0228", "fileName": "群英风华录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.nikke", "fileName": "胜利女神：新的希望.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.pvz2hdtxyyb", "fileName": "腾讯植物大战僵尸2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.qqlive", "fileName": "腾讯视频.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tanwan.zyzrly.*", "fileName": "自由之刃.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.ffom", "fileName": "自由幻想提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.hhw", "fileName": "航海王壮志雄心.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.h1game.ld", "fileName": "航海王热血航线卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "cn.mobage.g12000128.ld", "fileName": "航海王：启航卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.joygames.hhwmxzz.*|joygames.hhwmxzz.*", "fileName": "航海王：梦想指针.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sjxh.skdzz.*", "fileName": "色块大作战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yuyanshine.etecn.*", "fileName": "艾塔纪元.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hunantv.imgo.activity", "fileName": "芒果TV安卓9卡加载.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.bloomblade.*|com.netease.bloomblade", "fileName": "花与剑.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.feimo.hazeworld.*", "fileName": "苍雾世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.egame", "fileName": "英雄联盟电竞经理.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mskj.yxlc.*", "fileName": "英雄连城120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.hyxd.*", "fileName": "荒野行动雷电9闪退卡视角解决方法横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.caohua.hymc.*", "fileName": "荒野迷城.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.manling.lkhx.*", "fileName": "蓝空幻想.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.RoamingStar.BlueArchive.*", "fileName": "蔚蓝档案.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.junhai.yzjq.aligames|com.tencent.tmgp.yzjq|com.junhai.yzjq.vivo|com.junhai.yzjq", "fileName": "蚁族崛起渠道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.s10.*", "fileName": "蛋仔滑滑.nmp"}, {"packageNameType": 1, "packageNamePattern": "fm.qingting.qtradio", "fileName": "蜻蜓FM闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.ztgame.jielan|com.ztgame.jl.uc", "fileName": "街篮按键无效解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.echo.lsf.*", "fileName": "西行纪：燃魂.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ppyuewan.peiwan", "fileName": "西西语音.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tianyan.jyxcl.*", "fileName": "解忧小村落.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.siva.project.x2", "fileName": "解神者X2横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.touchsprite.android", "fileName": "触动精灵.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hupu.shihuo", "fileName": "识货.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.wanmei.zhuxian.*", "fileName": "诛仙.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pwrd.zx2.*", "fileName": "诛仙2.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.android.googleinstaller", "fileName": "谷歌安装器.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.komoe.kmumamusumegp|jp.co.cygames.umamusume", "fileName": "赛马娘(繁中日服)官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xf.cxjq.*", "fileName": "超星崛起120帧教程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bje.cnlcc.*", "fileName": "超能力冲刺.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pi.czrxdfirst.*", "fileName": "超自然行动组.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.biubiux2.theleaper.*", "fileName": "跃迁旅人.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zloong.im.*", "fileName": "踏风行显示异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jinke.jjdzc.*", "fileName": "进击的战场.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zxyx.jjdqs.ld", "fileName": "进击的骑士雷电5支付闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.shyl.ygly.*", "fileName": "远古灵域.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.nhyzbf.mscsld.yyjsqq", "fileName": "迷失传说卡界面解决横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.nshm|com.netease.nshm.*", "fileName": "逆水寒.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tuniu.app.ui", "fileName": "途牛旅游闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.aliyun.tongyi", "fileName": "通义.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "部落冲突.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.supercell.clashofclans", "fileName": "部落冲突9.1挂机卡死问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.supercell.clashofclans|com.supercell.clashofclans.*", "fileName": "部落冲突国际服.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.jybdd.cgatts.*|com.bilibili.aetatis.*|com.cgmoba.*", "fileName": "重构：阿塔提斯按键偏移问题.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.deadcells.*", "fileName": "重生细胞横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.wxzc|com.netease.wxzc.*|com.tencent.tmgp.eyou.zzsz", "fileName": "重装上阵方块透明解决方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmaoe", "fileName": "重返帝国挂机卡死横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.shenlan.m.reverse1999.*", "fileName": "重返未来：1999.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tap4fun.brutalage_test", "fileName": "野蛮时代.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "金铲铲之战 - 巨龙之巢.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "金铲铲之战手游模式.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "金铲铲之战调查问卷.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xinkao.jiaxia<PERSON><PERSON>an.*", "fileName": "鑫考云校园提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xd.ssrpg", "fileName": "铃兰之剑：为这和平的世界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.joygames.yhjjx.*", "fileName": "银河境界线.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.zhjtsww.*|com.xinyuan.zhj.*|com.tencent.tmgp.zhjtsww1|com.zhjtswwxb.bdpzjt.*", "fileName": "镇魂街：天生为王.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.br.*", "fileName": "镖人换4.0横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ydzh.jhywl", "fileName": "镜花异闻录3.0动画黑屏横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.syyx.cahx.*|com.cahx.gw1", "fileName": "长安幻想官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.papegames.nn4.*", "fileName": "闪耀暖暖.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.umamusu.*|com.tencent.tmgp.bilibili.umamusu", "fileName": "闪耀！优俊少女.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xjbkw.wsh.*", "fileName": "问山海.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gbits.atm.*|com.tencent.tmgp.gbits.atm", "fileName": "问道.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "问道官服.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.gbits.atm.huawei|com.gbits.atm.vivo|com.gbits.atm.nearme.gamecenter|com.gbits.atm.uc|com.gbits.atm.mi|com.tencent.tmgp.gbits.atm|com.gbits.atm.guopan|com.gbits.atm.jys|com.gbits.atm.ewan|com.gbits.atm.neice|com.gbits.Asktao.Mobile|com.gbits.atm.qihoo|com.gbits.atm.ld", "fileName": "问道渠道服.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.taobao.idlefish", "fileName": "闲鱼异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.onmyoji.*|com.tencent.tmgp.yys.zqb|com.netease.onmyoji.netease_simulator", "fileName": "阴阳师.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.bgyey|com.netease.bgyey.*", "fileName": "阴阳师：妖怪小班竖屏提示横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.tmgp.vgame", "fileName": "雪鹰领主提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hi4fun.ldsg.*", "fileName": "零度曙光-高帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yimu.cszc.ld", "fileName": "雷电防骗指南.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fengchi.ldjx.*", "fileName": "雷电：觉醒.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.hermes.goda", "fileName": "雷索纳斯.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.mistsequencech.starlinear.*|com.starlinear.mistsequencech.*", "fileName": "雾境序列.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.shangyoo.neon|com.yhhy.neon.*", "fileName": "霓虹深渊：无限横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.cmge.bqyx.*", "fileName": "霸气英雄.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.frxy|com.netease.frxy.*", "fileName": "非人学园.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.frxy|com.netease.frxy.*", "fileName": "非人学园按键错误.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ourpalm.fnmzl.*|com.bilibili.fnmzl.*", "fileName": "非匿名指令.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.lx7.ld.*|com.netease.lx7", "fileName": "风云岛行动提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.xhalo.awindf.*", "fileName": "风色幻想命运传说横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fqclcw.jy.*", "fileName": "风起苍岚.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ss.android.lark", "fileName": "飞书.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.fdg.flashplay.farsee", "fileName": "飞智游戏厅连接手柄方法.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.swy", "fileName": "食物语iOS .nmp"}, {"packageNameType": 1, "packageNamePattern": "com.nextjoy.xlgs.*", "fileName": "驯龙高手：旅程.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.autonavi.minimap", "fileName": "高德地图无法登陆.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gnsbt.Shgame.*|com.sh.figurestory", "fileName": "高能手办团优化横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.gnyx", "fileName": "高能英雄.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.yunchang.dmc.*|com.nebulajoy.nero.*", "fileName": "鬼泣：巅峰之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ml.mozhixq.*", "fileName": "魔之序曲120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.crossgate", "fileName": "魔力宝贝iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.giraffe.game.crossgate", "fileName": "魔力宝贝：复兴.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.game.crossgate.*|com.crossgate.kuaishou.*", "fileName": "魔力宝贝：旅人开高帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.com2us.smon.normal.freefull.google.kr.android.common", "fileName": "魔灵召唤官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.zgqyz", "fileName": "鸿图之下.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.toaa", "fileName": "黎明觉醒：生机.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tmsk.hazc.*", "fileName": "黑暗之潮120.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.superdream.blackmoon.mi|com.superdream.blackmoon.huawei|com.superdream.blackmoon.gamecenter|com.superdream.blackmoon.vivo|com.yixin.blackmoon.vivo|com.yixin.blackmoon.huawei|com.yixin.blackmoon.gamecenter|com.yixin.blackmoon.mi", "fileName": "黑月.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.uf.*|com.hczs.*|com.tencent.tmgp.uf", "fileName": "黑潮之上横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.pwrd.secret.*|com.honour.freedom.secret", "fileName": "黑猫奇闻社120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tiancity.blackbeacon.*|com.tencent.blackbeacon", "fileName": "黑色信标.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ztgame.zdfz.*", "fileName": "龙与世界的尽头.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zlongame.lzgwy.*", "fileName": "龙之国物语.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.wod", "fileName": "龙之谷2.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.tmgp.dragonnest", "fileName": "龙之谷提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.lzhx", "fileName": "龙族幻想.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.tencent.lzhx", "fileName": "龙族幻想提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.zulong.drc.*", "fileName": "龙族：卡塞尔之门.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.babeltime.longzhu.*", "fileName": "龙珠战力之巅.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.dca.lzjxjzyz.jh", "fileName": "龙珠觉醒-buff渠道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.dca.lzjx.ld", "fileName": "龙珠觉醒无法更新提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.lszz.*|com.jingxiu.lszz.*", "fileName": "龙石战争.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.sy.lqsxy.*", "fileName": "龙骑士学园.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.gravity.ragnarokorigin.aos", "fileName": "라그나로크오리진.nmp"}]}