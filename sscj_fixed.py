#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版三实查询暂无图片人员采集脚本
- 修复返回逻辑问题
- 只采集暂无图片人员
- 完善错误处理
"""

import uiautomator2 as u2
import pandas as pd
import os
import time
import re
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")
ERROR_LOG = os.path.join(RESULT_DIR, "error.log")

# 初始化
d = u2.connect()

def init_files():
    """初始化文件和目录"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')

def log_error(msg):
    """记录错误日志"""
    with open(ERROR_LOG, 'a', encoding='utf-8') as f:
        f.write(f"[{datetime.now()}] {msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        xml = d.dump_hierarchy()
        texts = re.findall(r'text="([^"]*)"', xml)
        return [text for text in texts if text.strip()]
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def is_house_list_page():
    """判断是否在房屋列表页"""
    try:
        texts = get_page_text()
        # 房屋列表页的特征：包含"共有"和"条数据"
        has_data_count = any("共有" in text and "条数据" in text for text in texts)
        # 或者包含搜索按钮
        has_search = any("搜索" in text for text in texts)
        return has_data_count or has_search
    except:
        return False

def is_house_detail_page():
    """判断是否在房屋详情页"""
    try:
        texts = get_page_text()
        # 房屋详情页的特征：包含"实有房屋"、"实有单位"、"实有人口"
        return any("实有房屋" in text or "实有单位" in text or "实有人口" in text for text in texts)
    except:
        return False

def is_people_list_page():
    """判断是否在人员列表页"""
    try:
        texts = get_page_text()
        # 人员列表页的特征：包含"暂无图片"或"共有"+"条数据"（人员数据）
        return any("暂无图片" in text for text in texts) or any("名：" in text for text in texts)
    except:
        return False

def safe_return_to_house_list():
    """安全返回到房屋列表页"""
    max_attempts = 5
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"[INFO] 尝试返回房屋列表页 (第{attempt}次)")
        
        if is_house_list_page():
            print("[INFO] 已在房屋列表页")
            return True
        
        if is_people_list_page():
            print("[INFO] 在人员列表页，返回房屋详情页")
            d.press("back")
            time.sleep(2)
            continue
        
        if is_house_detail_page():
            print("[INFO] 在房屋详情页，返回房屋列表页")
            d.press("back")
            time.sleep(2)
            continue
        
        # 如果都不是，尝试返回
        print("[INFO] 页面状态未知，尝试返回")
        d.press("back")
        time.sleep(2)
    
    print("[WARNING] 无法返回到房屋列表页")
    return False

def extract_no_photo_people():
    """只提取暂无图片的人员信息"""
    try:
        texts = get_page_text()
        people = []
        
        # 首先检查页面是否包含"暂无图片"
        if not any("暂无图片" in text for text in texts):
            return []
        
        print("[INFO] 发现'暂无图片'人员，开始提取...")
        
        # 找到"暂无图片"的索引位置
        no_photo_indices = []
        for i, text in enumerate(texts):
            if "暂无图片" in text:
                no_photo_indices.append(i)
        
        for no_photo_idx in no_photo_indices:
            # 在"暂无图片"前后查找人员信息
            search_start = max(0, no_photo_idx - 15)
            search_end = min(len(texts), no_photo_idx + 15)
            
            person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": "", "备注": "暂无图片"}
            
            for j in range(search_start, search_end):
                text = texts[j]
                
                # 查找姓名
                if ("名：" in text or "名:" in text) and not person["姓名"]:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    if name_part.strip() and len(name_part.strip()) <= 10:
                        person["姓名"] = name_part.strip()
                        print(f"[DEBUG] 找到暂无图片人员姓名: {person['姓名']}")
                
                # 查找身份证号
                if re.search(r'\d{18}', text) and not person["身份证号"]:
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person["身份证号"] = id_match.group(1)
                        print(f"[DEBUG] 找到身份证: {person['身份证号']}")
                
                # 查找电话
                if re.search(r'1[3-9]\d{9}', text) and not person["联系电话"]:
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person["联系电话"] = phone_match.group(1)
                        print(f"[DEBUG] 找到电话: {person['联系电话']}")
                
                # 查找更新时间
                if re.search(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', text) and not person["更新时间"]:
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person["更新时间"] = time_match.group(1)
                        print(f"[DEBUG] 找到更新时间: {person['更新时间']}")
            
            # 如果找到了完整信息
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 成功提取暂无图片人员: {person['姓名']} - {person['身份证号']}")
        
        return people
    except Exception as e:
        print(f"[ERROR] 提取暂无图片人员信息失败: {e}")
        return []

def save_people_data(people, house_address):
    """保存人员数据"""
    if not people:
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        saved_count = 0
        
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": person.get('备注', '暂无图片')
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存暂无图片人员: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    except Exception as e:
        print(f"[ERROR] 保存数据失败: {e}")
        return 0

def process_house_people(house_address):
    """处理房屋内的暂无图片人员信息"""
    print(f"[INFO] 开始采集房屋暂无图片人员")
    
    total_saved = 0
    scroll_count = 0
    max_scrolls = 10
    processed_people = set()
    
    while scroll_count < max_scrolls:
        # 提取当前页面的暂无图片人员信息
        people = extract_no_photo_people()
        
        if people:
            # 过滤重复人员
            new_people = []
            for person in people:
                person_key = f"{person['姓名']}_{person['身份证号']}"
                if person_key not in processed_people:
                    new_people.append(person)
                    processed_people.add(person_key)
            
            if new_people:
                saved = save_people_data(new_people, house_address)
                total_saved += saved
                print(f"[INFO] 本轮保存 {saved} 个暂无图片人员")
        
        # 检查是否显示"没有更多数据了"
        texts = get_page_text()
        if any("没有更多数据" in text for text in texts):
            print("[INFO] 检测到'没有更多数据了'，停止滚动")
            break
        
        # 向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                print(f"[INFO] 向下滚动查看更多暂无图片人员 ({scroll_count + 1}/{max_scrolls})")
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                break
        except:
            break
    
    print(f"[INFO] 房屋采集完成，共保存 {total_saved} 个暂无图片人员")
    return total_saved

def get_house_address():
    """获取房屋地址"""
    try:
        texts = get_page_text()
        for text in texts:
            if text.startswith("云南省") and len(text) > 15:
                return text
            elif "小组" in text and len(text) > 10:
                return text
        return "地址识别失败"
    except:
        return "地址识别失败"

def process_single_house(house_index):
    """处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理第 {house_index + 1} 个房屋 ===")

        # 确保在房屋列表页
        if not is_house_list_page():
            print("[WARNING] 不在房屋列表页，无法处理房屋")
            return False

        # 查找房屋项目并点击
        house_elements = d.xpath('//android.widget.LinearLayout').all()
        house_elements = [c for c in house_elements if c.bounds[3] - c.bounds[1] > 100]

        if house_index >= len(house_elements):
            print("[WARNING] 房屋索引超出范围")
            return False

        # 点击房屋项目
        target_element = house_elements[house_index]
        print(f"[DEBUG] 点击房屋项目，边界: {target_element.bounds}")
        target_element.click()
        time.sleep(3)

        # 检查是否进入了房屋详情页
        if not is_house_detail_page():
            print("[WARNING] 未进入房屋详情页")
            safe_return_to_house_list()
            return False

        # 获取房屋地址
        house_address = get_house_address()
        print(f"[INFO] 房屋地址: {house_address}")

        # 点击实有人口
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到'实有人口'按钮")
            safe_return_to_house_list()
            return False

        d(text="实有人口").click()
        time.sleep(3)

        # 检查是否进入人员列表页
        if not is_people_list_page():
            print("[WARNING] 未进入人员列表页")
            safe_return_to_house_list()
            return False

        # 采集暂无图片人员信息
        total_saved = process_house_people(house_address)

        # 安全返回房屋列表页
        success = safe_return_to_house_list()
        if not success:
            print("[WARNING] 返回房屋列表页失败")

        return True

    except Exception as e:
        error_msg = f"处理房屋 {house_index + 1} 失败: {e}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)

        # 尝试返回房屋列表
        safe_return_to_house_list()
        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询暂无图片人员采集...")

    # 初始化
    init_files()

    # 启动应用
    print("[INFO] 启动应用...")
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(10)

    time.sleep(5)

    # 点击搜索显示房屋列表
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)

    # 确保在房屋列表页
    if not is_house_list_page():
        print("[ERROR] 未能进入房屋列表页")
        return

    processed_count = 0
    house_index = 0
    max_houses = 100  # 最大处理房屋数量

    while processed_count < max_houses:
        # 确保在房屋列表页
        if not is_house_list_page():
            print("[WARNING] 不在房屋列表页，可能已处理完所有房屋")
            break

        # 处理当前房屋
        success = process_single_house(house_index)
        if success:
            processed_count += 1

        house_index += 1
        time.sleep(2)  # 间隔时间

        # 如果连续失败多次，可能需要滚动到下一页
        if house_index % 10 == 0:
            print("[INFO] 尝试滚动到下一页房屋")
            try:
                d(scrollable=True).scroll.vert.forward(steps=5)
                time.sleep(3)
            except:
                print("[INFO] 无法滚动，可能已到达底部")
                break

    print(f"\n[INFO] 采集完成！共处理 {processed_count} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

if __name__ == "__main__":
    main()
