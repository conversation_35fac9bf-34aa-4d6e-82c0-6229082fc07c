#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三实查询数据采集脚本 - 重新设计版本
功能：自动采集房屋内人员信息
"""

import uiautomator2 as u2
import time
import pandas as pd
import os
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "com.founder.police.sscx"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")

# 初始化设备连接
d = u2.connect()

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def log_error(error_msg):
    """记录错误日志"""
    error_file = os.path.join(RESULT_DIR, "error.log")
    with open(error_file, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {error_msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        # 获取页面XML
        xml = d.dump_hierarchy()
        
        # 简单解析文本内容
        import re
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def extract_house_address():
    """提取房屋地址"""
    try:
        texts = get_page_text()
        
        # 查找地址信息
        for text in texts:
            if "云南省" in text and len(text) > 15:
                return text
            elif "小组" in text and len(text) > 10:
                return text
        
        return "地址未识别"
    except Exception as e:
        print(f"[ERROR] 提取地址失败: {e}")
        return "地址提取失败"

def extract_person_info():
    """提取人员信息"""
    try:
        texts = get_page_text()
        people = []
        
        # 查找人员信息模式
        import re
        
        i = 0
        while i < len(texts):
            text = texts[i]
            
            # 查找姓名模式：名：xxx 或 姓名：xxx
            name_match = re.search(r'(?:姓\s*)?名\s*[：:]\s*([^\s\d]+)', text)
            if name_match:
                person = {
                    "姓名": name_match.group(1).strip(),
                    "身份证号": "",
                    "联系电话": "",
                    "更新时间": ""
                }
                
                # 在附近文本中查找其他信息
                search_range = range(max(0, i-5), min(len(texts), i+10))
                
                for j in search_range:
                    current_text = texts[j]
                    
                    # 身份证号 - 18位数字
                    if not person["身份证号"]:
                        id_match = re.search(r'(\d{18})', current_text)
                        if id_match:
                            person["身份证号"] = id_match.group(1)
                    
                    # 联系电话 - 11位手机号
                    if not person["联系电话"]:
                        phone_match = re.search(r'(1[3-9]\d{9})', current_text)
                        if phone_match:
                            person["联系电话"] = phone_match.group(1)
                    
                    # 更新时间
                    if not person["更新时间"]:
                        time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', current_text)
                        if time_match:
                            person["更新时间"] = time_match.group(1)
                
                # 如果找到了基本信息就添加
                if person["姓名"]:
                    people.append(person)
                    print(f"[INFO] 找到人员: {person['姓名']} - {person['身份证号']}")
            
            i += 1
        
        return people
    except Exception as e:
        print(f"[ERROR] 提取人员信息失败: {e}")
        return []

def save_people_data(people, house_address):
    """保存人员数据到CSV"""
    if not people:
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        
        saved_count = 0
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                # 添加新记录
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": "自动采集"
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    
    except Exception as e:
        log_error(f"保存数据失败: {e}")
        return 0

def process_house_people(house_address):
    """处理房屋内的人员信息"""
    print(f"[INFO] 开始采集房屋人员: {house_address}")
    
    total_saved = 0
    scroll_count = 0
    max_scrolls = 8
    
    while scroll_count < max_scrolls:
        # 提取当前页面的人员信息
        people = extract_person_info()
        
        if people:
            saved = save_people_data(people, house_address)
            total_saved += saved
            print(f"[INFO] 本轮保存 {saved} 个人员")
        
        # 尝试向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                break
        except:
            break
    
    print(f"[INFO] 房屋采集完成，共保存 {total_saved} 个人员")
    return total_saved

def process_single_house(house_index):
    """处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理第 {house_index + 1} 个房屋 ===")
        
        # 查找房屋项目并点击
        # 根据您的截图，房屋项目可能是包含图片和文本的容器
        house_elements = d.xpath('//android.widget.LinearLayout').all()
        
        if house_index >= len(house_elements):
            print("[WARNING] 房屋索引超出范围")
            return False
        
        # 点击房屋项目
        house_elements[house_index].click()
        time.sleep(3)
        
        # 检查是否进入了房屋详情页
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到实有人口按钮，可能未成功进入房屋详情")
            d.press("back")
            time.sleep(2)
            return False
        
        # 获取房屋地址
        house_address = extract_house_address()
        print(f"[INFO] 房屋地址: {house_address}")
        
        # 点击实有人口
        d(text="实有人口").click()
        time.sleep(3)
        
        # 采集人员信息
        total_saved = process_house_people(house_address)
        
        # 返回房屋列表
        print("[INFO] 返回房屋列表")
        d.press("back")  # 返回房屋详情页
        time.sleep(1)
        d.press("back")  # 返回房屋列表页
        time.sleep(2)
        
        return True
        
    except Exception as e:
        error_msg = f"处理房屋 {house_index + 1} 失败: {e}\n{traceback.format_exc()}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)
        
        # 尝试返回房屋列表
        try:
            for _ in range(3):
                d.press("back")
                time.sleep(1)
                if d(text="搜索").exists:
                    break
        except:
            pass
        
        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询数据采集...")
    
    # 初始化
    init_csv()
    
    # 启动应用
    d.app_start(APP_PACKAGE)
    time.sleep(15)
    
    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(5)
    
    time.sleep(3)
    
    # 点击搜索
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)
    else:
        print("[ERROR] 未找到搜索按钮")
        return
    
    # 开始处理房屋
    house_index = 0
    max_houses = 100  # 最大处理房屋数量
    
    while house_index < max_houses:
        success = process_single_house(house_index)
        
        if not success:
            print(f"[INFO] 处理房屋 {house_index + 1} 失败，尝试下一个")
        
        house_index += 1
        time.sleep(2)
        
        # 检查是否还在房屋列表页
        if not d(text="搜索").exists:
            print("[WARNING] 不在房屋列表页，可能已处理完所有房屋")
            break
    
    print(f"[INFO] 采集完成！共处理 {house_index} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

if __name__ == "__main__":
    main()
