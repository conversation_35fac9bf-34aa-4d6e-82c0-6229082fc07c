#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三实查询数据采集脚本 - 重新设计版本
功能：自动采集房屋内人员信息
"""

import uiautomator2 as u2
import time
import pandas as pd
import os
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "com.founder.police.sscx"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")

# 初始化设备连接
d = u2.connect()

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def log_error(error_msg):
    """记录错误日志"""
    error_file = os.path.join(RESULT_DIR, "error.log")
    with open(error_file, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {error_msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        # 获取页面XML
        xml = d.dump_hierarchy()
        
        # 简单解析文本内容
        import re
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def extract_house_address():
    """提取房屋地址"""
    try:
        texts = get_page_text()
        
        # 查找地址信息
        for text in texts:
            if "云南省" in text and len(text) > 15:
                return text
            elif "小组" in text and len(text) > 10:
                return text
        
        return "地址未识别"
    except Exception as e:
        print(f"[ERROR] 提取地址失败: {e}")
        return "地址提取失败"

def extract_person_info():
    """提取人员信息"""
    try:
        texts = get_page_text()
        people = []

        print(f"[DEBUG] 页面文本内容: {texts[:20]}")  # 调试输出

        # 查找人员信息模式
        import re

        # 方法1：查找完整的人员信息块
        all_text = " ".join(texts)

        # 查找姓名模式 - 根据截图，格式是 "名：刘堤"
        name_matches = re.finditer(r'名\s*[：:]\s*([^\s\d，。！？；：""''（）【】]+)', all_text)

        for name_match in name_matches:
            person = {
                "姓名": name_match.group(1).strip(),
                "身份证号": "",
                "联系电话": "",
                "更新时间": ""
            }

            print(f"[DEBUG] 找到姓名: {person['姓名']}")

            # 在整个文本中查找对应的其他信息
            # 身份证号 - 18位数字
            id_matches = re.findall(r'(\d{18})', all_text)
            if id_matches:
                person["身份证号"] = id_matches[0]  # 取第一个18位数字
                print(f"[DEBUG] 找到身份证: {person['身份证号']}")

            # 联系电话 - 11位手机号
            phone_matches = re.findall(r'(1[3-9]\d{9})', all_text)
            if phone_matches:
                person["联系电话"] = phone_matches[0]  # 取第一个手机号
                print(f"[DEBUG] 找到电话: {person['联系电话']}")

            # 更新时间
            time_matches = re.findall(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', all_text)
            if time_matches:
                person["更新时间"] = time_matches[0]  # 取第一个时间
                print(f"[DEBUG] 找到更新时间: {person['更新时间']}")

            # 如果找到了基本信息就添加
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 成功提取人员: {person['姓名']} - {person['身份证号']}")

        # 方法2：如果方法1没找到，尝试逐行分析
        if not people:
            print("[DEBUG] 方法1未找到人员，尝试逐行分析...")
            current_person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

            for text in texts:
                # 查找姓名
                if "名：" in text or "名:" in text:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    if name_part.strip():
                        current_person["姓名"] = name_part.strip()
                        print(f"[DEBUG] 逐行找到姓名: {current_person['姓名']}")

                # 查找身份证号
                id_match = re.search(r'(\d{18})', text)
                if id_match:
                    current_person["身份证号"] = id_match.group(1)
                    print(f"[DEBUG] 逐行找到身份证: {current_person['身份证号']}")

                # 查找电话
                phone_match = re.search(r'(1[3-9]\d{9})', text)
                if phone_match:
                    current_person["联系电话"] = phone_match.group(1)
                    print(f"[DEBUG] 逐行找到电话: {current_person['联系电话']}")

                # 查找更新时间
                time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                if time_match:
                    current_person["更新时间"] = time_match.group(1)
                    print(f"[DEBUG] 逐行找到更新时间: {current_person['更新时间']}")

            # 如果找到了完整信息
            if current_person["姓名"] and current_person["身份证号"]:
                people.append(current_person)
                print(f"[INFO] 逐行分析成功提取人员: {current_person['姓名']} - {current_person['身份证号']}")

        return people
    except Exception as e:
        print(f"[ERROR] 提取人员信息失败: {e}")
        import traceback
        print(f"[ERROR] 详细错误: {traceback.format_exc()}")
        return []

def save_people_data(people, house_address):
    """保存人员数据到CSV"""
    if not people:
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        
        saved_count = 0
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                # 添加新记录
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": "自动采集"
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    
    except Exception as e:
        log_error(f"保存数据失败: {e}")
        return 0

def process_house_people(house_address):
    """处理房屋内的人员信息"""
    print(f"[INFO] 开始采集房屋人员: {house_address}")

    total_saved = 0
    scroll_count = 0
    max_scrolls = 10
    processed_people = set()  # 记录已处理的人员，避免重复

    while scroll_count < max_scrolls:
        # 提取当前页面的人员信息
        people = extract_person_info()

        if people:
            # 过滤掉已处理的人员
            new_people = []
            for person in people:
                person_key = f"{person['姓名']}_{person['身份证号']}"
                if person_key not in processed_people:
                    new_people.append(person)
                    processed_people.add(person_key)

            if new_people:
                saved = save_people_data(new_people, house_address)
                total_saved += saved
                print(f"[INFO] 本轮保存 {saved} 个新人员")
            else:
                print("[INFO] 本轮没有新人员")

        # 检查是否显示"没有更多数据了"
        texts = get_page_text()
        if any("没有更多数据" in text for text in texts):
            print("[INFO] 检测到'没有更多数据了'，停止滚动")
            break

        # 尝试向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                print(f"[INFO] 向下滚动查看更多人员 ({scroll_count + 1}/{max_scrolls})")
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                print("[INFO] 页面不可滚动，停止")
                break
        except Exception as e:
            print(f"[INFO] 滚动失败: {e}")
            break

    print(f"[INFO] 房屋采集完成，共保存 {total_saved} 个人员")
    return total_saved

def process_single_house(house_index):
    """处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理第 {house_index + 1} 个房屋 ===")
        
        # 查找房屋项目并点击
        # 根据您的截图，房屋项目可能是包含图片和文本的容器
        # 尝试多种方式查找房屋项目
        house_elements = []

        # 方法1：查找包含房屋信息的容器
        containers = d.xpath('//android.widget.LinearLayout').all()
        # 过滤出可能是房屋项目的容器（有一定高度的）
        house_elements = [c for c in containers if c.bounds[3] - c.bounds[1] > 100]

        # 方法2：如果方法1没找到，尝试查找图片元素
        if not house_elements:
            house_elements = d.xpath('//android.widget.ImageView').all()
            house_elements = [img for img in house_elements if img.bounds[2] - img.bounds[0] > 50]

        print(f"[DEBUG] 找到 {len(house_elements)} 个可能的房屋项目")

        if house_index >= len(house_elements):
            print("[WARNING] 房屋索引超出范围")
            return False

        # 点击房屋项目
        target_element = house_elements[house_index]
        print(f"[DEBUG] 点击房屋项目，边界: {target_element.bounds}")
        target_element.click()
        time.sleep(3)
        
        # 检查是否进入了房屋详情页
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到实有人口按钮，可能未成功进入房屋详情")
            d.press("back")
            time.sleep(2)
            return False
        
        # 获取房屋地址
        house_address = extract_house_address()
        print(f"[INFO] 房屋地址: {house_address}")
        
        # 点击实有人口
        d(text="实有人口").click()
        time.sleep(3)
        
        # 采集人员信息
        total_saved = process_house_people(house_address)
        
        # 返回房屋列表 - 从实有人口页面只需要一次back
        print("[INFO] 返回房屋列表")
        d.press("back")  # 从实有人口页面返回到房屋详情页
        time.sleep(2)

        # 检查是否在房屋详情页，如果是则再返回一次到房屋列表
        if d(text="实有房屋").exists or d(text="实有单位").exists or d(text="实有人口").exists:
            print("[INFO] 在房屋详情页，再次返回到房屋列表")
            d.press("back")  # 从房屋详情页返回到房屋列表页
            time.sleep(2)
        
        return True
        
    except Exception as e:
        error_msg = f"处理房屋 {house_index + 1} 失败: {e}\n{traceback.format_exc()}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)
        
        # 尝试返回房屋列表
        try:
            for _ in range(3):
                d.press("back")
                time.sleep(1)
                if d(text="搜索").exists:
                    break
        except:
            pass
        
        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询数据采集...")
    
    # 初始化
    init_csv()
    
    # 启动应用
    d.app_start(APP_PACKAGE)
    time.sleep(15)
    
    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(5)
    
    time.sleep(3)
    
    # 点击搜索
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)
    else:
        print("[ERROR] 未找到搜索按钮")
        return
    
    # 开始处理房屋
    house_index = 0
    max_houses = 100  # 最大处理房屋数量
    
    while house_index < max_houses:
        success = process_single_house(house_index)
        
        if not success:
            print(f"[INFO] 处理房屋 {house_index + 1} 失败，尝试下一个")
        
        house_index += 1
        time.sleep(2)
        
        # 检查是否还在房屋列表页
        if not d(text="搜索").exists:
            print("[WARNING] 不在房屋列表页，可能已处理完所有房屋")
            break
    
    print(f"[INFO] 采集完成！共处理 {house_index} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

if __name__ == "__main__":
    main()
