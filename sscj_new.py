#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三实查询数据采集脚本 - 重新设计版本
功能：自动采集房屋内人员信息
"""

import uiautomator2 as u2
import time
import pandas as pd
import os
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "com.founder.police.sscx"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")

# 初始化设备连接
d = u2.connect()

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def log_error(error_msg):
    """记录错误日志"""
    error_file = os.path.join(RESULT_DIR, "error.log")
    with open(error_file, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {error_msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        # 获取页面XML
        xml = d.dump_hierarchy()
        
        # 简单解析文本内容
        import re
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def extract_house_address():
    """提取房屋地址"""
    try:
        texts = get_page_text()
        
        # 查找地址信息
        for text in texts:
            if "云南省" in text and len(text) > 15:
                return text
            elif "小组" in text and len(text) > 10:
                return text
        
        return "地址未识别"
    except Exception as e:
        print(f"[ERROR] 提取地址失败: {e}")
        return "地址提取失败"

def extract_no_photo_people():
    """只提取暂无图片的人员信息"""
    try:
        texts = get_page_text()
        people = []

        # 首先检查页面是否包含"暂无图片"
        if not any("暂无图片" in text for text in texts):
            return []

        print("[INFO] 发现'暂无图片'人员，开始提取...")

        # 找到所有"暂无图片"的索引位置
        no_photo_indices = []
        for i, text in enumerate(texts):
            if "暂无图片" in text:
                no_photo_indices.append(i)

        print(f"[DEBUG] 找到 {len(no_photo_indices)} 个'暂无图片'位置")

        import re

        for no_photo_idx in no_photo_indices:
            # 在"暂无图片"前后查找人员信息
            search_start = max(0, no_photo_idx - 15)
            search_end = min(len(texts), no_photo_idx + 15)

            person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": "", "备注": "暂无图片"}

            # 在搜索范围内查找人员信息
            for j in range(search_start, search_end):
                text = texts[j]

                # 查找姓名 - 格式：名：xxx
                if ("名：" in text or "名:" in text) and not person["姓名"]:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    name_clean = name_part.strip()
                    # 验证姓名合理性（2-10个字符，主要是中文）
                    if 2 <= len(name_clean) <= 10 and not any(char.isdigit() for char in name_clean):
                        person["姓名"] = name_clean
                        print(f"[DEBUG] 找到暂无图片人员姓名: {person['姓名']}")

                # 查找身份证号 - 18位数字
                if not person["身份证号"]:
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person["身份证号"] = id_match.group(1)
                        print(f"[DEBUG] 找到身份证: {person['身份证号']}")

                # 查找联系电话 - 11位手机号
                if not person["联系电话"]:
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person["联系电话"] = phone_match.group(1)
                        print(f"[DEBUG] 找到电话: {person['联系电话']}")

                # 查找更新时间
                if not person["更新时间"]:
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person["更新时间"] = time_match.group(1)
                        print(f"[DEBUG] 找到更新时间: {person['更新时间']}")

            # 如果找到了基本信息就添加
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 成功提取暂无图片人员: {person['姓名']} - {person['身份证号']}")
            else:
                print(f"[DEBUG] 暂无图片人员信息不完整: 姓名={person['姓名']}, 身份证={person['身份证号']}")

        return people
    except Exception as e:
        print(f"[ERROR] 提取暂无图片人员信息失败: {e}")
        return []

def save_people_data(people, house_address):
    """保存人员数据到CSV"""
    if not people:
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        
        saved_count = 0
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                # 添加新记录
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": person.get('备注', '暂无图片')  # 添加暂无图片备注
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存暂无图片人员: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    
    except Exception as e:
        log_error(f"保存数据失败: {e}")
        return 0

def process_house_people(house_address):
    """处理房屋内的暂无图片人员信息"""
    print(f"[INFO] 开始采集房屋暂无图片人员")

    total_saved = 0
    scroll_count = 0
    max_scrolls = 8
    processed_people = set()  # 记录已处理的人员，避免重复
    no_data_count = 0  # 连续没有数据的次数

    while scroll_count < max_scrolls:
        # 提取当前页面的暂无图片人员信息
        people = extract_no_photo_people()

        if people:
            no_data_count = 0  # 重置无数据计数
            # 过滤掉已处理的人员
            new_people = []
            for person in people:
                person_key = f"{person['姓名']}_{person['身份证号']}"
                if person_key not in processed_people:
                    new_people.append(person)
                    processed_people.add(person_key)

            if new_people:
                saved = save_people_data(new_people, house_address)
                total_saved += saved
                print(f"[INFO] 本轮保存 {saved} 个暂无图片人员")
            else:
                print("[INFO] 本轮没有新的暂无图片人员")
        else:
            no_data_count += 1
            print(f"[INFO] 本轮未发现暂无图片人员 ({no_data_count}/3)")

        # 检查是否显示"没有更多数据了"
        texts = get_page_text()
        if any("没有更多数据" in text for text in texts):
            print("[INFO] 检测到'没有更多数据了'，停止滚动")
            break

        # 如果连续3次没有找到暂无图片人员，可能已经滚动完了
        if no_data_count >= 3:
            print("[INFO] 连续多次未发现暂无图片人员，可能已滚动完毕")
            break

        # 尝试向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                print(f"[INFO] 向下滚动查看更多 ({scroll_count + 1}/{max_scrolls})")
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                print("[INFO] 页面不可滚动，停止")
                break
        except Exception as e:
            print(f"[INFO] 滚动失败: {e}")
            break

    print(f"[INFO] 房屋采集完成，共保存 {total_saved} 个暂无图片人员")
    return total_saved

def process_single_house(house_index):
    """处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理第 {house_index + 1} 个房屋 ===")

        # 查找房屋项目
        house_elements = d.xpath('//android.widget.LinearLayout').all()
        # 过滤出可能是房屋项目的容器（有一定高度的）
        house_elements = [c for c in house_elements if c.bounds[3] - c.bounds[1] > 100]

        print(f"[DEBUG] 找到 {len(house_elements)} 个可能的房屋项目")

        if house_index >= len(house_elements):
            print("[WARNING] 房屋索引超出范围")
            return False

        # 点击房屋项目
        target_element = house_elements[house_index]
        print(f"[DEBUG] 点击房屋项目，边界: {target_element.bounds}")
        target_element.click()
        time.sleep(3)

        # 检查是否进入了房屋详情页
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到实有人口按钮，可能未成功进入房屋详情")
            d.press("back")
            time.sleep(2)
            return False

        # 获取房屋地址
        house_address = extract_house_address()
        print(f"[INFO] 房屋地址: {house_address}")

        # 点击实有人口
        d(text="实有人口").click()
        time.sleep(3)

        # 检查是否成功进入人员列表页
        time.sleep(2)  # 等待页面加载
        texts = get_page_text()
        if not any("暂无图片" in text or "共有" in text for text in texts):
            print("[WARNING] 可能未成功进入人员列表页")
            # 尝试返回
            d.press("back")
            time.sleep(1)
            if d(text="实有人口").exists:
                d.press("back")
                time.sleep(1)
            return False

        # 采集暂无图片人员信息
        total_saved = process_house_people(house_address)

        # 返回房屋列表 - 简化且可靠的返回逻辑
        print("[INFO] 返回房屋列表")
        d.press("back")  # 从人员列表返回房屋详情
        time.sleep(2)

        # 如果还能看到"实有人口"按钮，说明在房屋详情页，需要再返回一次
        if d(text="实有人口").exists:
            print("[INFO] 仍在房屋详情页，再次返回")
            d.press("back")  # 从房屋详情返回房屋列表
            time.sleep(2)

        print("[INFO] 返回操作完成")
        return True

    except Exception as e:
        error_msg = f"处理房屋 {house_index + 1} 失败: {e}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)

        # 简化的错误恢复：最多返回3次
        print("[INFO] 尝试返回房屋列表")
        for i in range(3):
            d.press("back")
            time.sleep(1)
            # 检查是否回到了房屋列表（有搜索按钮）
            if d(text="搜索").exists:
                print("[INFO] 成功返回房屋列表")
                break

        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询暂无图片人员采集...")

    # 初始化
    init_csv()

    # 启动应用
    d.app_start(APP_PACKAGE)
    time.sleep(15)

    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(5)

    time.sleep(3)

    # 点击搜索
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)
    else:
        print("[ERROR] 未找到搜索按钮")
        return

    # 开始处理房屋
    house_index = 0
    max_houses = 100  # 最大处理房屋数量
    processed_count = 0
    failed_count = 0
    max_failed = 5  # 最大连续失败次数

    print("[INFO] 开始处理房屋列表...")

    while house_index < max_houses and failed_count < max_failed:
        # 检查是否还在房屋列表页
        if not d(text="搜索").exists:
            print("[WARNING] 不在房屋列表页，可能已处理完所有房屋")
            break

        success = process_single_house(house_index)

        if success:
            processed_count += 1
            failed_count = 0  # 重置失败计数
            print(f"[INFO] 已成功处理 {processed_count} 个房屋")
        else:
            failed_count += 1
            print(f"[INFO] 处理房屋 {house_index + 1} 失败 (连续失败 {failed_count}/{max_failed})")

        house_index += 1
        time.sleep(2)

        # 每处理10个房屋，尝试滚动查看更多
        if house_index % 10 == 0:
            print("[INFO] 尝试滚动查看更多房屋")
            try:
                d(scrollable=True).scroll.vert.forward(steps=5)
                time.sleep(3)
                house_index = 0  # 重置索引
            except:
                print("[INFO] 滚动失败，可能已到达底部")
                break

    if failed_count >= max_failed:
        print(f"[WARNING] 连续失败 {max_failed} 次，停止处理")

    print(f"\n[INFO] 采集完成！")
    print(f"[INFO] 共处理 {processed_count} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

    # 显示采集统计
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        total_people = len(df)
        print(f"[INFO] 共采集到 {total_people} 个暂无图片人员")
    except:
        print("[INFO] 无法读取统计数据")

if __name__ == "__main__":
    main()
