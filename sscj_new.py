#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三实查询数据采集脚本 - 重新设计版本
功能：自动采集房屋内人员信息
"""

import uiautomator2 as u2
import time
import pandas as pd
import os
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "com.founder.police.sscx"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")

# 初始化设备连接
d = u2.connect()

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def log_error(error_msg):
    """记录错误日志"""
    error_file = os.path.join(RESULT_DIR, "error.log")
    with open(error_file, 'a', encoding='utf-8') as f:
        f.write(f"{datetime.now()}: {error_msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        # 获取页面XML
        xml = d.dump_hierarchy()

        # 简单解析文本内容
        import re
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def is_house_list_page():
    """判断是否在房屋列表页"""
    try:
        texts = get_page_text()
        # 房屋列表页特征：包含分页信息
        return any("共有" in text and "条数据" in text and "每页显示" in text for text in texts)
    except:
        return False

def back_to_house_list():
    """返回房屋列表页"""
    try:
        # 最多返回3次，直到回到房屋列表页
        for i in range(3):
            if is_house_list_page():
                print("[INFO] 已在房屋列表页")
                return True

            print(f"[INFO] 执行返回操作 {i+1}/3")
            d.press("back")
            time.sleep(2)

        return is_house_list_page()
    except Exception as e:
        print(f"[ERROR] 返回房屋列表页失败: {e}")
        return False

def get_pagination_info():
    """获取分页信息"""
    try:
        texts = get_page_text()
        import re

        for text in texts:
            # 匹配格式：共有128837条数据，每页显示10条，第1页
            match = re.search(r'共有(\d+)条数据.*?每页显示(\d+)条.*?第(\d+)页', text)
            if match:
                total_records = int(match.group(1))
                per_page = int(match.group(2))
                current_page = int(match.group(3))
                total_pages = (total_records + per_page - 1) // per_page  # 向上取整

                return {
                    "total_records": total_records,
                    "per_page": per_page,
                    "current_page": current_page,
                    "total_pages": total_pages
                }

        return None
    except Exception as e:
        print(f"[ERROR] 获取分页信息失败: {e}")
        return None

def safe_navigate_to_house_list():
    """安全导航到房屋列表页"""
    max_attempts = 5
    attempt = 0

    while attempt < max_attempts:
        attempt += 1
        current_page = get_current_page_type()
        print(f"[INFO] 当前页面类型: {current_page} (尝试 {attempt}/{max_attempts})")

        if current_page == "house_list":
            print("[INFO] 已在房屋列表页")
            return True
        elif current_page == "people_list":
            print("[INFO] 在人员列表页，返回房屋详情页")
            d.press("back")
            time.sleep(2)
        elif current_page == "house_detail":
            print("[INFO] 在房屋详情页，返回房屋列表页")
            d.press("back")
            time.sleep(2)
        elif current_page == "main_page":
            print("[INFO] 在主页，点击搜索进入房屋列表")
            if d(text="搜索").exists:
                d(text="搜索").click()
                time.sleep(3)
            else:
                print("[ERROR] 未找到搜索按钮")
                return False
        else:
            print("[INFO] 页面类型未知，尝试返回")
            d.press("back")
            time.sleep(2)

    print("[ERROR] 无法导航到房屋列表页")
    return False

def extract_house_address():
    """提取房屋地址"""
    try:
        texts = get_page_text()
        
        # 查找地址信息
        for text in texts:
            if "云南省" in text and len(text) > 15:
                return text
            elif "小组" in text and len(text) > 10:
                return text
        
        return "地址未识别"
    except Exception as e:
        print(f"[ERROR] 提取地址失败: {e}")
        return "地址提取失败"

def extract_no_photo_people():
    """只提取暂无图片的人员信息"""
    try:
        texts = get_page_text()
        people = []

        # 首先检查页面是否包含"暂无图片"
        if not any("暂无图片" in text for text in texts):
            return []

        print("[INFO] 发现'暂无图片'人员，开始提取...")

        # 找到所有"暂无图片"的索引位置
        no_photo_indices = []
        for i, text in enumerate(texts):
            if "暂无图片" in text:
                no_photo_indices.append(i)

        print(f"[DEBUG] 找到 {len(no_photo_indices)} 个'暂无图片'位置")

        import re

        for no_photo_idx in no_photo_indices:
            # 在"暂无图片"前后查找人员信息
            search_start = max(0, no_photo_idx - 15)
            search_end = min(len(texts), no_photo_idx + 15)

            person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": "", "备注": "暂无图片"}

            # 在搜索范围内查找人员信息
            for j in range(search_start, search_end):
                text = texts[j]

                # 查找姓名 - 格式：名：xxx
                if ("名：" in text or "名:" in text) and not person["姓名"]:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    name_clean = name_part.strip()
                    # 验证姓名合理性（2-10个字符，主要是中文）
                    if 2 <= len(name_clean) <= 10 and not any(char.isdigit() for char in name_clean):
                        person["姓名"] = name_clean
                        print(f"[DEBUG] 找到暂无图片人员姓名: {person['姓名']}")

                # 查找身份证号 - 18位数字
                if not person["身份证号"]:
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person["身份证号"] = id_match.group(1)
                        print(f"[DEBUG] 找到身份证: {person['身份证号']}")

                # 查找联系电话 - 11位手机号
                if not person["联系电话"]:
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person["联系电话"] = phone_match.group(1)
                        print(f"[DEBUG] 找到电话: {person['联系电话']}")

                # 查找更新时间
                if not person["更新时间"]:
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person["更新时间"] = time_match.group(1)
                        print(f"[DEBUG] 找到更新时间: {person['更新时间']}")

            # 如果找到了基本信息就添加
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 成功提取暂无图片人员: {person['姓名']} - {person['身份证号']}")
            else:
                print(f"[DEBUG] 暂无图片人员信息不完整: 姓名={person['姓名']}, 身份证={person['身份证号']}")

        return people
    except Exception as e:
        print(f"[ERROR] 提取暂无图片人员信息失败: {e}")
        return []

def save_people_data(people, house_address):
    """保存人员数据到CSV"""
    if not people:
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        
        saved_count = 0
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                # 添加新记录
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": person.get('备注', '暂无图片')  # 添加暂无图片备注
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存暂无图片人员: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    
    except Exception as e:
        log_error(f"保存数据失败: {e}")
        return 0

def process_house_people(house_address):
    """处理房屋内的暂无图片人员信息"""
    print(f"[INFO] 开始采集房屋暂无图片人员")

    total_saved = 0
    scroll_count = 0
    max_scrolls = 8
    processed_people = set()  # 记录已处理的人员，避免重复
    no_data_count = 0  # 连续没有数据的次数

    while scroll_count < max_scrolls:
        # 提取当前页面的暂无图片人员信息
        people = extract_no_photo_people()

        if people:
            no_data_count = 0  # 重置无数据计数
            # 过滤掉已处理的人员
            new_people = []
            for person in people:
                person_key = f"{person['姓名']}_{person['身份证号']}"
                if person_key not in processed_people:
                    new_people.append(person)
                    processed_people.add(person_key)

            if new_people:
                saved = save_people_data(new_people, house_address)
                total_saved += saved
                print(f"[INFO] 本轮保存 {saved} 个暂无图片人员")
            else:
                print("[INFO] 本轮没有新的暂无图片人员")
        else:
            no_data_count += 1
            print(f"[INFO] 本轮未发现暂无图片人员 ({no_data_count}/3)")

        # 检查是否显示"没有更多数据了"
        texts = get_page_text()
        if any("没有更多数据" in text for text in texts):
            print("[INFO] 检测到'没有更多数据了'，停止滚动")
            break

        # 如果连续3次没有找到暂无图片人员，可能已经滚动完了
        if no_data_count >= 3:
            print("[INFO] 连续多次未发现暂无图片人员，可能已滚动完毕")
            break

        # 尝试向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                print(f"[INFO] 向下滚动查看更多 ({scroll_count + 1}/{max_scrolls})")
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                print("[INFO] 页面不可滚动，停止")
                break
        except Exception as e:
            print(f"[INFO] 滚动失败: {e}")
            break

    print(f"[INFO] 房屋采集完成，共保存 {total_saved} 个暂无图片人员")
    return total_saved

def get_house_items_on_current_page():
    """获取当前页面的房屋项目"""
    try:
        # 确保在房屋列表页
        if get_current_page_type() != "house_list":
            print("[ERROR] 不在房屋列表页，无法获取房屋项目")
            return []

        # 查找房屋项目容器
        house_elements = d.xpath('//android.widget.LinearLayout').all()
        # 过滤出可能是房屋项目的容器（有一定高度且包含文本）
        valid_houses = []

        for element in house_elements:
            height = element.bounds[3] - element.bounds[1]
            width = element.bounds[2] - element.bounds[0]

            # 房屋项目特征：高度>100，宽度>200
            if height > 100 and width > 200:
                # 检查是否包含房屋相关文本
                try:
                    element_text = element.get_text()
                    if element_text and any(keyword in element_text for keyword in ["自住房", "出租房", "云南省", "小组"]):
                        valid_houses.append(element)
                except:
                    # 如果无法获取文本，仍然添加（可能是图片+文本的组合）
                    valid_houses.append(element)

        print(f"[DEBUG] 当前页面找到 {len(valid_houses)} 个房屋项目")
        return valid_houses
    except Exception as e:
        print(f"[ERROR] 获取房屋项目失败: {e}")
        return []

def process_single_house_by_element(house_element, house_index):
    """通过元素处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理房屋项目 {house_index + 1} ===")

        # 确保在房屋列表页
        if not safe_navigate_to_house_list():
            return False

        # 点击房屋项目
        print(f"[DEBUG] 点击房屋项目，边界: {house_element.bounds}")
        house_element.click()
        time.sleep(3)

        # 检查是否进入房屋详情页
        if get_current_page_type() != "house_detail":
            print("[WARNING] 未成功进入房屋详情页")
            safe_navigate_to_house_list()
            return False

        # 获取房屋地址
        house_address = extract_house_address()
        print(f"[INFO] 房屋地址: {house_address}")

        # 点击实有人口
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到'实有人口'按钮")
            safe_navigate_to_house_list()
            return False

        d(text="实有人口").click()
        time.sleep(3)

        # 检查是否进入人员列表页
        if get_current_page_type() != "people_list":
            print("[WARNING] 未成功进入人员列表页")
            safe_navigate_to_house_list()
            return False

        # 采集暂无图片人员信息
        total_saved = process_house_people(house_address)

        # 返回房屋列表页
        if not safe_navigate_to_house_list():
            print("[WARNING] 返回房屋列表页失败")
            return False

        print(f"[INFO] 房屋处理完成，保存了 {total_saved} 个暂无图片人员")
        return True

    except Exception as e:
        error_msg = f"处理房屋失败: {e}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)

        # 尝试返回房屋列表页
        safe_navigate_to_house_list()
        return False

def process_current_page_houses():
    """处理当前页面的所有房屋"""
    # 确保在房屋列表页
    if not safe_navigate_to_house_list():
        return 0

    # 获取当前页面的房屋项目
    house_elements = get_house_items_on_current_page()
    if not house_elements:
        print("[WARNING] 当前页面没有找到房屋项目")
        return 0

    processed_count = 0

    # 处理每个房屋项目
    for i, house_element in enumerate(house_elements):
        success = process_single_house_by_element(house_element, i)
        if success:
            processed_count += 1

        time.sleep(2)  # 间隔时间

    return processed_count

def navigate_to_next_page():
    """导航到下一页"""
    try:
        # 确保在房屋列表页
        if not safe_navigate_to_house_list():
            return False

        # 获取分页信息
        pagination = get_pagination_info()
        if not pagination:
            print("[WARNING] 无法获取分页信息")
            return False

        current_page = pagination["current_page"]
        total_pages = pagination["total_pages"]

        print(f"[INFO] 当前第 {current_page} 页，共 {total_pages} 页")

        if current_page >= total_pages:
            print("[INFO] 已是最后一页")
            return False

        # 尝试滚动到下一页
        print("[INFO] 滚动到下一页")
        d(scrollable=True).scroll.vert.forward(steps=10)
        time.sleep(3)

        # 检查是否成功到达下一页
        new_pagination = get_pagination_info()
        if new_pagination and new_pagination["current_page"] > current_page:
            print(f"[INFO] 成功到达第 {new_pagination['current_page']} 页")
            return True
        else:
            print("[WARNING] 可能未成功到达下一页")
            return False

    except Exception as e:
        print(f"[ERROR] 导航到下一页失败: {e}")
        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询暂无图片人员采集...")

    # 初始化
    init_csv()

    # 启动应用
    d.app_start(APP_PACKAGE)
    time.sleep(15)

    # 导航到房屋列表页
    if not safe_navigate_to_house_list():
        print("[ERROR] 无法进入房屋列表页")
        return

    # 获取初始分页信息
    pagination = get_pagination_info()
    if pagination:
        print(f"[INFO] 总共有 {pagination['total_records']} 条记录，{pagination['total_pages']} 页")

    total_processed = 0
    current_page = 1
    max_pages = 50  # 最大处理页数

    while current_page <= max_pages:
        print(f"\n[INFO] === 处理第 {current_page} 页 ===")

        # 确保在房屋列表页
        if not safe_navigate_to_house_list():
            print("[ERROR] 无法返回房屋列表页")
            break

        # 处理当前页面的所有房屋
        page_processed = process_current_page_houses()
        total_processed += page_processed

        print(f"[INFO] 第 {current_page} 页处理完成，处理了 {page_processed} 个房屋")

        # 尝试导航到下一页
        if not navigate_to_next_page():
            print("[INFO] 无法导航到下一页，可能已处理完所有页面")
            break

        current_page += 1
        time.sleep(3)  # 页面间隔时间

    print(f"\n[INFO] 采集完成！")
    print(f"[INFO] 共处理 {total_processed} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

    # 显示采集统计
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        total_people = len(df)
        print(f"[INFO] 共采集到 {total_people} 个暂无图片人员")
    except:
        print("[INFO] 无法读取统计数据")

if __name__ == "__main__":
    main()
