#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
三实查询暂无图片人员采集脚本 - 简化版
只在房屋列表页和人员列表页之间切换
"""

import uiautomator2 as u2
import time
import pandas as pd
import os
from datetime import datetime

# 配置
APP_PACKAGE = "com.aisino.sscx"
RESULT_CSV = "暂无图片人员.csv"

# 连接设备
d = u2.connect()

def get_page_text():
    """获取页面所有文本"""
    try:
        return [elem.get_text() for elem in d.xpath('//*[@text]').all() if elem.get_text()]
    except:
        return []

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=['房屋地址', '姓名', '身份证号', '联系电话', '更新时间', '采集时间'])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def save_person_data(house_address, name, id_card, phone, update_time):
    """保存人员数据到CSV"""
    try:
        new_data = {
            '房屋地址': house_address,
            '姓名': name,
            '身份证号': id_card,
            '联系电话': phone,
            '更新时间': update_time,
            '采集时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        df = pd.DataFrame([new_data])
        df.to_csv(RESULT_CSV, mode='a', header=False, index=False, encoding='utf-8-sig')
        print(f"[INFO] 保存人员: {name}")
        return True
    except Exception as e:
        print(f"[ERROR] 保存数据失败: {e}")
        return False

def is_house_list_page():
    """判断是否在房屋列表页"""
    try:
        texts = get_page_text()
        return any("共有" in text and "条数据" in text for text in texts)
    except:
        return False

def back_to_house_list():
    """返回房屋列表页"""
    try:
        for i in range(3):
            if is_house_list_page():
                print("[INFO] 已在房屋列表页")
                return True
            print(f"[INFO] 返回操作 {i+1}/3")
            d.press("back")
            time.sleep(2)
        return is_house_list_page()
    except Exception as e:
        print(f"[ERROR] 返回失败: {e}")
        return False

def get_house_items():
    """获取房屋项目"""
    try:
        if not is_house_list_page():
            return []

        # 简单粗暴：找所有可点击的LinearLayout
        house_elements = d.xpath('//android.widget.LinearLayout[@clickable="true"]').all()

        # 如果没找到可点击的，就找所有LinearLayout
        if not house_elements:
            house_elements = d.xpath('//android.widget.LinearLayout').all()

        # 过滤出合适大小的元素
        valid_houses = []
        for element in house_elements:
            try:
                height = element.bounds[3] - element.bounds[1]
                width = element.bounds[2] - element.bounds[0]

                # 房屋项目应该有一定的大小
                if height > 80 and width > 300:
                    valid_houses.append(element)
            except:
                continue

        print(f"[DEBUG] 找到 {len(valid_houses)} 个可能的房屋项目")
        return valid_houses[:10]  # 只取前10个，避免太多
    except Exception as e:
        print(f"[ERROR] 获取房屋项目失败: {e}")
        return []

def extract_house_address():
    """提取房屋地址"""
    try:
        texts = get_page_text()
        for text in texts:
            if "云南省" in text and len(text) > 10:
                return text
        return "未知地址"
    except:
        return "未知地址"

def process_people_in_current_page(house_address):
    """处理当前页面的人员"""
    saved_count = 0
    try:
        texts = get_page_text()
        
        # 查找暂无图片的人员
        for i, text in enumerate(texts):
            if "暂无图片" in text:
                # 查找相关的人员信息
                name = ""
                id_card = ""
                phone = ""
                update_time = ""
                
                # 在附近的文本中查找信息
                for j in range(max(0, i-5), min(len(texts), i+10)):
                    check_text = texts[j]
                    
                    if "名：" in check_text:
                        name = check_text.replace("名：", "").strip()
                    elif "身份证号码：" in check_text:
                        id_card = check_text.replace("身份证号码：", "").strip()
                    elif "联系电话：" in check_text:
                        phone = check_text.replace("联系电话：", "").strip()
                    elif "更新时间：" in check_text:
                        update_time = check_text.replace("更新时间：", "").strip()
                
                # 如果找到了姓名，就保存
                if name:
                    if save_person_data(house_address, name, id_card, phone, update_time):
                        saved_count += 1
        
        return saved_count
    except Exception as e:
        print(f"[ERROR] 处理人员信息失败: {e}")
        return saved_count

def process_single_house(house_element, index):
    """处理单个房屋"""
    try:
        print(f"\n[INFO] === 处理第 {index + 1} 个房屋 ===")

        # 确保在房屋列表页
        if not is_house_list_page():
            print("[ERROR] 不在房屋列表页")
            return False

        # 点击房屋项目
        print(f"[DEBUG] 点击房屋项目")
        try:
            house_element.click()
        except:
            print("[WARNING] 点击房屋项目失败")
            return False
        time.sleep(3)

        # 检查是否有"实有人口"按钮（说明进入了房屋详情页）
        if not d(text="实有人口").exists:
            print("[WARNING] 未找到'实有人口'按钮，可能未进入详情页")
            back_to_house_list()
            return False

        # 获取房屋地址
        house_address = extract_house_address()
        print(f"[INFO] 房屋地址: {house_address}")

        # 点击实有人口
        try:
            d(text="实有人口").click()
            time.sleep(3)
        except:
            print("[WARNING] 点击'实有人口'失败")
            back_to_house_list()
            return False

        # 处理人员信息
        saved_count = process_people_in_current_page(house_address)
        print(f"[INFO] 保存了 {saved_count} 个暂无图片人员")

        # 返回房屋列表页
        back_to_house_list()
        return True

    except Exception as e:
        print(f"[ERROR] 处理房屋失败: {e}")
        back_to_house_list()
        return False

def main():
    """主函数"""
    print("[INFO] 启动三实查询暂无图片人员采集...")
    
    # 初始化
    init_csv()
    
    # 启动应用
    d.app_start(APP_PACKAGE)
    time.sleep(15)
    
    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(5)
    time.sleep(3)
    
    # 点击搜索进入房屋列表
    search_found = False
    for _ in range(3):
        if d(text="搜索").exists:
            try:
                d(text="搜索").click()
                search_found = True
                break
            except:
                print("[WARNING] 点击搜索失败，重试...")
                time.sleep(2)
        time.sleep(1)

    if not search_found:
        print("[ERROR] 未找到搜索按钮或点击失败")
        return

    time.sleep(3)
    
    # 确保在房屋列表页
    if not is_house_list_page():
        print("[ERROR] 未成功进入房屋列表页")
        return
    
    total_processed = 0
    page_count = 0
    max_pages = 20  # 最大处理页数
    
    while page_count < max_pages:
        print(f"\n[INFO] === 处理第 {page_count + 1} 页 ===")
        
        # 获取当前页面的房屋项目
        house_elements = get_house_items()
        if not house_elements:
            print("[WARNING] 当前页面没有房屋项目")
            break
        
        # 处理每个房屋
        for i, house_element in enumerate(house_elements):
            success = process_single_house(house_element, i)
            if success:
                total_processed += 1
            time.sleep(2)
        
        # 尝试滚动到下一页
        print("[INFO] 滚动到下一页")
        try:
            d(scrollable=True).scroll.vert.forward(steps=10)
            time.sleep(3)
        except:
            print("[INFO] 滚动失败，可能已到底部")
            break
        
        page_count += 1
    
    print(f"\n[INFO] 采集完成！")
    print(f"[INFO] 共处理 {total_processed} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")
    
    # 显示统计
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        print(f"[INFO] 共采集到 {len(df)} 个暂无图片人员")
    except:
        print("[INFO] 无法读取统计数据")

if __name__ == "__main__":
    main()
