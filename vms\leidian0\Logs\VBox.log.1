00:00:00.022593 VirtualBox VM 4.1.34 r2751252224 win.amd64 (Jul 26 2022 16:16:15) release log
00:00:00.022595 Log opened 2025-08-04T15:07:21.418857600Z
00:00:00.022595 Build Type: release
00:00:00.022599 OS Product: Windows 10
00:00:00.022600 OS Release: 10.0.19045
00:00:00.022600 OS Service Pack: 
00:00:00.033229 DMI Product Name: 10NBCTO1WW
00:00:00.037031 DMI Product Version: ThinkCentre M710t-N000
00:00:00.037051 Firmware type: UEFI
00:00:00.037459 Secure Boot: VERR_PRIVILEGE_NOT_HELD
00:00:00.037479 Host RAM: 32675MB (31.9GB) total, 19497MB (19.0GB) available
00:00:00.037481 Executable: C:\Program Files\ldplayer9box\Ld9BoxHeadless.exe
00:00:00.037482 Process ID: 9140
00:00:00.037482 Package type: WINDOWS_64BITS_GENERIC (OSE)
00:00:00.038748 Installed Extension Packs:
00:00:00.038783   None installed!
00:00:00.039446 Console: Machine state changed to 'Starting'
00:00:00.050558 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.050580 SUP: seg #1: R X 0x00001000 LB 0x00109000
00:00:00.050590 SUP: seg #2: R   0x0010a000 LB 0x0004a000
00:00:00.050599 SUP: seg #3: RW  0x00154000 LB 0x00013000
00:00:00.050607 SUP: seg #4: R   0x00167000 LB 0x0000e000
00:00:00.050615 SUP: seg #5: RW  0x00175000 LB 0x00003000
00:00:00.050622 SUP: seg #6: R   0x00178000 LB 0x0000b000
00:00:00.050630 SUP: seg #7: RWX 0x00183000 LB 0x00002000
00:00:00.050638 SUP: seg #8: R   0x00185000 LB 0x00007000
00:00:00.051602 SUP: Loaded Ld9VMMR0.r0 (C:\Program Files\ldplayer9box\Ld9VMMR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.051625 SUP: VMMR0EntryEx located at XXXXXXXXXXXXXXXX and VMMR0EntryFast at XXXXXXXXXXXXXXXX
00:00:00.051634 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9VMMR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.053302 Guest OS type: 'Linux26_64'
00:00:00.054236 fHMForced=true - No raw-mode support in this build!
00:00:00.056590 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\Snapshots' (snapshots) is unknown
00:00:00.056602 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk' is ntfs
00:00:00.057272 File system of 'D:\Program Files\LDPlayer9\system.vmdk' is ntfs
00:00:00.057914 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk' is ntfs
00:00:00.092460 Shared Clipboard: Service loaded
00:00:00.092506 Shared Clipboard: Mode: Off
00:00:00.092595 Shared Clipboard: Service running in headless mode
00:00:00.093721 Drag and drop service loaded
00:00:00.093735 Drag and drop mode: Off
00:00:00.095170 Extradata overrides:
00:00:00.095186   VBoxInternal/Devices/fastpipe/0/PCIBusNo="0"
00:00:00.095241   VBoxInternal/Devices/fastpipe/0/PCIDeviceNo="18"
00:00:00.095282   VBoxInternal/Devices/fastpipe/0/PCIFunctionNo="0"
00:00:00.095321   VBoxInternal/Devices/fastpipe/0/Trusted="1"
00:00:00.095407   VBoxInternal/PDM/Devices/fastpipe/Path="fastpipe.dll"
00:00:00.095571 ************************* CFGM dump *************************
00:00:00.095572 [/] (level 0)
00:00:00.095574   CpuExecutionCap   <integer> = 0x0000000000000064 (100)
00:00:00.095575   EnablePAE         <integer> = 0x0000000000000000 (0)
00:00:00.095576   HMEnabled         <integer> = 0x0000000000000001 (1)
00:00:00.095576   MemBalloonSize    <integer> = 0x0000000000000000 (0)
00:00:00.095577   Name              <string>  = "leidian0" (cb=9)
00:00:00.095578   NumCPUs           <integer> = 0x0000000000000004 (4)
00:00:00.095578   PageFusionAllowed <integer> = 0x0000000000000000 (0)
00:00:00.095579   RamHoleSize       <integer> = 0x0000000020000000 (536 870 912, 512 MB)
00:00:00.095580   RamSize           <integer> = 0x0000000180000000 (6 442 450 944, 6 144 MB, 6.0 GB)
00:00:00.095581   TimerMillies      <integer> = 0x000000000000000a (10)
00:00:00.095582   UUID              <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.095588 
00:00:00.095588 [/CPUM/] (level 1)
00:00:00.095589   GuestCpuName       <string>  = "host" (cb=5)
00:00:00.095590   NestedHWVirt       <integer> = 0x0000000000000000 (0)
00:00:00.095590   PortableCpuIdLevel <integer> = 0x0000000000000000 (0)
00:00:00.095591   SpecCtrl           <integer> = 0x0000000000000001 (1)
00:00:00.095591 
00:00:00.095591 [/CPUM/IsaExts/] (level 2)
00:00:00.095592 
00:00:00.095592 [/DBGC/] (level 1)
00:00:00.095593   GlobalInitScript <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-init" (cb=39)
00:00:00.095594   HistoryFile      <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-history" (cb=42)
00:00:00.095594   LocalInitScript  <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/dbgc-init" (cb=50)
00:00:00.095595 
00:00:00.095595 [/DBGF/] (level 1)
00:00:00.095596   Path <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/debug/;D:\Program Files\LDPlayer9\vms\leidian0/;cache*D:\Program Files\LDPlayer9\vms\leidian0/dbgcache/;C:\Users\<USER>\" (cb=159)
00:00:00.095596 
00:00:00.095596 [/Devices/] (level 1)
00:00:00.095597 
00:00:00.095597 [/Devices/8237A/] (level 2)
00:00:00.095598 
00:00:00.095598 [/Devices/8237A/0/] (level 3)
00:00:00.095598   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095599 
00:00:00.095599 [/Devices/GIMDev/] (level 2)
00:00:00.095600 
00:00:00.095600 [/Devices/GIMDev/0/] (level 3)
00:00:00.095600   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095601 
00:00:00.095601 [/Devices/VMMDev/] (level 2)
00:00:00.095601 
00:00:00.095601 [/Devices/VMMDev/0/] (level 3)
00:00:00.095602   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095603   PCIDeviceNo   <integer> = 0x0000000000000004 (4)
00:00:00.095603   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095604   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.095604 
00:00:00.095604 [/Devices/VMMDev/0/Config/] (level 4)
00:00:00.095605   GuestCoreDumpDir <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\Snapshots" (cb=50)
00:00:00.095605 
00:00:00.095606 [/Devices/VMMDev/0/LUN#0/] (level 4)
00:00:00.095606   Driver <string>  = "HGCM" (cb=5)
00:00:00.095607 
00:00:00.095607 [/Devices/VMMDev/0/LUN#0/Config/] (level 5)
00:00:00.095607   Object <integer> = 0x0000000002a6e520 (44 492 064)
00:00:00.095608 
00:00:00.095608 [/Devices/VMMDev/0/LUN#999/] (level 4)
00:00:00.095609   Driver <string>  = "MainStatus" (cb=11)
00:00:00.095610 
00:00:00.095610 [/Devices/VMMDev/0/LUN#999/Config/] (level 5)
00:00:00.095610   First   <integer> = 0x0000000000000000 (0)
00:00:00.095611   Last    <integer> = 0x0000000000000000 (0)
00:00:00.095611   papLeds <integer> = 0x0000000002a64338 (44 450 616)
00:00:00.095612 
00:00:00.095612 [/Devices/acpi/] (level 2)
00:00:00.095613 
00:00:00.095613 [/Devices/acpi/0/] (level 3)
00:00:00.095613   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095614   PCIDeviceNo   <integer> = 0x0000000000000007 (7)
00:00:00.095614   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095615   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.095615 
00:00:00.095615 [/Devices/acpi/0/Config/] (level 4)
00:00:00.095617   CpuHotPlug          <integer> = 0x0000000000000000 (0)
00:00:00.095617   FdcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.095618   HostBusPciAddress   <integer> = 0x0000000000000000 (0)
00:00:00.095618   HpetEnabled         <integer> = 0x0000000000000000 (0)
00:00:00.095619   IOAPIC              <integer> = 0x0000000000000001 (1)
00:00:00.095619   IocPciAddress       <integer> = 0x0000000000010000 (65 536)
00:00:00.095620   NumCPUs             <integer> = 0x0000000000000004 (4)
00:00:00.095620   Parallel0IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.095621   Parallel0Irq        <integer> = 0x0000000000000000 (0)
00:00:00.095621   Parallel1IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.095622   Parallel1Irq        <integer> = 0x0000000000000000 (0)
00:00:00.095622   Serial0IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.095622   Serial0Irq          <integer> = 0x0000000000000000 (0)
00:00:00.095623   Serial1IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.095623   Serial1Irq          <integer> = 0x0000000000000000 (0)
00:00:00.095624   ShowCpu             <integer> = 0x0000000000000001 (1)
00:00:00.095624   ShowRtc             <integer> = 0x0000000000000000 (0)
00:00:00.095625   SmcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.095625 
00:00:00.095625 [/Devices/acpi/0/LUN#0/] (level 4)
00:00:00.095626   Driver <string>  = "ACPIHost" (cb=9)
00:00:00.095626 
00:00:00.095626 [/Devices/acpi/0/LUN#0/Config/] (level 5)
00:00:00.095627 
00:00:00.095627 [/Devices/acpi/0/LUN#1/] (level 4)
00:00:00.095628   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.095628 
00:00:00.095628 [/Devices/acpi/0/LUN#1/Config/] (level 5)
00:00:00.095629 
00:00:00.095629 [/Devices/acpi/0/LUN#2/] (level 4)
00:00:00.095630   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.095630 
00:00:00.095630 [/Devices/acpi/0/LUN#2/Config/] (level 5)
00:00:00.095631 
00:00:00.095631 [/Devices/acpi/0/LUN#3/] (level 4)
00:00:00.095631   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.095632 
00:00:00.095632 [/Devices/acpi/0/LUN#3/Config/] (level 5)
00:00:00.095633 
00:00:00.095633 [/Devices/apic/] (level 2)
00:00:00.095634 
00:00:00.095634 [/Devices/apic/0/] (level 3)
00:00:00.095634   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095635 
00:00:00.095635 [/Devices/apic/0/Config/] (level 4)
00:00:00.095636   IOAPIC  <integer> = 0x0000000000000001 (1)
00:00:00.095636   Mode    <integer> = 0x0000000000000003 (3)
00:00:00.095636   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.095637 
00:00:00.095637 [/Devices/e1000/] (level 2)
00:00:00.095637 
00:00:00.095638 [/Devices/fastpipe/] (level 2)
00:00:00.095638 
00:00:00.095638 [/Devices/fastpipe/0/] (level 3)
00:00:00.095639   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095639   PCIDeviceNo   <integer> = 0x0000000000000012 (18)
00:00:00.095640   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095640   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.095641 
00:00:00.095641 [/Devices/i8254/] (level 2)
00:00:00.095641 
00:00:00.095641 [/Devices/i8254/0/] (level 3)
00:00:00.095642 
00:00:00.095642 [/Devices/i8254/0/Config/] (level 4)
00:00:00.095643 
00:00:00.095643 [/Devices/i8259/] (level 2)
00:00:00.095643 
00:00:00.095643 [/Devices/i8259/0/] (level 3)
00:00:00.095644   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095644 
00:00:00.095644 [/Devices/i8259/0/Config/] (level 4)
00:00:00.095645 
00:00:00.095645 [/Devices/ioapic/] (level 2)
00:00:00.095646 
00:00:00.095646 [/Devices/ioapic/0/] (level 3)
00:00:00.095646   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095647 
00:00:00.095647 [/Devices/ioapic/0/Config/] (level 4)
00:00:00.095647   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.095648 
00:00:00.095648 [/Devices/mc146818/] (level 2)
00:00:00.095648 
00:00:00.095649 [/Devices/mc146818/0/] (level 3)
00:00:00.095649 
00:00:00.095649 [/Devices/mc146818/0/Config/] (level 4)
00:00:00.095650   UseUTC <integer> = 0x0000000000000001 (1)
00:00:00.095650 
00:00:00.095650 [/Devices/parallel/] (level 2)
00:00:00.095651 
00:00:00.095651 [/Devices/pcarch/] (level 2)
00:00:00.095651 
00:00:00.095652 [/Devices/pcarch/0/] (level 3)
00:00:00.095652   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095652 
00:00:00.095653 [/Devices/pcarch/0/Config/] (level 4)
00:00:00.095653 
00:00:00.095653 [/Devices/pcbios/] (level 2)
00:00:00.095654 
00:00:00.095654 [/Devices/pcbios/0/] (level 3)
00:00:00.095654   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095655 
00:00:00.095655 [/Devices/pcbios/0/Config/] (level 4)
00:00:00.095656   APIC           <integer> = 0x0000000000000001 (1)
00:00:00.095656   BootDevice0    <string>  = "IDE" (cb=4)
00:00:00.095657   BootDevice1    <string>  = "NONE" (cb=5)
00:00:00.095657   BootDevice2    <string>  = "NONE" (cb=5)
00:00:00.095658   BootDevice3    <string>  = "NONE" (cb=5)
00:00:00.095658   FloppyDevice   <string>  = "i82078" (cb=7)
00:00:00.095658   HardDiskDevice <string>  = "piix3ide" (cb=9)
00:00:00.095659   IOAPIC         <integer> = 0x0000000000000001 (1)
00:00:00.095659   McfgBase       <integer> = 0x0000000000000000 (0)
00:00:00.095660   McfgLength     <integer> = 0x0000000000000000 (0)
00:00:00.095660   NumCPUs        <integer> = 0x0000000000000004 (4)
00:00:00.095661   PXEDebug       <integer> = 0x0000000000000000 (0)
00:00:00.095661   UUID           <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.095662   UuidLe         <integer> = 0x0000000000000000 (0)
00:00:00.095663 
00:00:00.095663 [/Devices/pcbios/0/Config/NetBoot/] (level 5)
00:00:00.095664 
00:00:00.095664 [/Devices/pcbios/0/Config/NetBoot/0/] (level 6)
00:00:00.095665   NIC           <integer> = 0x0000000000000000 (0)
00:00:00.095665   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095665   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.095666   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095666 
00:00:00.095666 [/Devices/pci/] (level 2)
00:00:00.095667 
00:00:00.095667 [/Devices/pci/0/] (level 3)
00:00:00.095668   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095668 
00:00:00.095668 [/Devices/pci/0/Config/] (level 4)
00:00:00.095669   IOAPIC <integer> = 0x0000000000000001 (1)
00:00:00.095669 
00:00:00.095669 [/Devices/pcibridge/] (level 2)
00:00:00.095670 
00:00:00.095670 [/Devices/pckbd/] (level 2)
00:00:00.095670 
00:00:00.095671 [/Devices/pckbd/0/] (level 3)
00:00:00.095671   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.095671 
00:00:00.095672 [/Devices/pckbd/0/Config/] (level 4)
00:00:00.095672 
00:00:00.095672 [/Devices/pckbd/0/LUN#0/] (level 4)
00:00:00.095673   Driver <string>  = "KeyboardQueue" (cb=14)
00:00:00.095673 
00:00:00.095674 [/Devices/pckbd/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.095674   Driver <string>  = "MainKeyboard" (cb=13)
00:00:00.095675 
00:00:00.095675 [/Devices/pckbd/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.095676   Object <integer> = 0x0000000002a67a50 (44 464 720)
00:00:00.095676 
00:00:00.095676 [/Devices/pckbd/0/LUN#0/Config/] (level 5)
00:00:00.095677   QueueSize <integer> = 0x0000000000000040 (64)
00:00:00.095678 
00:00:00.095678 [/Devices/pckbd/0/LUN#1/] (level 4)
00:00:00.095678   Driver <string>  = "MouseQueue" (cb=11)
00:00:00.095679 
00:00:00.095679 [/Devices/pckbd/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.095680   Driver <string>  = "MainMouse" (cb=10)
00:00:00.095680 
00:00:00.095681 [/Devices/pckbd/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.095681   Object <integer> = 0x0000000002a68110 (44 466 448)
00:00:00.095682 
00:00:00.095682 [/Devices/pckbd/0/LUN#1/Config/] (level 5)
00:00:00.095683   QueueSize <integer> = 0x0000000000000080 (128)
00:00:00.095683 
00:00:00.095683 [/Devices/pcnet/] (level 2)
00:00:00.095684 
00:00:00.095684 [/Devices/serial/] (level 2)
00:00:00.095684 
00:00:00.095685 [/Devices/virtio-net/] (level 2)
00:00:00.095685 
00:00:00.095685 [/Devices/virtio-net/0/] (level 3)
00:00:00.095686   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095686   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.095687   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095687   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.095687 
00:00:00.095688 [/Devices/virtio-net/0/Config/] (level 4)
00:00:00.095688   CableConnected <integer> = 0x0000000000000001 (1)
00:00:00.095689   LineSpeed      <integer> = 0x0000000000000000 (0)
00:00:00.095689   MAC            <bytes>   = "00 db 30 ef a7 99" (cb=6)
00:00:00.095690 
00:00:00.095690 [/Devices/virtio-net/0/LUN#0/] (level 4)
00:00:00.095691   Driver <string>  = "IntNet" (cb=7)
00:00:00.095691 
00:00:00.095691 [/Devices/virtio-net/0/LUN#0/Config/] (level 5)
00:00:00.095692   IfPolicyPromisc      <string>  = "deny" (cb=5)
00:00:00.095693   IgnoreConnectFailure <integer> = 0x0000000000000000 (0)
00:00:00.095693   Network              <string>  = "HostInterfaceNetworking-Realtek PCIe GBE Family Controller" (cb=59)
00:00:00.095694   Trunk                <string>  = "\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}" (cb=47)
00:00:00.095694   TrunkType            <integer> = 0x0000000000000003 (3)
00:00:00.095695 
00:00:00.095695 [/Devices/virtio-net/0/LUN#999/] (level 4)
00:00:00.095695   Driver <string>  = "MainStatus" (cb=11)
00:00:00.095696 
00:00:00.095696 [/Devices/virtio-net/0/LUN#999/Config/] (level 5)
00:00:00.095697   First   <integer> = 0x0000000000000000 (0)
00:00:00.095697   Last    <integer> = 0x0000000000000000 (0)
00:00:00.095698   papLeds <integer> = 0x0000000002a64218 (44 450 328)
00:00:00.095698 
00:00:00.095698 [/Devices/virtio-scsi/] (level 2)
00:00:00.095699 
00:00:00.095699 [/Devices/virtio-scsi/0/] (level 3)
00:00:00.095700   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.095700   PCIDeviceNo   <integer> = 0x000000000000000f (15)
00:00:00.095700   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.095701   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.095701 
00:00:00.095701 [/Devices/virtio-scsi/0/Config/] (level 4)
00:00:00.095702   Bootable   <integer> = 0x0000000000000001 (1)
00:00:00.095703   NumTargets <integer> = 0x0000000000000003 (3)
00:00:00.095703 
00:00:00.095703 [/Devices/virtio-scsi/0/LUN#0/] (level 4)
00:00:00.095704   Driver <string>  = "SCSI" (cb=5)
00:00:00.095704 
00:00:00.095704 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.095705   Driver <string>  = "VD" (cb=3)
00:00:00.095706 
00:00:00.095706 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.095707   Format    <string>  = "VMDK" (cb=5)
00:00:00.095707   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.095707   Path      <string>  = "D:\Program Files\LDPlayer9\system.vmdk" (cb=39)
00:00:00.095708   ReadOnly  <integer> = 0x0000000000000001 (1)
00:00:00.095708   Type      <string>  = "HardDisk" (cb=9)
00:00:00.095709 
00:00:00.095709 [/Devices/virtio-scsi/0/LUN#1/] (level 4)
00:00:00.095709   Driver <string>  = "SCSI" (cb=5)
00:00:00.095710 
00:00:00.095710 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.095711   Driver <string>  = "VD" (cb=3)
00:00:00.095711 
00:00:00.095711 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.095712   Format    <string>  = "VMDK" (cb=5)
00:00:00.095712   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.095713   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk" (cb=50)
00:00:00.095713   Type      <string>  = "HardDisk" (cb=9)
00:00:00.095713 
00:00:00.095714 [/Devices/virtio-scsi/0/LUN#2/] (level 4)
00:00:00.095714   Driver <string>  = "SCSI" (cb=5)
00:00:00.095714 
00:00:00.095715 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/] (level 5)
00:00:00.095715   Driver <string>  = "VD" (cb=3)
00:00:00.095716 
00:00:00.095716 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/Config/] (level 6)
00:00:00.095717   Format    <string>  = "VMDK" (cb=5)
00:00:00.095717   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.095717   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk" (cb=52)
00:00:00.095718   Type      <string>  = "HardDisk" (cb=9)
00:00:00.095718 
00:00:00.095718 [/Devices/virtio-scsi/0/LUN#999/] (level 4)
00:00:00.095719   Driver <string>  = "MainStatus" (cb=11)
00:00:00.095719 
00:00:00.095719 [/Devices/virtio-scsi/0/LUN#999/Config/] (level 5)
00:00:00.095720   DeviceInstance        <string>  = "virtio-scsi/0" (cb=14)
00:00:00.095721   First                 <integer> = 0x0000000000000000 (0)
00:00:00.095721   Last                  <integer> = 0x0000000000000002 (2)
00:00:00.095722   pConsole              <integer> = 0x0000000002a62f40 (44 445 504)
00:00:00.095722   papLeds               <integer> = 0x0000000002a63a20 (44 448 288)
00:00:00.095723   pmapMediumAttachments <integer> = 0x0000000002a64358 (44 450 648)
00:00:00.095724 
00:00:00.095724 [/EM/] (level 1)
00:00:00.095724   TripleFaultReset <integer> = 0x0000000000000000 (0)
00:00:00.095725 
00:00:00.095725 [/GIM/] (level 1)
00:00:00.095725   Provider <string>  = "KVM" (cb=4)
00:00:00.095725 
00:00:00.095726 [/HM/] (level 1)
00:00:00.095726   64bitEnabled       <integer> = 0x0000000000000001 (1)
00:00:00.095727   EnableLargePages   <integer> = 0x0000000000000001 (1)
00:00:00.095727   EnableNestedPaging <integer> = 0x0000000000000001 (1)
00:00:00.095727   EnableUX           <integer> = 0x0000000000000001 (1)
00:00:00.095728   EnableVPID         <integer> = 0x0000000000000001 (1)
00:00:00.095728   Exclusive          <integer> = 0x0000000000000000 (0)
00:00:00.095729   HMForced           <integer> = 0x0000000000000001 (1)
00:00:00.095729   IBPBOnVMEntry      <integer> = 0x0000000000000000 (0)
00:00:00.095730   IBPBOnVMExit       <integer> = 0x0000000000000000 (0)
00:00:00.095730   L1DFlushOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.095731   L1DFlushOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.095731   MDSClearOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.095731   MDSClearOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.095732   SpecCtrlByHost     <integer> = 0x0000000000000000 (0)
00:00:00.095732   UseNEMInstead      <integer> = 0x0000000000000000 (0)
00:00:00.095733 
00:00:00.095733 [/MM/] (level 1)
00:00:00.095733   CanUseLargerHeap <integer> = 0x0000000000000000 (0)
00:00:00.095734 
00:00:00.095734 [/NEM/] (level 1)
00:00:00.095734   Allow64BitGuests <integer> = 0x0000000000000001 (1)
00:00:00.095735 
00:00:00.095735 [/PDM/] (level 1)
00:00:00.095735 
00:00:00.095735 [/PDM/AsyncCompletion/] (level 2)
00:00:00.095736 
00:00:00.095736 [/PDM/AsyncCompletion/File/] (level 3)
00:00:00.095736 
00:00:00.095737 [/PDM/AsyncCompletion/File/BwGroups/] (level 4)
00:00:00.095737 
00:00:00.095737 [/PDM/BlkCache/] (level 2)
00:00:00.095738   CacheSize <integer> = 0x0000000000500000 (5 242 880, 5 MB)
00:00:00.095739 
00:00:00.095739 [/PDM/Devices/] (level 2)
00:00:00.095739 
00:00:00.095740 [/PDM/Devices/fastpipe/] (level 3)
00:00:00.095740   Path <string>  = "fastpipe.dll" (cb=13)
00:00:00.095740 
00:00:00.095741 [/PDM/Drivers/] (level 2)
00:00:00.095741 
00:00:00.095741 [/PDM/Drivers/VBoxC/] (level 3)
00:00:00.095742   Path <string>  = "VBoxC" (cb=6)
00:00:00.095742 
00:00:00.095742 [/PDM/NetworkShaper/] (level 2)
00:00:00.095743 
00:00:00.095743 [/PDM/NetworkShaper/BwGroups/] (level 3)
00:00:00.095744 
00:00:00.095744 [/TM/] (level 1)
00:00:00.095744   UTCOffset <integer> = 0x0000000000000000 (0)
00:00:00.095744 
00:00:00.095745 ********************* End of CFGM dump **********************
00:00:00.095977 HM: HMR3Init: VT-x w/ nested paging and unrestricted guest execution hw support
00:00:00.096074 MM: cbHyperHeap=0x840000 (8650752)
00:00:00.106315 CPUM: fXStateHostMask=0x7; initial: 0x7; host XCR0=0x1f
00:00:00.107749 CPUM: Matched host CPU INTEL 0x6/0x9e/0x9 Intel_Core7_KabyLake with CPU DB entry 'Intel Core i7-6700K' (INTEL 0x6/0x5e/0x3 Intel_Core7_Skylake)
00:00:00.107824 CPUM: MXCSR_MASK=0xffff (host: 0xffff)
00:00:00.107837 CPUM: Microcode revision 0x000000B4
00:00:00.107849 CPUM: Changing leaf 13[0]: EBX=0x440 -> 0x340, ECX=0x440 -> 0x340
00:00:00.107860 CPUM: MSR/CPUID reconciliation insert: 0x0000010b IA32_FLUSH_CMD
00:00:00.107887 CPUM: SetGuestCpuIdFeature: Enabled Speculation Control.
00:00:00.108116 PGM: Host paging mode: AMD64+NX
00:00:00.108133 PGM: PGMPool: cMaxPages=3328 (u64MaxPages=3110)
00:00:00.108141 PGM: pgmR3PoolInit: cMaxPages=0xd00 cMaxUsers=0x1a00 cMaxPhysExts=0x1a00 fCacheEnable=true 
00:00:00.142065 TM: GIP - u32Mode=3 (Invariant) u32UpdateHz=93 u32UpdateIntervalNS=10734900 enmUseTscDelta=2 (Practically Zero) fGetGipCpu=0x1b cCpus=8
00:00:00.142113 TM: GIP - u64CpuHz=3 600 001 324 (0xd693a92c)  SUPGetCpuHzFromGip => 3 600 001 324
00:00:00.142121 TM: GIP - CPU: iCpuSet=0x0 idCpu=0x0 idApic=0x0 iGipCpu=0x6 i64TSCDelta=0 enmState=3 u64CpuHz=3600001113(*) cErrors=0
00:00:00.142127 TM: GIP - CPU: iCpuSet=0x1 idCpu=0x1 idApic=0x1 iGipCpu=0x7 i64TSCDelta=0 enmState=3 u64CpuHz=3600001264(*) cErrors=0
00:00:00.142133 TM: GIP - CPU: iCpuSet=0x2 idCpu=0x2 idApic=0x2 iGipCpu=0x3 i64TSCDelta=0 enmState=3 u64CpuHz=3599955467(*) cErrors=0
00:00:00.142145 TM: GIP - CPU: iCpuSet=0x3 idCpu=0x3 idApic=0x3 iGipCpu=0x4 i64TSCDelta=0 enmState=3 u64CpuHz=3600000949(*) cErrors=0
00:00:00.142153 TM: GIP - CPU: iCpuSet=0x4 idCpu=0x4 idApic=0x4 iGipCpu=0x5 i64TSCDelta=0 enmState=3 u64CpuHz=3600001155(*) cErrors=0
00:00:00.142159 TM: GIP - CPU: iCpuSet=0x5 idCpu=0x5 idApic=0x5 iGipCpu=0x0 i64TSCDelta=0 enmState=3 u64CpuHz=3600001324(*) cErrors=0
00:00:00.142164 TM: GIP - CPU: iCpuSet=0x6 idCpu=0x6 idApic=0x6 iGipCpu=0x2 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.142170 TM: GIP - CPU: iCpuSet=0x7 idCpu=0x7 idApic=0x7 iGipCpu=0x1 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.142223 TM: cTSCTicksPerSecond=3 600 001 324 (0xd693a92c) enmTSCMode=1 (VirtTscEmulated)
00:00:00.142225 TM: TSCTiedToExecution=false TSCNotTiedToHalt=false
00:00:00.143181 EMR3Init: fIemExecutesAll=false fGuruOnTripleFault=true 
00:00:00.143884 IEM: TargetCpu=CURRENT, Microarch=Intel_Core7_KabyLake
00:00:00.144220 GIM: Using provider 'KVM' (Implementation version: 0)
00:00:00.144233 CPUM: SetGuestCpuIdFeature: Enabled Hypervisor Present bit
00:00:00.144318 AIOMgr: Default manager type is 'Async'
00:00:00.144325 AIOMgr: Default file backend is 'NonBuffered'
00:00:00.144679 BlkCache: Cache successfully initialized. Cache size is 5242880 bytes
00:00:00.144693 BlkCache: Cache commit interval is 10000 ms
00:00:00.144699 BlkCache: Cache commit threshold is 2621440 bytes
00:00:00.146223 fastpipe::VBoxDevicesRegister: u32Version=0x60001 pCallbacks->u32Version=0xffe30010
00:00:00.146423 PcBios: [SMP] BIOS with 4 CPUs
00:00:00.146446 PcBios: Using the 386+ BIOS image.
00:00:00.146521 PcBios: MPS table at 000e1300
00:00:00.148910 PcBios: fCheckShutdownStatusForSoftReset=true   fClearShutdownStatusOnHardReset=true 
00:00:00.152343 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.152363 SUP: seg #1: R X 0x00001000 LB 0x0001f000
00:00:00.152372 SUP: seg #2: R   0x00020000 LB 0x0000c000
00:00:00.152380 SUP: seg #3: RW  0x0002c000 LB 0x00001000
00:00:00.152387 SUP: seg #4: R   0x0002d000 LB 0x00002000
00:00:00.152394 SUP: seg #5: RW  0x0002f000 LB 0x00001000
00:00:00.152401 SUP: seg #6: R   0x00030000 LB 0x00001000
00:00:00.152408 SUP: seg #7: RWX 0x00031000 LB 0x00001000
00:00:00.152415 SUP: seg #8: R   0x00032000 LB 0x00002000
00:00:00.152490 SUP: Loaded Ld9BoxDDR0.r0 (C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.152501 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.152958 CPUM: SetGuestCpuIdFeature: Enabled xAPIC
00:00:00.152972 CPUM: SetGuestCpuIdFeature: Enabled x2APIC
00:00:00.153358 IOAPIC: Using implementation 2.0! Chipset type ICH9
00:00:00.153436 PIT: mode=3 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.153628 VMMDev: cbDefaultBudget: 535 351 424 (1fe8d080)
00:00:00.159028 Shared Folders service loaded
00:00:00.159966 Guest Control service loaded
00:00:00.161131 VIRTIOSCSI0: Targets=3 Bootable=true  (unimplemented) R0Enabled=true  RCEnabled=false
00:00:00.162010 DrvVD: Flushes will be ignored
00:00:00.162023 DrvVD: Async flushes will be passed to the disk
00:00:00.163124 VD: VDInit finished with VINF_SUCCESS
00:00:00.163310 VD: Opening the disk took 482980 ns
00:00:00.163399 DrvVD: Flushes will be ignored
00:00:00.163406 DrvVD: Async flushes will be passed to the disk
00:00:00.175191 VD: Opening the disk took 11055558 ns
00:00:00.175292 DrvVD: Flushes will be ignored
00:00:00.175300 DrvVD: Async flushes will be passed to the disk
00:00:00.185059 VD: Opening the disk took 8889562 ns
00:00:00.185312 IntNet#0: szNetwork={HostInterfaceNetworking-Realtek PCIe GBE Family Controller} enmTrunkType=3 szTrunk={\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}} fFlags=0x8000 cbRecv=325632 cbSend=196608 fIgnoreConnectFailure=false
00:00:00.208062 fastpipe: load host successs mod=00000000061606d0, path=C:\Program Files\ldplayer9box\libOpenglRender.dll
00:00:00.208085 fastpipe: GetFunctionAddr success mod=00000000061606d0, lpszFuncName=OnLoad
00:00:00.208441 fastpipe: load host successs mod=0000000006160a90, path=C:\Program Files\ldplayer9box\host_manager.dll
00:00:00.208452 fastpipe: GetFunctionAddr success mod=0000000006160a90, lpszFuncName=OnLoad
00:00:00.209762 PGM: The CPU physical address width is 39 bits
00:00:00.209781 PGM: PGMR3InitFinalize: 4 MB PSE mask 0000007fffffffff -> VINF_SUCCESS
00:00:00.209971 TM: TMR3InitFinalize: fTSCModeSwitchAllowed=true 
00:00:00.210261 VMM: Thread-context hooks unavailable
00:00:00.210273 VMM: RTThreadPreemptIsPending() can be trusted
00:00:00.210294 VMM: Kernel preemption is possible
00:00:00.213530 HM: fWorldSwitcher=0x0 (fIbpbOnVmExit=false fIbpbOnVmEntry=false fL1dFlushOnVmEntry=false); fL1dFlushOnSched=false fMdsClearOnVmEntry=false
00:00:00.213554 HM: Using VT-x implementation 3.0
00:00:00.213559 HM: Max resume loops                  = 8192
00:00:00.213560 HM: Host CR4                          = 0x370678
00:00:00.213560 HM: Host EFER                         = 0xd01
00:00:00.213561 HM: MSR_IA32_SMM_MONITOR_CTL          = 0x0
00:00:00.213562 HM: MSR_IA32_FEATURE_CONTROL          = 0x5
00:00:00.213563 HM:   LOCK
00:00:00.213563 HM:   VMXON
00:00:00.213564 HM: MSR_IA32_VMX_BASIC                = 0xda040000000004
00:00:00.213565 HM:   VMCS id                           = 0x4
00:00:00.213567 HM:   VMCS size                         = 1024 bytes
00:00:00.213568 HM:   VMCS physical address limit       = None
00:00:00.213569 HM:   VMCS memory type                  = Write Back (WB)
00:00:00.213570 HM:   Dual-monitor treatment support    = true 
00:00:00.213570 HM:   OUTS & INS instruction-info       = true 
00:00:00.213571 HM:   Supports true-capability MSRs     = true 
00:00:00.213571 HM:   VM-entry Xcpt error-code optional = false
00:00:00.213574 HM: MSR_IA32_VMX_PINBASED_CTLS        = 0x7f00000016
00:00:00.213575 HM:   EXT_INT_EXIT
00:00:00.213576 HM:   NMI_EXIT
00:00:00.213576 HM:   VIRTUAL_NMI
00:00:00.213576 HM:   PREEMPT_TIMER
00:00:00.213577 HM:   POSTED_INT (must be cleared)
00:00:00.213577 HM: MSR_IA32_VMX_PROCBASED_CTLS       = 0xfff9fffe0401e172
00:00:00.213578 HM:   INT_WINDOW_EXIT
00:00:00.213579 HM:   USE_TSC_OFFSETTING
00:00:00.213602 HM:   HLT_EXIT
00:00:00.213602 HM:   INVLPG_EXIT
00:00:00.213603 HM:   MWAIT_EXIT
00:00:00.213603 HM:   RDPMC_EXIT
00:00:00.213604 HM:   RDTSC_EXIT
00:00:00.213604 HM:   CR3_LOAD_EXIT (must be set)
00:00:00.213605 HM:   CR3_STORE_EXIT (must be set)
00:00:00.213606 HM:   CR8_LOAD_EXIT
00:00:00.213606 HM:   CR8_STORE_EXIT
00:00:00.213607 HM:   USE_TPR_SHADOW
00:00:00.213607 HM:   NMI_WINDOW_EXIT
00:00:00.213608 HM:   MOV_DR_EXIT
00:00:00.213608 HM:   UNCOND_IO_EXIT
00:00:00.213608 HM:   USE_IO_BITMAPS
00:00:00.213609 HM:   MONITOR_TRAP_FLAG
00:00:00.213609 HM:   USE_MSR_BITMAPS
00:00:00.213610 HM:   MONITOR_EXIT
00:00:00.213610 HM:   PAUSE_EXIT
00:00:00.213610 HM:   USE_SECONDARY_CTLS
00:00:00.213613 HM: MSR_IA32_VMX_PROCBASED_CTLS2      = 0x5ffcff00000000
00:00:00.213614 HM:   VIRT_APIC_ACCESS
00:00:00.213614 HM:   EPT
00:00:00.213615 HM:   DESC_TABLE_EXIT
00:00:00.213615 HM:   RDTSCP
00:00:00.213616 HM:   VIRT_X2APIC_MODE
00:00:00.213616 HM:   VPID
00:00:00.213616 HM:   WBINVD_EXIT
00:00:00.213617 HM:   UNRESTRICTED_GUEST
00:00:00.213617 HM:   APIC_REG_VIRT (must be cleared)
00:00:00.213618 HM:   VIRT_INT_DELIVERY (must be cleared)
00:00:00.213618 HM:   PAUSE_LOOP_EXIT
00:00:00.213619 HM:   RDRAND_EXIT
00:00:00.213619 HM:   INVPCID
00:00:00.213619 HM:   VMFUNC
00:00:00.213622 HM:   VMCS_SHADOWING
00:00:00.213622 HM:   ENCLS_EXIT
00:00:00.213623 HM:   RDSEED_EXIT
00:00:00.213623 HM:   PML
00:00:00.213623 HM:   EPT_VE
00:00:00.213624 HM:   CONCEAL_VMX_FROM_PT
00:00:00.213624 HM:   XSAVES_XRSTORS
00:00:00.213624 HM:   MODE_BASED_EPT_PERM
00:00:00.213625 HM:   SPPTP_EPT (must be cleared)
00:00:00.213625 HM:   PT_EPT (must be cleared)
00:00:00.213626 HM:   TSC_SCALING (must be cleared)
00:00:00.213626 HM:   USER_WAIT_PAUSE (must be cleared)
00:00:00.213627 HM:   ENCLV_EXIT (must be cleared)
00:00:00.213627 HM: MSR_IA32_VMX_ENTRY_CTLS           = 0x3ffff000011ff
00:00:00.213628 HM:   LOAD_DEBUG (must be set)
00:00:00.213628 HM:   IA32E_MODE_GUEST
00:00:00.213629 HM:   ENTRY_TO_SMM
00:00:00.213629 HM:   DEACTIVATE_DUAL_MON
00:00:00.213629 HM:   LOAD_PERF_MSR
00:00:00.213630 HM:   LOAD_PAT_MSR
00:00:00.213630 HM:   LOAD_EFER_MSR
00:00:00.213630 HM:   LOAD_BNDCFGS_MSR
00:00:00.213631 HM:   CONCEAL_VMX_FROM_PT
00:00:00.213631 HM:   LOAD_RTIT_CTL_MSR (must be cleared)
00:00:00.213632 HM: MSR_IA32_VMX_EXIT_CTLS            = 0x1ffffff00036dff
00:00:00.213633 HM:   SAVE_DEBUG (must be set)
00:00:00.213633 HM:   HOST_ADDR_SPACE_SIZE
00:00:00.213633 HM:   LOAD_PERF_MSR
00:00:00.213634 HM:   ACK_EXT_INT
00:00:00.213634 HM:   SAVE_PAT_MSR
00:00:00.213634 HM:   LOAD_PAT_MSR
00:00:00.213635 HM:   SAVE_EFER_MSR
00:00:00.213635 HM:   LOAD_EFER_MSR
00:00:00.213636 HM:   SAVE_PREEMPT_TIMER
00:00:00.213636 HM:   CLEAR_BNDCFGS_MSR
00:00:00.213636 HM:   CONCEAL_VMX_FROM_PT
00:00:00.213637 HM:   CLEAR_RTIT_CTL_MSR (must be cleared)
00:00:00.213637 HM: MSR_IA32_VMX_TRUE_PINBASED_CTLS   = 0x7f00000016
00:00:00.213638 HM: MSR_IA32_VMX_TRUE_PROCBASED_CTLS  = 0xfff9fffe04006172
00:00:00.213639 HM: MSR_IA32_VMX_TRUE_ENTRY_CTLS      = 0x3ffff000011fb
00:00:00.213640 HM: MSR_IA32_VMX_TRUE_EXIT_CTLS       = 0x1ffffff00036dfb
00:00:00.213642 HM: MSR_IA32_VMX_MISC                 = 0x7004c1e7
00:00:00.213643 HM:   PREEMPT_TIMER_TSC                 = 0x7
00:00:00.213644 HM:   EXIT_SAVE_EFER_LMA                = true 
00:00:00.213645 HM:   ACTIVITY_STATES                   = 0x7 ( HLT SHUTDOWN SIPI_WAIT )
00:00:00.213645 HM:   INTEL_PT                          = true 
00:00:00.213646 HM:   SMM_READ_SMBASE_MSR               = true 
00:00:00.213647 HM:   CR3_TARGET                        = 0x4
00:00:00.213647 HM:   MAX_MSR                           = 0x0 ( 512 )
00:00:00.213648 HM:   VMXOFF_BLOCK_SMI                  = true 
00:00:00.213648 HM:   VMWRITE_ALL                       = true 
00:00:00.213649 HM:   ENTRY_INJECT_SOFT_INT             = 0x1
00:00:00.213649 HM:   MSEG_ID                           = 0x0
00:00:00.213650 HM: MSR_IA32_VMX_VMCS_ENUM            = 0x2e
00:00:00.213650 HM:   HIGHEST_IDX                       = 0x17
00:00:00.213651 HM: MSR_IA32_VMX_EPT_VPID_CAP         = 0xf0106734141
00:00:00.213652 HM:   RWX_X_ONLY
00:00:00.213652 HM:   PAGE_WALK_LENGTH_4
00:00:00.213652 HM:   EMT_UC
00:00:00.213652 HM:   EMT_WB
00:00:00.213653 HM:   PDE_2M
00:00:00.213653 HM:   PDPTE_1G
00:00:00.213654 HM:   INVEPT
00:00:00.213654 HM:   EPT_ACCESS_DIRTY
00:00:00.213668 HM:   ADVEXITINFO_EPT
00:00:00.213668 HM:   INVEPT_SINGLE_CONTEXT
00:00:00.213668 HM:   INVEPT_ALL_CONTEXTS
00:00:00.213669 HM:   INVVPID
00:00:00.213669 HM:   INVVPID_INDIV_ADDR
00:00:00.213670 HM:   INVVPID_SINGLE_CONTEXT
00:00:00.213670 HM:   INVVPID_ALL_CONTEXTS
00:00:00.213670 HM:   INVVPID_SINGLE_CONTEXT_RETAIN_GLOBALS
00:00:00.213671 HM: MSR_IA32_VMX_VMFUNC               = 0x1
00:00:00.213671 HM:   EPTP_SWITCHING
00:00:00.213672 HM: MSR_IA32_VMX_CR0_FIXED0           = 0x80000021
00:00:00.213673 HM: MSR_IA32_VMX_CR0_FIXED1           = 0xffffffff
00:00:00.213673 HM: MSR_IA32_VMX_CR4_FIXED0           = 0x2000
00:00:00.213674 HM: MSR_IA32_VMX_CR4_FIXED1           = 0x3767ff
00:00:00.213675 HM: APIC-access page physaddr         = 0x00000005c863c000
00:00:00.213676 HM: VCPU  0: MSR bitmap physaddr      = 0x0000000756440000
00:00:00.213692 HM: VCPU  0: VMCS physaddr            = 0x000000072093d000
00:00:00.213693 HM: VCPU  1: MSR bitmap physaddr      = 0x0000000034544000
00:00:00.213694 HM: VCPU  1: VMCS physaddr            = 0x00000007ec541000
00:00:00.213695 HM: VCPU  2: MSR bitmap physaddr      = 0x000000067d548000
00:00:00.213696 HM: VCPU  2: VMCS physaddr            = 0x00000006cc645000
00:00:00.213697 HM: VCPU  3: MSR bitmap physaddr      = 0x00000005d5a4c000
00:00:00.213698 HM: VCPU  3: VMCS physaddr            = 0x00000001bd049000
00:00:00.213699 HM: Guest support: 32-bit and 64-bit
00:00:00.213712 HM: Supports VMCS EFER fields         = true 
00:00:00.213713 HM: Enabled VMX
00:00:00.213716 CPUM: SetGuestCpuIdFeature: Enabled SYSENTER/EXIT
00:00:00.213717 CPUM: SetGuestCpuIdFeature: Enabled PAE
00:00:00.213717 CPUM: SetGuestCpuIdFeature: Enabled LONG MODE
00:00:00.213718 CPUM: SetGuestCpuIdFeature: Enabled SYSCALL/RET
00:00:00.213718 CPUM: SetGuestCpuIdFeature: Enabled LAHF/SAHF
00:00:00.213719 CPUM: SetGuestCpuIdFeature: Enabled NX
00:00:00.213719 HM: Enabled nested paging
00:00:00.213720 HM:   EPT flush type                  = Single context
00:00:00.213721 HM: Enabled unrestricted guest execution
00:00:00.213721 HM: Enabled large page support
00:00:00.213721 HM: Enabled VPID
00:00:00.213722 HM:   VPID flush type                 = Single context
00:00:00.213723 HM: Enabled VMX-preemption timer (cPreemptTimerShift=7)
00:00:00.213736 HM: VT-x/AMD-V init method: Local
00:00:00.213738 EM: Exit history optimizations: enabled=true  enabled-r0=true  enabled-r0-no-preemption=false
00:00:00.213804 APIC: fPostedIntrsEnabled=false fVirtApicRegsEnabled=false fSupportsTscDeadline=false
00:00:00.213817 TMR3UtcNow: nsNow=1 754 320 041 609 983 400 nsPrev=0 -> cNsDelta=1 754 320 041 609 983 400 (offLag=0 offVirtualSync=0 offVirtualSyncGivenUp=0, NowAgain=1 754 320 041 609 983 400)
00:00:00.213830 VMM: fUsePeriodicPreemptionTimers=false
00:00:00.213890 CPUM: Logical host processors: 8 present, 8 max, 8 online, online mask: 00000000000000ff
00:00:00.213892 CPUM: Physical host cores: 4
00:00:00.213892 ************************* CPUID dump ************************
00:00:00.213900          Raw Standard CPUID Leaves
00:00:00.213901      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.213904 Gst: 00000000/0000  00000016 756e6547 6c65746e 49656e69
00:00:00.213906 Hst:                00000016 756e6547 6c65746e 49656e69
00:00:00.213907 Gst: 00000001/0000  000906e9 00040800 d6fa2203 178bfbff
00:00:00.213909 Hst:                000906e9 06100800 7ffafbff bfebfbff
00:00:00.213926 Gst: 00000002/0000  76036301 00f0b5ff 00000000 00c30000
00:00:00.213927 Hst:                76036301 00f0b5ff 00000000 00c30000
00:00:00.213928 Gst: 00000003/0000  00000000 00000000 00000000 00000000
00:00:00.213930 Hst:                00000000 00000000 00000000 00000000
00:00:00.213931 Gst: 00000004/0000  0c000121 01c0003f 0000003f 00000000
00:00:00.213932 Hst:                1c004121 01c0003f 0000003f 00000000
00:00:00.213934 Gst: 00000004/0001  0c000122 01c0003f 0000003f 00000000
00:00:00.213935 Hst:                1c004122 01c0003f 0000003f 00000000
00:00:00.213936 Gst: 00000004/0002  0c000143 00c0003f 000003ff 00000000
00:00:00.213938 Hst:                1c004143 00c0003f 000003ff 00000000
00:00:00.213939 Gst: 00000004/0003  0c000163 03c0003f 00001fff 00000006
00:00:00.213940 Hst:                1c03c163 03c0003f 00001fff 00000006
00:00:00.213942 Gst: 00000004/0004  0c000000 00000000 00000000 00000000
00:00:00.213942 Hst:                00000000 00000000 00000000 00000000
00:00:00.213956 Gst: 00000005/0000  00000000 00000000 00000000 00000000
00:00:00.213957 Hst:                00000040 00000040 00000003 00142120
00:00:00.213959 Gst: 00000006/0000  00000000 00000000 00000000 00000000
00:00:00.213960 Hst:                000027f7 00000002 00000009 00000000
00:00:00.213961 Gst: 00000007/0000  00000000 00842421 00000000 1c000400
00:00:00.213962 Hst:                00000000 029c6fbf 00000000 9c002400
00:00:00.213964 Gst: 00000007/0001  00000000 00000000 00000000 00000000
00:00:00.213965 Hst:                00000000 00000000 00000000 00000000
00:00:00.213966 Gst: 00000008/0000  00000000 00000000 00000000 00000000
00:00:00.213967 Hst:                00000000 00000000 00000000 00000000
00:00:00.213968 Gst: 00000009/0000  00000000 00000000 00000000 00000000
00:00:00.213970 Hst:                00000000 00000000 00000000 00000000
00:00:00.213971 Gst: 0000000a/0000  00000000 00000000 00000000 00000000
00:00:00.213971 Hst:                07300404 00000000 00000000 00000603
00:00:00.213972 Gst: 0000000b/0000  00000000 00000001 00000100 00000000
00:00:00.213973 Hst:                00000001 00000002 00000100 00000006
00:00:00.213974 Gst: 0000000b/0001  00000002 00000004 00000201 00000000
00:00:00.213975 Hst:                00000004 00000008 00000201 00000006
00:00:00.213976 Gst: 0000000b/0002  00000000 00000000 00000002 00000000
00:00:00.213978 Hst:                00000000 00000000 00000002 00000006
00:00:00.213979 Gst: 0000000c/0000  00000000 00000000 00000000 00000000
00:00:00.213980 Hst:                00000000 00000000 00000000 00000000
00:00:00.213981 Gst: 0000000d/0000  00000007 00000340 00000340 00000000
00:00:00.213982 Hst:                0000001f 00000440 00000440 00000000
00:00:00.213983 Gst: 0000000d/0001  00000000 00000440 00000000 00000000
00:00:00.213984 Hst:                0000000f 00000440 00000100 00000000
00:00:00.213985 Gst: 0000000d/0002  00000100 00000240 00000000 00000000
00:00:00.213987 Hst:                00000100 00000240 00000000 00000000
00:00:00.213988 Gst: 0000000d/0003  00000000 00000000 00000000 00000000
00:00:00.213989 Hst:                00000040 000003c0 00000000 00000000
00:00:00.213990 Gst: 0000000d/0004  00000000 00000000 00000000 00000000
00:00:00.213991 Hst:                00000040 00000400 00000000 00000000
00:00:00.213992 Gst: 0000000d/0005  00000000 00000000 00000000 00000000
00:00:00.213993 Hst:                00000000 00000000 00000000 00000000
00:00:00.213994 Gst: 0000000d/0006  00000000 00000000 00000000 00000000
00:00:00.213995 Hst:                00000000 00000000 00000000 00000000
00:00:00.213996 Gst: 0000000d/0007  00000000 00000000 00000000 00000000
00:00:00.213997 Hst:                00000000 00000000 00000000 00000000
00:00:00.213998 Gst: 0000000d/0008  00000000 00000000 00000000 00000000
00:00:00.213999 Hst:                00000080 00000000 00000001 00000000
00:00:00.214000 Gst: 0000000d/0009  00000000 00000000 00000000 00000000
00:00:00.214001 Hst:                00000000 00000000 00000000 00000000
00:00:00.214019 Gst: 0000000e/0000  00000000 00000000 00000000 00000000
00:00:00.214021 Hst:                00000000 00000000 00000000 00000000
00:00:00.214022 Gst: 0000000f/0000  00000000 00000000 00000000 00000000
00:00:00.214023 Hst:                00000000 00000000 00000000 00000000
00:00:00.214024 Gst: 00000010/0000  00000000 00000000 00000000 00000000
00:00:00.214026 Hst:                00000000 00000000 00000000 00000000
00:00:00.214027 Gst: 00000011/0000  00000000 00000000 00000000 00000000
00:00:00.214028 Hst:                00000000 00000000 00000000 00000000
00:00:00.214029 Gst: 00000012/0000  00000000 00000000 00000000 00000000
00:00:00.214030 Hst:                00000000 00000000 00000000 00000000
00:00:00.214046 Gst: 00000013/0000  00000000 00000000 00000000 00000000
00:00:00.214047 Hst:                00000000 00000000 00000000 00000000
00:00:00.214048 Gst: 00000014/0000  00000000 00000000 00000000 00000000
00:00:00.214049 Hst:                00000001 0000000f 00000007 00000000
00:00:00.214050 Hst: 00000015/0000  00000002 0000012c 00000000 00000000
00:00:00.214052 Hst: 00000016/0000  00000e10 00001068 00000064 00000000
00:00:00.214054                                Name: GenuineIntel
00:00:00.214056                            Supports: 0x00000000-0x00000016
00:00:00.214061                              Family:  6 	Extended: 0 	Effective: 6
00:00:00.214064                               Model: 14 	Extended: 9 	Effective: 158
00:00:00.214066                            Stepping: 9
00:00:00.214067                                Type: 0 (primary)
00:00:00.214069                             APIC ID: 0x00
00:00:00.214071                        Logical CPUs: 4
00:00:00.214073                        CLFLUSH Size: 8
00:00:00.214074                            Brand ID: 0x00
00:00:00.214076 Features
00:00:00.214077   Mnemonic - Description                                  = guest (host)
00:00:00.214080   FPU - x87 FPU on Chip                                   = 1 (1)
00:00:00.214082   VME - Virtual 8086 Mode Enhancements                    = 1 (1)
00:00:00.214084   DE - Debugging extensions                               = 1 (1)
00:00:00.214086   PSE - Page Size Extension                               = 1 (1)
00:00:00.214101   TSC - Time Stamp Counter                                = 1 (1)
00:00:00.214104   MSR - Model Specific Registers                          = 1 (1)
00:00:00.214106   PAE - Physical Address Extension                        = 1 (1)
00:00:00.214108   MCE - Machine Check Exception                           = 1 (1)
00:00:00.214125   CX8 - CMPXCHG8B instruction                             = 1 (1)
00:00:00.214128   APIC - APIC On-Chip                                     = 1 (1)
00:00:00.214130   SEP - SYSENTER and SYSEXIT Present                      = 1 (1)
00:00:00.214132   MTRR - Memory Type Range Registers                      = 1 (1)
00:00:00.214134   PGE - PTE Global Bit                                    = 1 (1)
00:00:00.214137   MCA - Machine Check Architecture                        = 1 (1)
00:00:00.214139   CMOV - Conditional Move instructions                    = 1 (1)
00:00:00.214141   PAT - Page Attribute Table                              = 1 (1)
00:00:00.214144   PSE-36 - 36-bit Page Size Extension                     = 1 (1)
00:00:00.214146   PSN - Processor Serial Number                           = 0 (0)
00:00:00.214148   CLFSH - CLFLUSH instruction                             = 1 (1)
00:00:00.214150   DS - Debug Store                                        = 0 (1)
00:00:00.214153   ACPI - Thermal Mon. & Soft. Clock Ctrl.                 = 0 (1)
00:00:00.214155   MMX - Intel MMX Technology                              = 1 (1)
00:00:00.214172   FXSR - FXSAVE and FXRSTOR instructions                  = 1 (1)
00:00:00.214174   SSE - SSE support                                       = 1 (1)
00:00:00.214176   SSE2 - SSE2 support                                     = 1 (1)
00:00:00.214179   SS - Self Snoop                                         = 0 (1)
00:00:00.214180   HTT - Hyper-Threading Technology                        = 1 (1)
00:00:00.214182   TM - Therm. Monitor                                     = 0 (1)
00:00:00.214185   PBE - Pending Break Enabled                             = 0 (1)
00:00:00.214187   SSE3 - SSE3 support                                     = 1 (1)
00:00:00.214190   PCLMUL - PCLMULQDQ support (for AES-GCM)                = 1 (1)
00:00:00.214192   DTES64 - DS Area 64-bit Layout                          = 0 (1)
00:00:00.214194   MONITOR - MONITOR/MWAIT instructions                    = 0 (1)
00:00:00.214196   CPL-DS - CPL Qualified Debug Store                      = 0 (1)
00:00:00.214198   VMX - Virtual Machine Extensions                        = 0 (1)
00:00:00.214200   SMX - Safer Mode Extensions                             = 0 (1)
00:00:00.214202   EST - Enhanced SpeedStep Technology                     = 0 (1)
00:00:00.214204   TM2 - Terminal Monitor 2                                = 0 (1)
00:00:00.214206   SSSE3 - Supplemental Streaming SIMD Extensions 3        = 1 (1)
00:00:00.214207   CNTX-ID - L1 Context ID                                 = 0 (0)
00:00:00.214209   SDBG - Silicon Debug interface                          = 0 (1)
00:00:00.214211   FMA - Fused Multiply Add extensions                     = 0 (1)
00:00:00.214214   CX16 - CMPXCHG16B instruction                           = 1 (1)
00:00:00.214216   TPRUPDATE - xTPR Update Control                         = 0 (1)
00:00:00.214218   PDCM - Perf/Debug Capability MSR                        = 0 (1)
00:00:00.214220   PCID - Process Context Identifiers                      = 1 (1)
00:00:00.214222   DCA - Direct Cache Access                               = 0 (0)
00:00:00.214224   SSE4_1 - SSE4_1 support                                 = 1 (1)
00:00:00.214226   SSE4_2 - SSE4_2 support                                 = 1 (1)
00:00:00.214229   X2APIC - x2APIC support                                 = 1 (1)
00:00:00.214246   MOVBE - MOVBE instruction                               = 1 (1)
00:00:00.214248   POPCNT - POPCNT instruction                             = 1 (1)
00:00:00.214250   TSCDEADL - Time Stamp Counter Deadline                  = 0 (1)
00:00:00.214251   AES - AES instructions                                  = 1 (1)
00:00:00.214252   XSAVE - XSAVE instruction                               = 1 (1)
00:00:00.214254   OSXSAVE - OSXSAVE instruction                           = 0 (1)
00:00:00.214269   AVX - AVX support                                       = 1 (1)
00:00:00.214270   F16C - 16-bit floating point conversion instructions    = 0 (1)
00:00:00.214271   RDRAND - RDRAND instruction                             = 1 (1)
00:00:00.214274   HVP - Hypervisor Present (we're a guest)                = 1 (0)
00:00:00.214276 Structured Extended Feature Flags Enumeration (leaf 7):
00:00:00.214277   Mnemonic - Description                                  = guest (host)
00:00:00.214278   FSGSBASE - RDFSBASE/RDGSBASE/WRFSBASE/WRGSBASE instr.   = 1 (1)
00:00:00.214279   TSCADJUST - Supports MSR_IA32_TSC_ADJUST                = 0 (1)
00:00:00.214281   SGX - Supports Software Guard Extensions                = 0 (1)
00:00:00.214284   BMI1 - Advanced Bit Manipulation extension 1            = 0 (1)
00:00:00.214285   HLE - Hardware Lock Elision                             = 0 (1)
00:00:00.214287   AVX2 - Advanced Vector Extensions 2                     = 1 (1)
00:00:00.214290   FDP_EXCPTN_ONLY - FPU DP only updated on exceptions     = 0 (0)
00:00:00.214291   SMEP - Supervisor Mode Execution Prevention             = 0 (1)
00:00:00.214292   BMI2 - Advanced Bit Manipulation extension 2            = 0 (1)
00:00:00.214293   ERMS - Enhanced REP MOVSB/STOSB instructions            = 0 (1)
00:00:00.214295   INVPCID - INVPCID instruction                           = 1 (1)
00:00:00.214297   RTM - Restricted Transactional Memory                   = 0 (1)
00:00:00.214300   PQM - Platform Quality of Service Monitoring            = 0 (0)
00:00:00.214301   DEPFPU_CS_DS - Deprecates FPU CS, FPU DS values if set  = 1 (1)
00:00:00.214302   MPE - Intel Memory Protection Extensions                = 0 (1)
00:00:00.214303   PQE - Platform Quality of Service Enforcement           = 0 (0)
00:00:00.214304   AVX512F - AVX512 Foundation instructions                = 0 (0)
00:00:00.214305   RDSEED - RDSEED instruction                             = 1 (1)
00:00:00.214307   ADX - ADCX/ADOX instructions                            = 0 (1)
00:00:00.214308   SMAP - Supervisor Mode Access Prevention                = 0 (1)
00:00:00.214308   CLFLUSHOPT - CLFLUSHOPT (Cache Line Flush) instruction  = 1 (1)
00:00:00.214309   INTEL_PT - Intel Processor Trace                        = 0 (1)
00:00:00.214310   AVX512PF - AVX512 Prefetch instructions                 = 0 (0)
00:00:00.214311   AVX512ER - AVX512 Exponential & Reciprocal instructions = 0 (0)
00:00:00.214312   AVX512CD - AVX512 Conflict Detection instructions       = 0 (0)
00:00:00.214313   SHA - Secure Hash Algorithm extensions                  = 0 (0)
00:00:00.214314   PREFETCHWT1 - PREFETCHWT1 instruction                   = 0 (0)
00:00:00.214315   UMIP - User mode insturction prevention                 = 0 (0)
00:00:00.214315   PKU - Protection Key for Usermode pages                 = 0 (0)
00:00:00.214316   OSPKE - CR4.PKU mirror                                  = 0 (0)
00:00:00.214317   MAWAU - Value used by BNDLDX & BNDSTX                   = 0x0 (0x0)
00:00:00.214318   RDPID - Read processor ID support                       = 0 (0)
00:00:00.214319   SGX_LC - Supports SGX Launch Configuration              = 0 (0)
00:00:00.214320   MD_CLEAR - Supports MDS related buffer clearing         = 1 (1)
00:00:00.214321   13 - Reserved                                           = 0 (1)
00:00:00.214322   IBRS_IBPB - IA32_SPEC_CTRL.IBRS and IA32_PRED_CMD.IBPB  = 1 (1)
00:00:00.214323   STIBP - Supports IA32_SPEC_CTRL.STIBP                   = 1 (1)
00:00:00.214324   FLUSH_CMD - Supports IA32_FLUSH_CMD                     = 1 (1)
00:00:00.214325   ARCHCAP - Supports IA32_ARCH_CAP                        = 0 (0)
00:00:00.214326   CORECAP - Supports IA32_CORE_CAP                        = 0 (0)
00:00:00.214327   SSBD - Supports IA32_SPEC_CTRL.SSBD                     = 0 (1)
00:00:00.214328 Processor Extended State Enumeration (leaf 0xd):
00:00:00.214329    XSAVE area cur/max size by XCR0, guest: 0x340/0x340
00:00:00.214330     XSAVE area cur/max size by XCR0, host: 0x440/0x440
00:00:00.214330                    Valid XCR0 bits, guest: 0x00000000`00000007 ( x87 SSE YMM_Hi128 )
00:00:00.214332                     Valid XCR0 bits, host: 0x00000000`0000001f ( x87 SSE YMM_Hi128 BNDREGS BNDCSR )
00:00:00.214335                     XSAVE features, guest:
00:00:00.214336                      XSAVE features, host: XSAVEOPT XSAVEC XGETBC1 XSAVES
00:00:00.214340       XSAVE area cur size XCR0|XSS, guest: 0x440
00:00:00.214340        XSAVE area cur size XCR0|XSS, host: 0x440
00:00:00.214341                Valid IA32_XSS bits, guest: 0x00000000`00000000
00:00:00.214342                 Valid IA32_XSS bits, host: 0x00000100`00000000 ( 40 )
00:00:00.214343   State #2, guest: off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.214345   State #2, host:  off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.214346   State #3, host:  off=0x03c0, cb=0x0040 IA32_XSS-bit -- BNDREGS
00:00:00.214347   State #4, host:  off=0x0400, cb=0x0040 IA32_XSS-bit -- BNDCSR
00:00:00.214349   State #8, host:  off=0x0000, cb=0x0080 XCR0-bit -- 8
00:00:00.214357          Unknown CPUID Leaves
00:00:00.214358      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.214358 Gst: 00000014/0001  00000000 00000000 00000000 00000000
00:00:00.214359 Hst:                02490002 003f3fff 00000000 00000000
00:00:00.214360 Gst: 00000014/0002  00000000 00000000 00000000 00000000
00:00:00.214361 Hst:                00000000 00000000 00000000 00000000
00:00:00.214362 Gst: 00000015/0000  00000000 00000000 00000000 00000000
00:00:00.214363 Hst:                00000002 0000012c 00000000 00000000
00:00:00.214364 Gst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:00.214364 Hst:                00000e10 00001068 00000064 00000000
00:00:00.214366          Raw Hypervisor CPUID Leaves
00:00:00.214366      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.214367 Gst: 40000000/0000  40000001 4b4d564b 564b4d56 0000004d
00:00:00.214368 Hst:                00000e10 00001068 00000064 00000000
00:00:00.214369 Gst: 40000001/0000  01000089 00000000 00000000 00000000
00:00:00.214370 Hst:                00000e10 00001068 00000064 00000000
00:00:00.214371          Raw Extended CPUID Leaves
00:00:00.214371      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.214372 Gst: 80000000/0000  80000008 00000000 00000000 00000000
00:00:00.214372 Hst:                80000008 00000000 00000000 00000000
00:00:00.214373 Gst: 80000001/0000  00000000 00000000 00000121 28100800
00:00:00.214374 Hst:                00000000 00000000 00000121 2c100800
00:00:00.214376 Gst: 80000002/0000  65746e49 2952286c 726f4320 4d542865
00:00:00.214377 Hst:                65746e49 2952286c 726f4320 4d542865
00:00:00.214378 Gst: 80000003/0000  37692029 3037372d 50432030 20402055
00:00:00.214379 Hst:                37692029 3037372d 50432030 20402055
00:00:00.214380 Gst: 80000004/0000  30362e33 007a4847 00000000 00000000
00:00:00.214381 Hst:                30362e33 007a4847 00000000 00000000
00:00:00.214382 Gst: 80000005/0000  00000000 00000000 00000000 00000000
00:00:00.214383 Hst:                00000000 00000000 00000000 00000000
00:00:00.214384 Gst: 80000006/0000  00000000 00000000 01006040 00000000
00:00:00.214385 Hst:                00000000 00000000 01006040 00000000
00:00:00.214385 Gst: 80000007/0000  00000000 00000000 00000000 00000100
00:00:00.214386 Hst:                00000000 00000000 00000000 00000100
00:00:00.214387 Gst: 80000008/0000  00003027 00000000 00000000 00000000
00:00:00.214388 Hst:                00003027 00000000 00000000 00000000
00:00:00.214389 Ext Name:                        
00:00:00.214389 Ext Supports:                    0x80000000-0x80000008
00:00:00.214390 Family:                          0  	Extended: 0 	Effective: 0
00:00:00.214391 Model:                           0  	Extended: 0 	Effective: 0
00:00:00.214391 Stepping:                        0
00:00:00.214392 Brand ID:                        0x000
00:00:00.214392 Ext Features
00:00:00.214393   Mnemonic - Description                                  = guest (host)
00:00:00.214393   FPU - x87 FPU on Chip                                   = 0 (0)
00:00:00.214394   VME - Virtual 8086 Mode Enhancements                    = 0 (0)
00:00:00.214395   DE - Debugging extensions                               = 0 (0)
00:00:00.214396   PSE - Page Size Extension                               = 0 (0)
00:00:00.214397   TSC - Time Stamp Counter                                = 0 (0)
00:00:00.214398   MSR - K86 Model Specific Registers                      = 0 (0)
00:00:00.214399   PAE - Physical Address Extension                        = 0 (0)
00:00:00.214399   MCE - Machine Check Exception                           = 0 (0)
00:00:00.214400   CX8 - CMPXCHG8B instruction                             = 0 (0)
00:00:00.214401   APIC - APIC On-Chip                                     = 0 (0)
00:00:00.214402   SEP - SYSCALL/SYSRET                                    = 1 (1)
00:00:00.214403   MTRR - Memory Type Range Registers                      = 0 (0)
00:00:00.214404   PGE - PTE Global Bit                                    = 0 (0)
00:00:00.214404   MCA - Machine Check Architecture                        = 0 (0)
00:00:00.214405   CMOV - Conditional Move instructions                    = 0 (0)
00:00:00.214406   PAT - Page Attribute Table                              = 0 (0)
00:00:00.214407   PSE-36 - 36-bit Page Size Extension                     = 0 (0)
00:00:00.214408   NX - No-Execute/Execute-Disable                         = 1 (1)
00:00:00.214411   AXMMX - AMD Extensions to MMX instructions              = 0 (0)
00:00:00.214413   MMX - Intel MMX Technology                              = 0 (0)
00:00:00.214415   FXSR - FXSAVE and FXRSTOR Instructions                  = 0 (0)
00:00:00.214417   FFXSR - AMD fast FXSAVE and FXRSTOR instructions        = 0 (0)
00:00:00.214418   Page1GB - 1 GB large page                               = 0 (1)
00:00:00.214421   RDTSCP - RDTSCP instruction                             = 1 (1)
00:00:00.214423   LM - AMD64 Long Mode                                    = 1 (1)
00:00:00.214425   3DNOWEXT - AMD Extensions to 3DNow                      = 0 (0)
00:00:00.214427   3DNOW - AMD 3DNow                                       = 0 (0)
00:00:00.214430   LahfSahf - LAHF/SAHF support in 64-bit mode             = 1 (1)
00:00:00.214431   CmpLegacy - Core multi-processing legacy mode           = 0 (0)
00:00:00.214432   SVM - AMD Secure Virtual Machine extensions             = 0 (0)
00:00:00.214433   EXTAPIC - AMD Extended APIC registers                   = 0 (0)
00:00:00.214435   CR8L - AMD LOCK MOV CR0 means MOV CR8                   = 0 (0)
00:00:00.214436   ABM - AMD Advanced Bit Manipulation                     = 1 (1)
00:00:00.214438   SSE4A - SSE4A instructions                              = 0 (0)
00:00:00.214439   MISALIGNSSE - AMD Misaligned SSE mode                   = 0 (0)
00:00:00.214441   3DNOWPRF - AMD PREFETCH and PREFETCHW instructions      = 1 (1)
00:00:00.214442   OSVW - AMD OS Visible Workaround                        = 0 (0)
00:00:00.214445   IBS - Instruct Based Sampling                           = 0 (0)
00:00:00.214446   XOP - Extended Operation support                        = 0 (0)
00:00:00.214448   SKINIT - SKINIT, STGI, and DEV support                  = 0 (0)
00:00:00.214449   WDT - AMD Watchdog Timer support                        = 0 (0)
00:00:00.214450   LWP - Lightweight Profiling support                     = 0 (0)
00:00:00.214451   FMA4 - Four operand FMA instruction support             = 0 (0)
00:00:00.214452   NodeId - NodeId in MSR C001_100C                        = 0 (0)
00:00:00.214454   TBM - Trailing Bit Manipulation instructions            = 0 (0)
00:00:00.214455   TOPOEXT - Topology Extensions                           = 0 (0)
00:00:00.214456   PRFEXTCORE - Performance Counter Extensions support     = 0 (0)
00:00:00.214457   PRFEXTNB - NB Performance Counter Extensions support    = 0 (0)
00:00:00.214458   DATABPEXT - Data-access Breakpoint Extension            = 0 (0)
00:00:00.214459   PERFTSC - Performance Time Stamp Counter                = 0 (0)
00:00:00.214461   PCX_L2I - L2I/L3 Performance Counter Extensions         = 0 (0)
00:00:00.214462   MWAITX - MWAITX and MONITORX instructions               = 0 (0)
00:00:00.214464 Full Name:                       "Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz"
00:00:00.214465 TLB 2/4M Instr/Uni:              res0     0 entries
00:00:00.214466 TLB 2/4M Data:                   res0     0 entries
00:00:00.214467 TLB 4K Instr/Uni:                res0     0 entries
00:00:00.214467 TLB 4K Data:                     res0     0 entries
00:00:00.214468 L1 Instr Cache Line Size:        0 bytes
00:00:00.214468 L1 Instr Cache Lines Per Tag:    0
00:00:00.214469 L1 Instr Cache Associativity:    res0  
00:00:00.214469 L1 Instr Cache Size:             0 KB
00:00:00.214470 L1 Data Cache Line Size:         0 bytes
00:00:00.214470 L1 Data Cache Lines Per Tag:     0
00:00:00.214471 L1 Data Cache Associativity:     res0  
00:00:00.214471 L1 Data Cache Size:              0 KB
00:00:00.214472 L2 TLB 2/4M Instr/Uni:           off       0 entries
00:00:00.214473 L2 TLB 2/4M Data:                off       0 entries
00:00:00.214473 L2 TLB 4K Instr/Uni:             off       0 entries
00:00:00.214474 L2 TLB 4K Data:                  off       0 entries
00:00:00.214474 L2 Cache Line Size:              0 bytes
00:00:00.214475 L2 Cache Lines Per Tag:          0
00:00:00.214475 L2 Cache Associativity:          off   
00:00:00.214476 L2 Cache Size:                   0 KB
00:00:00.214477   TS - Temperature Sensor                                 = 0 (0)
00:00:00.214479   FID - Frequency ID control                              = 0 (0)
00:00:00.214480   VID - Voltage ID control                                = 0 (0)
00:00:00.214482   TscInvariant - Invariant Time Stamp Counter             = 1 (1)
00:00:00.214485   CBP - Core Performance Boost                            = 0 (0)
00:00:00.214486   EffFreqRO - Read-only Effective Frequency Interface     = 0 (0)
00:00:00.214487   ProcFdbkIf - Processor Feedback Interface               = 0 (0)
00:00:00.214489   ProcPwrRep - Core power reporting interface support     = 0 (0)
00:00:00.214491 Physical Address Width:          39 bits
00:00:00.214491 Virtual Address Width:           48 bits
00:00:00.214492 Guest Physical Address Width:    0 bits
00:00:00.214492 Physical Core Count:             1
00:00:00.214494 
00:00:00.214495 ******************** End of CPUID dump **********************
00:00:00.214495 *********************** VT-x features ***********************
00:00:00.214497 Nested hardware virtualization - VMX features
00:00:00.214498   Mnemonic - Description                                  = guest (host)
00:00:00.214498   VMX - Virtual-Machine Extensions                        = 0 (1)
00:00:00.214499   InsOutInfo - INS/OUTS instruction info.                 = 0 (1)
00:00:00.214500   ExtIntExit - External interrupt exiting                 = 0 (1)
00:00:00.214501   NmiExit - NMI exiting                                   = 0 (1)
00:00:00.214502   VirtNmi - Virtual NMIs                                  = 0 (1)
00:00:00.214503   PreemptTimer - VMX preemption timer                     = 0 (1)
00:00:00.214503   PostedInt - Posted interrupts                           = 0 (0)
00:00:00.214504   IntWindowExit - Interrupt-window exiting                = 0 (1)
00:00:00.214504   TscOffsetting - TSC offsetting                          = 0 (1)
00:00:00.214505   HltExit - HLT exiting                                   = 0 (1)
00:00:00.214506   InvlpgExit - INVLPG exiting                             = 0 (1)
00:00:00.214506   MwaitExit - MWAIT exiting                               = 0 (1)
00:00:00.214509   RdpmcExit - RDPMC exiting                               = 0 (1)
00:00:00.214510   RdtscExit - RDTSC exiting                               = 0 (1)
00:00:00.214510   Cr3LoadExit - CR3-load exiting                          = 0 (1)
00:00:00.214511   Cr3StoreExit - CR3-store exiting                        = 0 (1)
00:00:00.214512   Cr8LoadExit  - CR8-load exiting                         = 0 (1)
00:00:00.214512   Cr8StoreExit - CR8-store exiting                        = 0 (1)
00:00:00.214513   UseTprShadow - Use TPR shadow                           = 0 (1)
00:00:00.214535   NmiWindowExit - NMI-window exiting                      = 0 (1)
00:00:00.214535   MovDRxExit - Mov-DR exiting                             = 0 (1)
00:00:00.214536   UncondIoExit - Unconditional I/O exiting                = 0 (1)
00:00:00.214537   UseIoBitmaps - Use I/O bitmaps                          = 0 (1)
00:00:00.214540   MonitorTrapFlag - Monitor Trap Flag                     = 0 (1)
00:00:00.214540   UseMsrBitmaps - MSR bitmaps                             = 0 (1)
00:00:00.214541   MonitorExit - MONITOR exiting                           = 0 (1)
00:00:00.214541   PauseExit - PAUSE exiting                               = 0 (1)
00:00:00.214542   SecondaryExecCtl - Activate secondary controls          = 0 (1)
00:00:00.214542   VirtApic - Virtualize-APIC accesses                     = 0 (1)
00:00:00.214543   Ept - Extended Page Tables                              = 0 (1)
00:00:00.214543   DescTableExit - Descriptor-table exiting                = 0 (1)
00:00:00.214544   Rdtscp - Enable RDTSCP                                  = 0 (1)
00:00:00.214545   VirtX2ApicMode - Virtualize-x2APIC mode                 = 0 (1)
00:00:00.214545   Vpid - Enable VPID                                      = 0 (1)
00:00:00.214546   WbinvdExit - WBINVD exiting                             = 0 (1)
00:00:00.214547   UnrestrictedGuest - Unrestricted guest                  = 0 (1)
00:00:00.214548   ApicRegVirt - APIC-register virtualization              = 0 (0)
00:00:00.214548   VirtIntDelivery - Virtual-interrupt delivery            = 0 (0)
00:00:00.214549   PauseLoopExit - PAUSE-loop exiting                      = 0 (1)
00:00:00.214550   RdrandExit - RDRAND exiting                             = 0 (1)
00:00:00.214550   Invpcid - Enable INVPCID                                = 0 (1)
00:00:00.214551   VmFuncs - Enable VM Functions                           = 0 (1)
00:00:00.214551   VmcsShadowing - VMCS shadowing                          = 0 (1)
00:00:00.214552   RdseedExiting - RDSEED exiting                          = 0 (1)
00:00:00.214553   PML - Page-Modification Log (PML)                       = 0 (1)
00:00:00.214553   EptVe - EPT violations can cause #VE                    = 0 (1)
00:00:00.214554   XsavesXRstors - Enable XSAVES/XRSTORS                   = 0 (1)
00:00:00.214555   EntryLoadDebugCtls - Load debug controls on VM-entry    = 0 (1)
00:00:00.214555   Ia32eModeGuest - IA-32e mode guest                      = 0 (1)
00:00:00.214556   EntryLoadEferMsr - Load IA32_EFER MSR on VM-entry       = 0 (1)
00:00:00.214557   EntryLoadPatMsr - Load IA32_PAT MSR on VM-entry         = 0 (1)
00:00:00.214558   ExitSaveDebugCtls - Save debug controls on VM-exit      = 0 (1)
00:00:00.214558   HostAddrSpaceSize - Host address-space size             = 0 (1)
00:00:00.214559   ExitAckExtInt - Acknowledge interrupt on VM-exit        = 0 (1)
00:00:00.214560   ExitSavePatMsr - Save IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.214561   ExitLoadPatMsr - Load IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.214561   ExitSaveEferMsr - Save IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.214562   ExitLoadEferMsr - Load IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.214563   SavePreemptTimer - Save VMX-preemption timer            = 0 (1)
00:00:00.214564   ExitSaveEferLma - Save IA32_EFER.LMA on VM-exit         = 0 (1)
00:00:00.214564   IntelPt - Intel PT (Processor Trace) in VMX operation   = 0 (1)
00:00:00.214565   VmwriteAll - VMWRITE to any supported VMCS field        = 0 (1)
00:00:00.214566   EntryInjectSoftInt - Inject softint. with 0-len instr.  = 0 (1)
00:00:00.214566 
00:00:00.214567 ******************* End of VT-x features ********************
00:00:00.214617 VMEmt: Halt method global1 (5)
00:00:00.214751 VMEmt: HaltedGlobal1 config: cNsSpinBlockThresholdCfg=50000
00:00:00.214800 Changing the VM state from 'CREATING' to 'CREATED'
00:00:00.216531 SharedFolders host service: Adding host mapping
00:00:00.216550     Host path 'C:/Users/<USER>/Documents/leidian9/Applications', map name 'Applications', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.216836 SharedFolders host service: Adding host mapping
00:00:00.216852     Host path 'C:\Users\<USER>\AppData\Roaming\leidian9\android_bug', map name 'Bug', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.217071 SharedFolders host service: Adding host mapping
00:00:00.217083     Host path 'C:/Users/<USER>/Documents/leidian9/Misc', map name 'Misc', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.217261 SharedFolders host service: Adding host mapping
00:00:00.217271     Host path 'C:/Users/<USER>/Documents/leidian9/Pictures', map name 'Pictures', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.217443 Changing the VM state from 'CREATED' to 'POWERING_ON'
00:00:00.217620 virtioCoreVirtqAvailBufCount: Driver not ready or queue controlq not enabled
00:00:00.217768 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<0> not enabled
00:00:00.218351 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<1> not enabled
00:00:00.218985 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<2> not enabled
00:00:00.219022 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<3> not enabled
00:00:00.219076 Changing the VM state from 'POWERING_ON' to 'RUNNING'
00:00:00.219164 Console: Machine state changed to 'Running'
00:00:00.222891 VMMDev: Guest Log: BIOS: VirtualBox 6.1.34
00:00:00.222988 PCI: Setting up resources and interrupts
00:00:00.223083 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.247569 VMMDev: Guest Log: CPUID EDX: 0x178bfbff
00:00:00.248127 VMMDev: Guest Log: BIOS: No PCI IDE controller, not probing IDE
00:00:00.250955 VMMDev: Guest Log: BIOS: SCSI 0-ID#0: LCHS=326/255/63 0x0000000000503f2a sectors
00:00:00.251562 VMMDev: Guest Log: BIOS: SCSI 1-ID#1: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.252227 VMMDev: Guest Log: BIOS: SCSI 2-ID#2: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.254041 PIT: mode=2 count=0x48d3 (18643) - 64.00 Hz (ch=0)
00:00:00.254080 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.254308 VMMDev: Guest Log: BIOS: Boot : bseqnr=1, bootseq=0002
00:00:00.254613 VMMDev: Guest Log: BIOS: Booting from Hard Disk...
00:00:00.256831 VMMDev: Guest Log: int13_harddisk_ext: function 41, unmapped device for ELDL=83
00:00:00.257207 VMMDev: Guest Log: int13_harddisk: function 08, unmapped device for ELDL=83
00:00:00.466116 VMMDev: Guest Log: BIOS: KBD: unsupported int 16h function 03
00:00:00.466365 VMMDev: Guest Log: BIOS: AX=0305 BX=0000 CX=0000 DX=0000 
00:00:00.543031 VBoxHeadless: starting event loop
00:00:00.673552 GIM: KVM: VCPU  0: Enabled system-time struct. at 0x000000019ff7c000 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x616a7761 uVirtNanoTS=0x1b0f5965 TscKHz=3600000
00:00:00.673666 TM: Switching TSC mode from 'VirtTscEmulated' to 'RealTscOffset'
00:00:00.731398 GIM: KVM: Enabled wall-clock struct. at 0x00000000010c32a8 - u32Sec=1754320042 u32Nano=127332092 uVersion=2
00:00:00.757977 PIT: mode=2 count=0xf89 (3977) - 300.02 Hz (ch=0)
00:00:00.767514 APIC0: Switched mode to x2APIC
00:00:00.874360 PIT: mode=0 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.905051 APIC1: Switched mode to x2APIC
00:00:00.905064 GIM: KVM: VCPU  1: Enabled system-time struct. at 0x000000019ff7c040 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x931781fc uVirtNanoTS=0x28dbdc02 TscKHz=3600000
00:00:00.920561 APIC2: Switched mode to x2APIC
00:00:00.920572 GIM: KVM: VCPU  2: Enabled system-time struct. at 0x000000019ff7c080 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x966bbd6e uVirtNanoTS=0x29c8972a TscKHz=3600000
00:00:00.934134 APIC3: Switched mode to x2APIC
00:00:00.934154 GIM: KVM: VCPU  3: Enabled system-time struct. at 0x000000019ff7c0c0 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x995533c4 uVirtNanoTS=0x2a97a9cc TscKHz=3600000
00:00:02.232347 VMMDev: Guest Additions information report: Version 6.1.36 r152435 '6.1.36'
00:00:02.232404 VMMDev: Guest Additions information report: Interface = 0x00010004 osType = 0x00053100 (Linux >= 2.6, 64-bit)
00:00:02.232524 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.232634 VMMDev: vmmDevReqHandler_HeartbeatConfigure: No change (fHeartbeatActive=false)
00:00:02.232654 VMMDev: Heartbeat flatline timer set to trigger after 4 000 000 000 ns
00:00:02.232689 VMMDev: Guest Log: vgdrvHeartbeatInit: Setting up heartbeat to trigger every 2000 milliseconds
00:00:02.233463 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.233573 VMMDev: Guest Log: vboxguest: Successfully loaded version 6.1.36 r152435
00:00:02.233806 VMMDev: Guest Log: vboxguest: misc device minor 53, IRQ 20, I/O port d020, MMIO at 00000000f0000000 (size 0x400000)
00:00:02.235452 VMMDev: Guest Log: vboxsf: g_fHostFeatures=0x8000000f g_fSfFeatures=0x1 g_uSfLastFunction=29
00:00:02.235992 VMMDev: Guest Log: vboxsf: Successfully loaded version 6.1.36 r152435 on 4.4.146 SMP preempt mod_unload modversions  (LINUX_VERSION_CODE=0x40492)
00:04:29.246931 Console: Machine state changed to 'Stopping'
00:04:29.247774 Console::powerDown(): A request to power off the VM has been issued (mMachineState=Stopping, InUninit=0)
00:04:29.248189 Changing the VM state from 'RUNNING' to 'POWERING_OFF'
00:04:29.248204 ****************** Guest state at power off for VCpu 3 ******************
00:04:29.248211 Guest CPUM (VCPU 3) state: 
00:04:29.248216 rax=0000000000000003 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:04:29.248218 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:04:29.248219 r10=00000000ffffdada r11=0000000000000202 r12=0000000000000000 r13=0000000000000000
00:04:29.248220 r14=ffff880198970000 r15=ffff880198974000
00:04:29.248221 rip=ffffffff80239162 rsp=ffff880198973ef8 rbp=ffff880198974000 iopl=0         nv up ei pl zr na pe nc
00:04:29.248223 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:04:29.248224 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248225 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248225 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248226 gs={0000 base=ffff88019fd80000 limit=ffffffff flags=0001c000}
00:04:29.248227 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:04:29.248227 cr0=0000000080050033 cr2=00007ffff7254000 cr3=00000000dae40000 cr4=00000000000606b0
00:04:29.248229 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:04:29.248229 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:04:29.248230 gdtr=ffff88019fd8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:04:29.248231 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:04:29.248232 tr  ={0040 base=ffff88019fd84840 limit=00002087 flags=0000008b}
00:04:29.248233 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:04:29.248234 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:04:29.248253 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:04:29.248254 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:04:29.248255 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248257 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248258 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248259 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248260 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248261 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248261 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248262 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248263 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248264 YMM1 =00000000'00000000'00000000'00000000'00000000'00000000'00000011'00000003
00:04:29.248265 YMM2 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'0000004c
00:04:29.248266 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00007fff'f5221000
00:04:29.248267 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248268 YMM5 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248269 YMM6 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248269 YMM7 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248270 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:04:29.248271 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:04:29.248272 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248273 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248274 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248274 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248275 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248276 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248277 EFER         =0000000000000d01
00:04:29.248277 PAT          =0007040600070406
00:04:29.248278 STAR         =0023001000000000
00:04:29.248279 CSTAR        =ffffffff809e4e70
00:04:29.248279 LSTAR        =ffffffff809e3690
00:04:29.248279 SFMASK       =0000000000047700
00:04:29.248280 KERNELGSBASE =0000000000000000
00:04:29.248281 ***
00:04:29.248286 VCPU[3] hardware virtualization state:
00:04:29.248287 fLocalForcedActions          = 0x0
00:04:29.248287 No/inactive hwvirt state
00:04:29.248288 ***
00:04:29.248307 Guest paging mode (VCPU #3):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:04:29.248309 Shadow paging mode (VCPU #3): EPT
00:04:29.248311 Host paging mode:             AMD64+NX
00:04:29.248312 ***
00:04:29.248312 ************** End of Guest state at power off for VCpu 3 ***************
00:04:29.248404 ****************** Guest state at power off for VCpu 2 ******************
00:04:29.248409 Guest CPUM (VCPU 2) state: 
00:04:29.248411 rax=0000000000000002 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:04:29.248413 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:04:29.248416 r10=00000000ffffd9c7 r11=0000000000000246 r12=0000000000000000 r13=0000000000000000
00:04:29.248417 r14=ffff880198964000 r15=ffff880198968000
00:04:29.248419 rip=ffffffff80239162 rsp=ffff880198967ef8 rbp=ffff880198968000 iopl=0         nv up ei pl zr na pe nc
00:04:29.248422 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:04:29.248423 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248425 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248426 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248427 gs={0000 base=ffff88019fd00000 limit=ffffffff flags=0001c000}
00:04:29.248428 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:04:29.248429 cr0=0000000080050033 cr2=00000000d5d37000 cr3=0000000198261000 cr4=00000000000606b0
00:04:29.248431 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:04:29.248433 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:04:29.248434 gdtr=ffff88019fd0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:04:29.248437 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:04:29.248438 tr  ={0040 base=ffff88019fd04840 limit=00002087 flags=0000008b}
00:04:29.248439 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:04:29.248440 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:04:29.248442 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:04:29.248444 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:04:29.248446 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248464 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248467 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248469 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248472 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248474 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248477 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248497 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248499 YMM0 =00000000'00000000'00000000'00000000'00000000'022f13fd'00000000'6890cdb7
00:04:29.248501 YMM1 =00000000'00000000'00000000'00000000'403dffff'ffffe84e'19080000'00000000
00:04:29.248503 YMM2 =00000000'00000000'00000000'00000000'00000000'00000001'00000000'00000001
00:04:29.248504 YMM3 =00000000'00000000'00000000'00000000'00007fff'f70315c0'00007fff'f7031480
00:04:29.248505 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248507 YMM5 =00000000'00000000'00000000'00000000'ffffffff'ffffffff'ffffffff'ffffffff
00:04:29.248508 YMM6 =00000000'00000000'00000000'00000000'ffffffff'ffffffff'ffffffff'ffffffff
00:04:29.248510 YMM7 =00000000'00000000'00000000'00000000'ffffffff'ffffffff'ffffffff'ffffffff
00:04:29.248511 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:04:29.248513 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:04:29.248514 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248516 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248517 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248518 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248520 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248521 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248522 EFER         =0000000000000d01
00:04:29.248523 PAT          =0007040600070406
00:04:29.248523 STAR         =0023001000000000
00:04:29.248524 CSTAR        =ffffffff809e4e70
00:04:29.248525 LSTAR        =ffffffff809e3690
00:04:29.248525 SFMASK       =0000000000047700
00:04:29.248526 KERNELGSBASE =0000000000000000
00:04:29.248528 ***
00:04:29.248530 VCPU[2] hardware virtualization state:
00:04:29.248530 fLocalForcedActions          = 0x0
00:04:29.248531 No/inactive hwvirt state
00:04:29.248532 ***
00:04:29.248534 Guest paging mode (VCPU #2):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:04:29.248535 Shadow paging mode (VCPU #2): EPT
00:04:29.248535 Host paging mode:             AMD64+NX
00:04:29.248536 ***
00:04:29.248536 ************** End of Guest state at power off for VCpu 2 ***************
00:04:29.248615 ****************** Guest state at power off for VCpu 1 ******************
00:04:29.248635 Guest CPUM (VCPU 1) state: 
00:04:29.248637 rax=0000000000000001 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:04:29.248639 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:04:29.248641 r10=00000000ffffdabb r11=0000000000000000 r12=0000000000000000 r13=0000000000000000
00:04:29.248642 r14=ffff880198960000 r15=ffff880198964000
00:04:29.248643 rip=ffffffff80239162 rsp=ffff880198963ef8 rbp=ffff880198964000 iopl=0         nv up ei pl zr na pe nc
00:04:29.248646 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:04:29.248647 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248648 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248649 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248650 gs={0000 base=ffff88019fc80000 limit=ffffffff flags=0001c000}
00:04:29.248651 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:04:29.248652 cr0=0000000080050033 cr2=0000000000607530 cr3=00000001942aa000 cr4=00000000000606b0
00:04:29.248654 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:04:29.248655 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:04:29.248657 gdtr=ffff88019fc8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:04:29.248659 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:04:29.248659 tr  ={0040 base=ffff88019fc84840 limit=00002087 flags=0000008b}
00:04:29.248660 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:04:29.248662 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:04:29.248663 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa1 MXCSR_MASK=0000ffff
00:04:29.248680 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:04:29.248681 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248682 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248683 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248685 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248686 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248687 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248689 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248690 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248691 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248692 YMM1 =00000000'00000000'00000000'00000000'bf900000'00000000'3fed6e59'd7f7eb6a
00:04:29.248694 YMM2 =00000000'00000000'00000000'00000000'80000000'00000000'3f30e3c1'2b23fc1d
00:04:29.248695 YMM3 =00000000'00000000'00000000'00000000'bf900000'00000000'00000000'00000000
00:04:29.248697 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'3fbab779'21bcaf9e
00:04:29.248698 YMM5 =00000000'00000000'00000000'00000000'00000000'00000000'bf3bfe6c'12f4dbb1
00:04:29.248699 YMM6 =00000000'00000000'00000000'00000000'00000000'00000000'bed15a56'77c70695
00:04:29.248701 YMM7 =00000000'00000000'00000000'00000000'bf900000'00000000'bf3c158e'467d4291
00:04:29.248702 YMM8 =00000000'00000000'00000000'00000000'00000000'00000000'ba1d20d4'ba1d20d4
00:04:29.248704 YMM9 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'41a00000
00:04:29.248705 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'3bf152b8
00:04:29.248706 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'ba1d20d4'c2c1e89d
00:04:29.248708 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248709 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248710 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248711 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248713 EFER         =0000000000000d01
00:04:29.248713 PAT          =0007040600070406
00:04:29.248714 STAR         =0023001000000000
00:04:29.248714 CSTAR        =ffffffff809e4e70
00:04:29.248715 LSTAR        =ffffffff809e3690
00:04:29.248715 SFMASK       =0000000000047700
00:04:29.248716 KERNELGSBASE =0000000000000000
00:04:29.248717 ***
00:04:29.248719 VCPU[1] hardware virtualization state:
00:04:29.248719 fLocalForcedActions          = 0x0
00:04:29.248720 No/inactive hwvirt state
00:04:29.248721 ***
00:04:29.248722 Guest paging mode (VCPU #1):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
00:04:29.248723 Shadow paging mode (VCPU #1): EPT
00:04:29.248724 Host paging mode:             AMD64+NX
00:04:29.248725 ***
00:04:29.248725 ************** End of Guest state at power off for VCpu 1 ***************
00:04:29.248765 ****************** Guest state at power off for VCpu 0 ******************
00:04:29.248769 Guest CPUM (VCPU 0) state: 
00:04:29.248771 rax=0000000000000000 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:04:29.248773 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =ffff88019fc0f878
00:04:29.248774 r10=0000000000ffffdb r11=ffffffff80e03e88 r12=0000000000000000 r13=0000000000000000
00:04:29.248775 r14=ffffffff80e00000 r15=ffffffff80e04000
00:04:29.248776 rip=ffffffff80239162 rsp=ffffffff80e03f08 rbp=ffffffff80e04000 iopl=0         nv up ei pl zr na pe nc
00:04:29.248778 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:04:29.248795 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248796 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248797 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
00:04:29.248797 gs={0000 base=ffff88019fc00000 limit=ffffffff flags=0001c000}
00:04:29.248798 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:04:29.248799 cr0=0000000080050033 cr2=000000004723c000 cr3=00000001942aa000 cr4=00000000000606b0
00:04:29.248800 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:04:29.248801 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
00:04:29.248802 gdtr=ffff88019fc0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:04:29.248803 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
00:04:29.248803 tr  ={0040 base=ffff88019fc04840 limit=00002087 flags=0000008b}
00:04:29.248804 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:04:29.248805 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
00:04:29.248806 FCW=037f FSW=0220 FTW=0000 FOP=0000 MXCSR=00001fa1 MXCSR_MASK=0000ffff
00:04:29.248807 FPUIP=f6adcd15 CS=7fff Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:04:29.248808 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248810 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248811 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248812 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248812 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248813 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:04:29.248814 ST(6)=FPR6={3fff'80000000'00000000} t0 +1.0000000000000000000000 * 2 ^ 0 (*)
00:04:29.248815 ST(7)=FPR7={3fff'c0aa5fd3'0fe10e33} t0 +1.0004659642124577345075 * 2 ^ 0 (*)
00:04:29.248817 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000003
00:04:29.248817 YMM1 =00000000'00000000'00000000'00000000'00000000'00000000'00000010'00000004
00:04:29.248818 YMM2 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'0000002c
00:04:29.248819 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00007fff'563aa140
00:04:29.248820 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248821 YMM5 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248822 YMM6 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248822 YMM7 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248823 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
00:04:29.248824 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
00:04:29.248825 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'ebad807a
00:04:29.248826 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'ebad807b
00:04:29.248827 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248828 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248828 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248829 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
00:04:29.248830 EFER         =0000000000000d01
00:04:29.248830 PAT          =0007040600070406
00:04:29.248831 STAR         =0023001000000000
00:04:29.248831 CSTAR        =ffffffff809e4e70
00:04:29.248832 LSTAR        =ffffffff809e3690
00:04:29.248832 SFMASK       =0000000000047700
00:04:29.248833 KERNELGSBASE =0000000000000000
00:04:29.248834 ***
00:04:29.248835 VCPU[0] hardware virtualization state:
00:04:29.248836 fLocalForcedActions          = 0x0
00:04:29.248836 No/inactive hwvirt state
00:04:29.248837 ***
00:04:29.248839 Guest paging mode (VCPU #0):  AMD64+NX (changed 3700 times), A20 enabled (changed 2 times)
00:04:29.248840 Shadow paging mode (VCPU #0): EPT
00:04:29.248841 Host paging mode:             AMD64+NX
00:04:29.248842 ***
00:04:29.248843 Active Timers (pVM=0000000000d50000)
00:04:29.248844 pTimerR3         offNext  offPrev  offSched Clock               Time             Expire HzHint State                     Description
00:04:29.248845 0000000003c125d0 00000000 00000000 00000000 Real           110193370          110193836      0 2-ACTIVE                  CPU Load Timer
00:04:29.248848 0000000003c11d10 00000000 00000000 00000000 Virt        269029504989       272026373019      0 2-ACTIVE                  Heartbeat flatlined
00:04:29.248850 0000000003c0d760 00000080 00000000 00000000 VrSy        269028805141       269041475597     10 2-ACTIVE                  APIC Timer 2
00:04:29.248852 0000000003c0d7e0 ffffff00 ffffff80 00000000 VrSy        269028806968       269044195562     61 2-ACTIVE                  APIC Timer 3
00:04:29.248854 0000000003c0d6e0 00000370 00000100 00000000 VrSy        269028808621       269078188764      5 2-ACTIVE                  APIC Timer 1
00:04:29.248855 0000000003c0da50 fffffc10 fffffc90 00000000 VrSy        269028810272       269990000000      0 2-ACTIVE                  MC146818 RTC (CMOS) - Second
00:04:29.248857 0000000003c0d660 00004cf0 000003f0 00000000 VrSy        269028812013       270322188298      0 2-ACTIVE                  APIC Timer 0
00:04:29.248859 0000000003c12350 00000000 ffffb310 00000000 VrSy        269028813687       599932015941      0 2-ACTIVE                  ACPI PM Timer
00:04:29.248861 ***
00:04:29.248863 Guest GDT (GCAddr=ffff88019fc0c000 limit=7f):
00:04:29.248869 0008 - 0000ffff 00cf9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 32-bit 
00:04:29.248870 0010 - 0000ffff 00af9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 16-bit 
00:04:29.248871 0018 - 0000ffff 00cf9300 - base=00000000 limit=ffffffff dpl=0 DataRW Accessed Present Page 32-bit 
00:04:29.248872 0020 - 0000ffff 00cffb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 32-bit 
00:04:29.248873 0028 - 0000ffff 00cff300 - base=00000000 limit=ffffffff dpl=3 DataRW Accessed Present Page 32-bit 
00:04:29.248874 0030 - 0000ffff 00affb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 16-bit 
00:04:29.248875 0040 - 48402087 9f008bc0 - base=9fc04840 limit=00002087 dpl=0 TSS32Busy Present 16-bit 
00:04:29.248877 0078 - 00000000 0040f500 - base=00000000 limit=00000000 dpl=3 DataDownRO Accessed Present 32-bit 
00:04:29.248878 ************** End of Guest state at power off ***************
00:04:29.338760 PDMR3PowerOff: after    89 ms, 1 loops: 1 async tasks - virtio-scsi/0
00:04:29.431859 PDMR3PowerOff: 182 962 871 ns run time
00:04:29.431891 Changing the VM state from 'POWERING_OFF' to 'OFF'
00:04:29.433557 Changing the VM state from 'OFF' to 'DESTROYING'
00:04:29.433726 ************************* Statistics *************************
00:04:29.433745 /CPUM/MSR-Totals/Reads              28250 times
00:04:29.433755 /CPUM/MSR-Totals/ReadsRaisingGP         0 times
00:04:29.433763 /CPUM/MSR-Totals/ReadsUnknown           0 times
00:04:29.433774 /CPUM/MSR-Totals/Writes            728542 times
00:04:29.433783 /CPUM/MSR-Totals/WritesRaisingGP        0 times
00:04:29.433791 /CPUM/MSR-Totals/WritesToIgnoredBits        4 times
00:04:29.433801 /CPUM/MSR-Totals/WritesUnknown          0 times
00:04:29.433809 /Devices/8237A/DmaRun                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.433818 /Devices/VMMDev/BalloonChunks           0 count
00:04:29.433838 /Devices/VMMDev/FastIrqAckR3            0 count
00:04:29.433846 /Devices/VMMDev/FastIrqAckRZ           67 count
00:04:29.433853 /Devices/VMMDev/HGCM-Guest/BudgetAvailable 535351424 bytes
00:04:29.433861 /Devices/VMMDev/HGCM-Guest/BudgetConfig 535351424 bytes
00:04:29.433869 /Devices/VMMDev/HGCM-Guest/cTotalMessages        0 count
00:04:29.433876 /Devices/VMMDev/HGCM-Guest/cbHeapTotal        0 bytes
00:04:29.433899 /Devices/VMMDev/HGCM-Legacy/BudgetAvailable 535351424 bytes
00:04:29.433906 /Devices/VMMDev/HGCM-Legacy/BudgetConfig 535351424 bytes
00:04:29.433914 /Devices/VMMDev/HGCM-Legacy/cTotalMessages        0 count
00:04:29.433922 /Devices/VMMDev/HGCM-Legacy/cbHeapTotal        0 bytes
00:04:29.433929 /Devices/VMMDev/HGCM-OtherDrv/BudgetAvailable 535351424 bytes
00:04:29.433937 /Devices/VMMDev/HGCM-OtherDrv/BudgetConfig 535351424 bytes
00:04:29.433944 /Devices/VMMDev/HGCM-OtherDrv/cTotalMessages       67 count
00:04:29.433952 /Devices/VMMDev/HGCM-OtherDrv/cbHeapTotal   302829 bytes
00:04:29.433960 /Devices/VMMDev/HGCM-Reserved1/BudgetAvailable 535351424 bytes
00:04:29.433980 /Devices/VMMDev/HGCM-Reserved1/BudgetConfig 535351424 bytes
00:04:29.434001 /Devices/VMMDev/HGCM-Reserved1/cTotalMessages        0 count
00:04:29.434008 /Devices/VMMDev/HGCM-Reserved1/cbHeapTotal        0 bytes
00:04:29.434015 /Devices/VMMDev/HGCM-Root/BudgetAvailable 535351424 bytes
00:04:29.434023 /Devices/VMMDev/HGCM-Root/BudgetConfig 535351424 bytes
00:04:29.434044 /Devices/VMMDev/HGCM-Root/cTotalMessages        0 count
00:04:29.434051 /Devices/VMMDev/HGCM-Root/cbHeapTotal        0 bytes
00:04:29.434058 /Devices/VMMDev/HGCM-System/BudgetAvailable 535351424 bytes
00:04:29.434065 /Devices/VMMDev/HGCM-System/BudgetConfig 535351424 bytes
00:04:29.434073 /Devices/VMMDev/HGCM-System/cTotalMessages        0 count
00:04:29.434080 /Devices/VMMDev/HGCM-System/cbHeapTotal        0 bytes
00:04:29.434087 /Devices/VMMDev/HGCM-User/BudgetAvailable 535351424 bytes
00:04:29.434094 /Devices/VMMDev/HGCM-User/BudgetConfig 535351424 bytes
00:04:29.434102 /Devices/VMMDev/HGCM-User/cTotalMessages        0 count
00:04:29.434109 /Devices/VMMDev/HGCM-User/cbHeapTotal        0 bytes
00:04:29.434120 /Devices/VMMDev/HGCM-VBoxGuest/BudgetAvailable 535351424 bytes
00:04:29.434131 /Devices/VMMDev/HGCM-VBoxGuest/BudgetConfig 535351424 bytes
00:04:29.434138 /Devices/VMMDev/HGCM-VBoxGuest/cTotalMessages        0 count
00:04:29.434145 /Devices/VMMDev/HGCM-VBoxGuest/cbHeapTotal        0 bytes
00:04:29.434153 /Devices/VMMDev/LargeReqBufAllocs        0 count
00:04:29.434160 /Devices/VMMDev/SlowIrqAck              0 count
00:04:29.434167 /Devices/mc146818/Irq                   0 times
00:04:29.434175 /Devices/mc146818/TimerCB               0 times
00:04:29.434182 /Devices/virtio-net#0/Interrupts/Raised      269 times
00:04:29.434190 /Devices/virtio-net#0/Interrupts/Skipped      210 times
00:04:29.434197 /Devices/virtio-net#0/Packets/ReceiveGSO        0 count
00:04:29.434205 /Devices/virtio-net#0/Packets/Transmit       97 count
00:04:29.434212 /Devices/virtio-net#0/Packets/Transmit-Csum        0 count
00:04:29.434220 /Devices/virtio-net#0/Packets/Transmit-Gso        0 count
00:04:29.434228 /Devices/virtio-net#0/ReceiveBytes    58425 bytes
00:04:29.434235 /Devices/virtio-net#0/TransmitBytes    15738 bytes
00:04:29.434243 /Devices/virtio-scsi#0/DescChainsAllocated    17428 count
00:04:29.434251 /Devices/virtio-scsi#0/DescChainsFreed    17428 count
00:04:29.434259 /Devices/virtio-scsi#0/DescChainsSegsIn   130000 count
00:04:29.434267 /Devices/virtio-scsi#0/DescChainsSegsOut    23392 count
00:04:29.434274 /Drivers/IntNet-0/BadFrames             0 count
00:04:29.434282 /Drivers/IntNet-0/Bytes/Received   108833 bytes
00:04:29.434289 /Drivers/IntNet-0/Bytes/Sent        15738 bytes
00:04:29.434297 /Drivers/IntNet-0/Overflows/Recv        0 count
00:04:29.434304 /Drivers/IntNet-0/Overflows/Sent        0 count
00:04:29.434311 /Drivers/IntNet-0/Packets/Lost          0 count
00:04:29.434318 /Drivers/IntNet-0/Packets/Received      573 count
00:04:29.434326 /Drivers/IntNet-0/Packets/Received-Gso        0 count
00:04:29.434333 /Drivers/IntNet-0/Packets/Sent         97 count
00:04:29.434340 /Drivers/IntNet-0/Packets/Sent-Gso        0 count
00:04:29.434348 /Drivers/IntNet-0/Packets/Sent-R0        0 count
00:04:29.434357 /Drivers/IntNet-0/Recv1                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434366 /Drivers/IntNet-0/Recv2                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434373 /Drivers/IntNet-0/Reserved              0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434381 /Drivers/IntNet-0/Send1            112947 ticks/call (    10955950 ticks,      97 times, max    450316, min   18824)
00:04:29.434390 /Drivers/IntNet-0/Send2            108897 ticks/call (    10563098 ticks,      97 times, max    440852, min   18118)
00:04:29.434413 /Drivers/IntNet-0/XmitProcessRing        0 count
00:04:29.434421 /Drivers/IntNet-0/XmitWakeup-R0         0 count
00:04:29.434428 /Drivers/IntNet-0/XmitWakeup-R3         0 count
00:04:29.434449 /Drivers/IntNet-0/YieldNok              0 count
00:04:29.434456 /Drivers/IntNet-0/YieldOk               0 count
00:04:29.434468 /EM/CPU0/ExitHashing/Used               0 times
00:04:29.434476 /EM/CPU0/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434488 /EM/CPU0/ExitOpt/ExecInstructions        0 times
00:04:29.434498 /EM/CPU0/ExitOpt/ExecSavedExit          0 times
00:04:29.434505 /EM/CPU0/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434514 /EM/CPU0/ExitOpt/ProbeInstructions        0 times
00:04:29.434521 /EM/CPU0/ExitOpt/ProbedExecWithMax        0 times
00:04:29.434528 /EM/CPU0/ExitOpt/ProbedNormal           0 times
00:04:29.434535 /EM/CPU0/ExitOpt/ProbedToRing3          0 times
00:04:29.434546 /EM/CPU1/ExitHashing/Used               0 times
00:04:29.434553 /EM/CPU1/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434563 /EM/CPU1/ExitOpt/ExecInstructions        0 times
00:04:29.434571 /EM/CPU1/ExitOpt/ExecSavedExit          0 times
00:04:29.434579 /EM/CPU1/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434587 /EM/CPU1/ExitOpt/ProbeInstructions        0 times
00:04:29.434595 /EM/CPU1/ExitOpt/ProbedExecWithMax        0 times
00:04:29.434602 /EM/CPU1/ExitOpt/ProbedNormal           0 times
00:04:29.434610 /EM/CPU1/ExitOpt/ProbedToRing3          0 times
00:04:29.434621 /EM/CPU2/ExitHashing/Used               0 times
00:04:29.434629 /EM/CPU2/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434637 /EM/CPU2/ExitOpt/ExecInstructions        0 times
00:04:29.434644 /EM/CPU2/ExitOpt/ExecSavedExit          0 times
00:04:29.434652 /EM/CPU2/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434660 /EM/CPU2/ExitOpt/ProbeInstructions        0 times
00:04:29.434667 /EM/CPU2/ExitOpt/ProbedExecWithMax        0 times
00:04:29.434674 /EM/CPU2/ExitOpt/ProbedNormal           0 times
00:04:29.434681 /EM/CPU2/ExitOpt/ProbedToRing3          0 times
00:04:29.434690 /EM/CPU3/ExitHashing/Used               0 times
00:04:29.434698 /EM/CPU3/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434706 /EM/CPU3/ExitOpt/ExecInstructions        0 times
00:04:29.434713 /EM/CPU3/ExitOpt/ExecSavedExit          0 times
00:04:29.434721 /EM/CPU3/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.434729 /EM/CPU3/ExitOpt/ProbeInstructions        0 times
00:04:29.434736 /EM/CPU3/ExitOpt/ProbedExecWithMax        0 times
00:04:29.434743 /EM/CPU3/ExitOpt/ProbedNormal           0 times
00:04:29.434750 /EM/CPU3/ExitOpt/ProbedToRing3          0 times
00:04:29.434758 /GMM/ChunkTlbHits                    5114 times
00:04:29.434765 /GMM/ChunkTlbMisses                   372 times
00:04:29.434777 /GMM/VM/Allocated/cBasePages       264798 pages
00:04:29.434785 /GMM/VM/Allocated/cFixedPages           0 pages
00:04:29.434792 /GMM/VM/Allocated/cShadowPages          0 pages
00:04:29.434800 /GMM/VM/Reserved/cBasePages       1572958 pages
00:04:29.434807 /GMM/VM/Reserved/cFixedPages         5140 pages
00:04:29.434814 /GMM/VM/Reserved/cShadowPages           1 pages
00:04:29.434822 /GMM/VM/cBalloonedPages                 0 pages
00:04:29.434829 /GMM/VM/cMaxBalloonedPages              0 pages
00:04:29.434836 /GMM/VM/cPrivatePages              264798 pages
00:04:29.434844 /GMM/VM/cReqActuallyBalloonedPages        0 pages
00:04:29.434851 /GMM/VM/cReqBalloonedPages              0 pages
00:04:29.434858 /GMM/VM/cReqDeflatePages                0 pages
00:04:29.434865 /GMM/VM/cShareableModules               0 count
00:04:29.434873 /GMM/VM/cSharedPages                    0 pages
00:04:29.434880 /GMM/VM/enmPolicy                       1 
00:04:29.434888 /GMM/VM/enmPriority                     2 
00:04:29.434896 /GMM/VM/fBallooningEnabled       false    
00:04:29.434903 /GMM/VM/fMayAllocate             true     
00:04:29.434911 /GMM/VM/fSharedPagingEnabled     false    
00:04:29.434918 /GMM/cAllocatedPages               264798 pages
00:04:29.434925 /GMM/cBalloonedPages                    0 pages
00:04:29.434933 /GMM/cChunks                          518 count
00:04:29.434940 /GMM/cDuplicatePages                    0 pages
00:04:29.434948 /GMM/cFreedChunks                       0 count
00:04:29.434956 /GMM/cLeftBehindSharedPages             0 pages
00:04:29.434963 /GMM/cMaxPages                   4294967295 pages
00:04:29.434971 /GMM/cOverCommittedPages                0 pages
00:04:29.434978 /GMM/cReservedPages               1578099 pages
00:04:29.434986 /GMM/cShareableModules                  0 count
00:04:29.434993 /GMM/cSharedPages                       0 pages
00:04:29.435001 /GMM/idFreeGeneration            4611686018427387775 
00:04:29.435190 /GVMM/EMTs                              4 calls
00:04:29.435201 /GVMM/HostCPUs                          8 calls
00:04:29.435209 /GVMM/HostCpus/0                        0 
00:04:29.435217 /GVMM/HostCpus/0/CurTimerHz             0 Hz
00:04:29.435224 /GVMM/HostCpus/0/DesiredHz              0 Hz
00:04:29.435231 /GVMM/HostCpus/0/PPTChanges             0 times
00:04:29.435239 /GVMM/HostCpus/0/PPTStarts              0 times
00:04:29.435246 /GVMM/HostCpus/0/idxCpuSet              0 
00:04:29.435253 /GVMM/HostCpus/1                        1 
00:04:29.435260 /GVMM/HostCpus/1/CurTimerHz             0 Hz
00:04:29.435267 /GVMM/HostCpus/1/DesiredHz              0 Hz
00:04:29.435275 /GVMM/HostCpus/1/PPTChanges             0 times
00:04:29.435282 /GVMM/HostCpus/1/PPTStarts              0 times
00:04:29.435289 /GVMM/HostCpus/1/idxCpuSet              1 
00:04:29.435296 /GVMM/HostCpus/2                        2 
00:04:29.435304 /GVMM/HostCpus/2/CurTimerHz             0 Hz
00:04:29.435311 /GVMM/HostCpus/2/DesiredHz              0 Hz
00:04:29.435319 /GVMM/HostCpus/2/PPTChanges             0 times
00:04:29.435328 /GVMM/HostCpus/2/PPTStarts              0 times
00:04:29.435353 /GVMM/HostCpus/2/idxCpuSet              2 
00:04:29.435360 /GVMM/HostCpus/3                        3 
00:04:29.435367 /GVMM/HostCpus/3/CurTimerHz             0 Hz
00:04:29.435396 /GVMM/HostCpus/3/DesiredHz              0 Hz
00:04:29.435403 /GVMM/HostCpus/3/PPTChanges             0 times
00:04:29.435411 /GVMM/HostCpus/3/PPTStarts              0 times
00:04:29.435418 /GVMM/HostCpus/3/idxCpuSet              3 
00:04:29.435439 /GVMM/HostCpus/4                        4 
00:04:29.435446 /GVMM/HostCpus/4/CurTimerHz             0 Hz
00:04:29.435453 /GVMM/HostCpus/4/DesiredHz              0 Hz
00:04:29.435460 /GVMM/HostCpus/4/PPTChanges             0 times
00:04:29.435467 /GVMM/HostCpus/4/PPTStarts              0 times
00:04:29.435475 /GVMM/HostCpus/4/idxCpuSet              4 
00:04:29.435497 /GVMM/HostCpus/5                        5 
00:04:29.435505 /GVMM/HostCpus/5/CurTimerHz             0 Hz
00:04:29.435512 /GVMM/HostCpus/5/DesiredHz              0 Hz
00:04:29.435520 /GVMM/HostCpus/5/PPTChanges             0 times
00:04:29.435527 /GVMM/HostCpus/5/PPTStarts              0 times
00:04:29.435547 /GVMM/HostCpus/5/idxCpuSet              5 
00:04:29.435555 /GVMM/HostCpus/6                        6 
00:04:29.435562 /GVMM/HostCpus/6/CurTimerHz             0 Hz
00:04:29.435569 /GVMM/HostCpus/6/DesiredHz              0 Hz
00:04:29.435577 /GVMM/HostCpus/6/PPTChanges             0 times
00:04:29.435584 /GVMM/HostCpus/6/PPTStarts              0 times
00:04:29.435591 /GVMM/HostCpus/6/idxCpuSet              6 
00:04:29.435598 /GVMM/HostCpus/7                        7 
00:04:29.435606 /GVMM/HostCpus/7/CurTimerHz             0 Hz
00:04:29.435613 /GVMM/HostCpus/7/DesiredHz              0 Hz
00:04:29.435620 /GVMM/HostCpus/7/PPTChanges             0 times
00:04:29.435628 /GVMM/HostCpus/7/PPTStarts              0 times
00:04:29.435635 /GVMM/HostCpus/7/idxCpuSet              7 
00:04:29.435643 /GVMM/Sum/HaltBlocking             170261 calls
00:04:29.435651 /GVMM/Sum/HaltCalls                170235 calls
00:04:29.435658 /GVMM/Sum/HaltNotBlocking               0 calls
00:04:29.435665 /GVMM/Sum/HaltTimeouts              29883 calls
00:04:29.435673 /GVMM/Sum/HaltWakeUps                   0 calls
00:04:29.435681 /GVMM/Sum/PokeCalls                104742 calls
00:04:29.435688 /GVMM/Sum/PokeNotBusy                1320 calls
00:04:29.435696 /GVMM/Sum/PollCalls                   334 calls
00:04:29.435703 /GVMM/Sum/PollHalts                     0 calls
00:04:29.435711 /GVMM/Sum/PollWakeUps                   0 calls
00:04:29.435718 /GVMM/Sum/WakeUpCalls              140654 calls
00:04:29.435725 /GVMM/Sum/WakeUpNotHalted            1573 calls
00:04:29.435733 /GVMM/Sum/WakeUpWakeUps                 0 calls
00:04:29.435740 /GVMM/VM/HaltBlocking              170261 calls
00:04:29.435748 /GVMM/VM/HaltCalls                 170235 calls
00:04:29.435757 /GVMM/VM/HaltNotBlocking                0 calls
00:04:29.435765 /GVMM/VM/HaltTimeouts               29883 calls
00:04:29.435772 /GVMM/VM/HaltWakeUps                    0 calls
00:04:29.435780 /GVMM/VM/PokeCalls                 104742 calls
00:04:29.435787 /GVMM/VM/PokeNotBusy                 1320 calls
00:04:29.435795 /GVMM/VM/PollCalls                    334 calls
00:04:29.435802 /GVMM/VM/PollHalts                      0 calls
00:04:29.435810 /GVMM/VM/PollWakeUps                    0 calls
00:04:29.435817 /GVMM/VM/WakeUpCalls               140654 calls
00:04:29.435824 /GVMM/VM/WakeUpNotHalted             1573 calls
00:04:29.435832 /GVMM/VM/WakeUpWakeUps                  0 calls
00:04:29.435839 /GVMM/VMs                               1 calls
00:04:29.435847 /HGCM/FailedPageListLocking             0 count
00:04:29.435855 /HGCM/LargeCmdAllocs                    4 count
00:04:29.435862 /HGCM/MsgArrival                    38056 ticks/call (     2435640 ticks,      64 times, max    172076, min   14248)
00:04:29.435871 /HGCM/MsgCompletion                 38224 ticks/call (     2561046 ticks,      67 times, max    420160, min    6584)
00:04:29.435880 /HGCM/MsgTotal                     453284 ticks/call (    29010225 ticks,      64 times, max   3074441, min   38899)
00:04:29.435889 /HM/CPU0/Exit/HostNmiInGC               0 times
00:04:29.435896 /HM/CPU0/Exit/HostNmiInGCIpi            0 times
00:04:29.435904 /HM/CPU0/Exit/Trap/Gst/#AC              0 times
00:04:29.435911 /HM/CPU0/Exit/Trap/Gst/#AC-split-lock        0 times
00:04:29.435918 /HM/CPU0/Switch/Preempting              0 times
00:04:29.435925 /HM/CPU1/Exit/HostNmiInGC               0 times
00:04:29.435933 /HM/CPU1/Exit/HostNmiInGCIpi            0 times
00:04:29.435940 /HM/CPU1/Exit/Trap/Gst/#AC              0 times
00:04:29.435948 /HM/CPU1/Exit/Trap/Gst/#AC-split-lock        0 times
00:04:29.435954 /HM/CPU1/Switch/Preempting              0 times
00:04:29.435962 /HM/CPU2/Exit/HostNmiInGC               0 times
00:04:29.435970 /HM/CPU2/Exit/HostNmiInGCIpi            0 times
00:04:29.435977 /HM/CPU2/Exit/Trap/Gst/#AC              0 times
00:04:29.435985 /HM/CPU2/Exit/Trap/Gst/#AC-split-lock        0 times
00:04:29.435992 /HM/CPU2/Switch/Preempting              0 times
00:04:29.435999 /HM/CPU3/Exit/HostNmiInGC               0 times
00:04:29.436006 /HM/CPU3/Exit/HostNmiInGCIpi            0 times
00:04:29.436014 /HM/CPU3/Exit/Trap/Gst/#AC              0 times
00:04:29.436021 /HM/CPU3/Exit/Trap/Gst/#AC-split-lock        0 times
00:04:29.436029 /HM/CPU3/Switch/Preempting              0 times
00:04:29.436036 /IEM/CPU0/CodeTlb-Misses                0 count
00:04:29.436043 /IEM/CPU0/CodeTlb-PhysRev        ffffffffffff9c00 
00:04:29.436051 /IEM/CPU0/CodeTlb-Revision       fffff38000000000 
00:04:29.436059 /IEM/CPU0/CodeTlb-SlowReads             0 
00:04:29.436066 /IEM/CPU0/DataTlb-Misses                0 count
00:04:29.436074 /IEM/CPU0/DataTlb-PhysRev        ffffffffffff9c00 
00:04:29.436082 /IEM/CPU0/DataTlb-Revision       fffff38000000000 
00:04:29.436089 /IEM/CPU0/cInstructions            115361 count
00:04:29.436097 /IEM/CPU0/cLongJumps                    6 bytes
00:04:29.436104 /IEM/CPU0/cPendingCommit                0 bytes
00:04:29.436112 /IEM/CPU0/cPotentialExits          118113 count
00:04:29.436119 /IEM/CPU0/cRetAspectNotImplemented        0 count
00:04:29.436126 /IEM/CPU0/cRetErrStatuses               0 count
00:04:29.436134 /IEM/CPU0/cRetInfStatuses              89 count
00:04:29.436142 /IEM/CPU0/cRetInstrNotImplemented        0 count
00:04:29.436149 /IEM/CPU0/cbWritten                209558 bytes
00:04:29.436156 /IEM/CPU1/CodeTlb-Misses                0 count
00:04:29.436164 /IEM/CPU1/CodeTlb-PhysRev        ffffffffffff9c00 
00:04:29.436172 /IEM/CPU1/CodeTlb-Revision       fffff38000000000 
00:04:29.436179 /IEM/CPU1/CodeTlb-SlowReads             0 
00:04:29.436186 /IEM/CPU1/DataTlb-Misses                0 count
00:04:29.436194 /IEM/CPU1/DataTlb-PhysRev        ffffffffffff9c00 
00:04:29.436202 /IEM/CPU1/DataTlb-Revision       fffff38000000000 
00:04:29.436211 /IEM/CPU1/cInstructions              6344 count
00:04:29.436218 /IEM/CPU1/cLongJumps                    0 bytes
00:04:29.436226 /IEM/CPU1/cPendingCommit                0 bytes
00:04:29.436233 /IEM/CPU1/cPotentialExits            6528 count
00:04:29.436240 /IEM/CPU1/cRetAspectNotImplemented        0 count
00:04:29.436248 /IEM/CPU1/cRetErrStatuses               0 count
00:04:29.436255 /IEM/CPU1/cRetInfStatuses              51 count
00:04:29.436263 /IEM/CPU1/cRetInstrNotImplemented        0 count
00:04:29.436270 /IEM/CPU1/cbWritten                 12586 bytes
00:04:29.436277 /IEM/CPU2/CodeTlb-Misses                0 count
00:04:29.436285 /IEM/CPU2/CodeTlb-PhysRev        ffffffffffff9c00 
00:04:29.436292 /IEM/CPU2/CodeTlb-Revision       fffff38000000000 
00:04:29.436300 /IEM/CPU2/CodeTlb-SlowReads             0 
00:04:29.436307 /IEM/CPU2/DataTlb-Misses                0 count
00:04:29.436315 /IEM/CPU2/DataTlb-PhysRev        ffffffffffff9c00 
00:04:29.436323 /IEM/CPU2/DataTlb-Revision       fffff38000000000 
00:04:29.436331 /IEM/CPU2/cInstructions              4116 count
00:04:29.436339 /IEM/CPU2/cLongJumps                    0 bytes
00:04:29.436346 /IEM/CPU2/cPendingCommit                0 bytes
00:04:29.436376 /IEM/CPU2/cPotentialExits            4309 count
00:04:29.436384 /IEM/CPU2/cRetAspectNotImplemented        0 count
00:04:29.436391 /IEM/CPU2/cRetErrStatuses               0 count
00:04:29.436399 /IEM/CPU2/cRetInfStatuses              59 count
00:04:29.436419 /IEM/CPU2/cRetInstrNotImplemented        0 count
00:04:29.436426 /IEM/CPU2/cbWritten                  8154 bytes
00:04:29.436433 /IEM/CPU3/CodeTlb-Misses                0 count
00:04:29.436441 /IEM/CPU3/CodeTlb-PhysRev        ffffffffffff9c00 
00:04:29.436449 /IEM/CPU3/CodeTlb-Revision       fffff38000000000 
00:04:29.436456 /IEM/CPU3/CodeTlb-SlowReads             0 
00:04:29.436464 /IEM/CPU3/DataTlb-Misses                0 count
00:04:29.436471 /IEM/CPU3/DataTlb-PhysRev        ffffffffffff9c00 
00:04:29.436479 /IEM/CPU3/DataTlb-Revision       fffff38000000000 
00:04:29.436487 /IEM/CPU3/cInstructions              3390 count
00:04:29.436494 /IEM/CPU3/cLongJumps                    0 bytes
00:04:29.436502 /IEM/CPU3/cPendingCommit                0 bytes
00:04:29.436509 /IEM/CPU3/cPotentialExits            3609 count
00:04:29.436516 /IEM/CPU3/cRetAspectNotImplemented        0 count
00:04:29.436524 /IEM/CPU3/cRetErrStatuses               0 count
00:04:29.436531 /IEM/CPU3/cRetInfStatuses              29 count
00:04:29.436538 /IEM/CPU3/cRetInstrNotImplemented        0 count
00:04:29.436545 /IEM/CPU3/cbWritten                  6722 bytes
00:04:29.436553 /IOM/MmioMappingsStale                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.436562 /MM/HyperHeap/cbFree              8534064 bytes
00:04:29.436569 /MM/HyperHeap/cbHeap              8650432 bytes
00:04:29.436577 /PDM/BlkCache/cbCached                  0 bytes
00:04:29.436584 /PDM/BlkCache/cbCachedFru               0 bytes
00:04:29.436591 /PDM/BlkCache/cbCachedMruIn             0 bytes
00:04:29.436599 /PDM/BlkCache/cbCachedMruOut            0 bytes
00:04:29.436606 /PDM/BlkCache/cbMax               5242880 bytes
00:04:29.436614 /PDM/CritSects/8237A#0Auto/ContentionR3        0 times
00:04:29.436621 /PDM/CritSects/8237A#0Auto/ContentionRZLock        0 times
00:04:29.436628 /PDM/CritSects/8237A#0Auto/ContentionRZUnlock        0 times
00:04:29.436636 /PDM/CritSects/GIMDev#0Auto/ContentionR3        0 times
00:04:29.436643 /PDM/CritSects/GIMDev#0Auto/ContentionRZLock        0 times
00:04:29.436650 /PDM/CritSects/GIMDev#0Auto/ContentionRZUnlock        0 times
00:04:29.436658 /PDM/CritSects/IntNetXmit_0/ContentionR3        0 times
00:04:29.436666 /PDM/CritSects/IntNetXmit_0/ContentionRZLock        0 times
00:04:29.436673 /PDM/CritSects/IntNetXmit_0/ContentionRZUnlock        0 times
00:04:29.436680 /PDM/CritSects/MM-HYPER/ContentionR3        0 times
00:04:29.436688 /PDM/CritSects/MM-HYPER/ContentionRZLock        0 times
00:04:29.436696 /PDM/CritSects/MM-HYPER/ContentionRZUnlock        0 times
00:04:29.436704 /PDM/CritSects/NOP/ContentionR3         0 times
00:04:29.436711 /PDM/CritSects/NOP/ContentionRZLock        0 times
00:04:29.436718 /PDM/CritSects/NOP/ContentionRZUnlock        0 times
00:04:29.436725 /PDM/CritSects/PDM/ContentionR3        59 times
00:04:29.436732 /PDM/CritSects/PDM/ContentionRZLock        5 times
00:04:29.436739 /PDM/CritSects/PDM/ContentionRZUnlock        0 times
00:04:29.436747 /PDM/CritSects/PGM/ContentionR3      5935 times
00:04:29.436754 /PDM/CritSects/PGM/ContentionRZLock     1191 times
00:04:29.436761 /PDM/CritSects/PGM/ContentionRZUnlock        0 times
00:04:29.436769 /PDM/CritSects/TM Timer Lock/ContentionR3     1584 times
00:04:29.436776 /PDM/CritSects/TM Timer Lock/ContentionRZLock        0 times
00:04:29.436783 /PDM/CritSects/TM Timer Lock/ContentionRZUnlock        0 times
00:04:29.436791 /PDM/CritSects/TM VirtualSync Lock/ContentionR3     2819 times
00:04:29.436798 /PDM/CritSects/TM VirtualSync Lock/ContentionRZLock    15769 times
00:04:29.436806 /PDM/CritSects/TM VirtualSync Lock/ContentionRZUnlock        3 times
00:04:29.436813 /PDM/CritSects/VMMDev#0/ContentionR3        0 times
00:04:29.436820 /PDM/CritSects/VMMDev#0/ContentionRZLock        0 times
00:04:29.436827 /PDM/CritSects/VMMDev#0/ContentionRZUnlock        0 times
00:04:29.436835 /PDM/CritSects/VNet0/ContentionR3        0 times
00:04:29.436842 /PDM/CritSects/VNet0/ContentionRZLock        0 times
00:04:29.436849 /PDM/CritSects/VNet0/ContentionRZUnlock        0 times
00:04:29.436856 /PDM/CritSects/acpi#0/ContentionR3        0 times
00:04:29.436864 /PDM/CritSects/acpi#0/ContentionRZLock        0 times
00:04:29.436871 /PDM/CritSects/acpi#0/ContentionRZUnlock        0 times
00:04:29.436878 /PDM/CritSects/fastpipe#0Auto/ContentionR3       29 times
00:04:29.436885 /PDM/CritSects/fastpipe#0Auto/ContentionRZLock        0 times
00:04:29.436893 /PDM/CritSects/fastpipe#0Auto/ContentionRZUnlock        0 times
00:04:29.436900 /PDM/CritSects/mc146818#0Auto/ContentionR3        0 times
00:04:29.436907 /PDM/CritSects/mc146818#0Auto/ContentionRZLock        0 times
00:04:29.436915 /PDM/CritSects/mc146818#0Auto/ContentionRZUnlock        0 times
00:04:29.436922 /PDM/CritSects/pcarch#0Auto/ContentionR3        0 times
00:04:29.436929 /PDM/CritSects/pcarch#0Auto/ContentionRZLock        0 times
00:04:29.436936 /PDM/CritSects/pcarch#0Auto/ContentionRZUnlock        0 times
00:04:29.436944 /PDM/CritSects/pcbios#0Auto/ContentionR3        0 times
00:04:29.436951 /PDM/CritSects/pcbios#0Auto/ContentionRZLock        0 times
00:04:29.436958 /PDM/CritSects/pcbios#0Auto/ContentionRZUnlock        0 times
00:04:29.436965 /PDM/CritSects/pckbd#0Auto/ContentionR3        0 times
00:04:29.436972 /PDM/CritSects/pckbd#0Auto/ContentionRZLock        0 times
00:04:29.436980 /PDM/CritSects/pckbd#0Auto/ContentionRZUnlock        0 times
00:04:29.436987 /PDM/CritSects/pit#0/ContentionR3        0 times
00:04:29.436994 /PDM/CritSects/pit#0/ContentionRZLock        0 times
00:04:29.437001 /PDM/CritSects/pit#0/ContentionRZUnlock        0 times
00:04:29.437009 /PDM/CritSects/virtio-scsi#0Auto/ContentionR3       67 times
00:04:29.437017 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZLock      102 times
00:04:29.437024 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZUnlock        0 times
00:04:29.437031 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterExcl        0 times
00:04:29.437038 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterShared        0 times
00:04:29.437046 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterExcl        0 times
00:04:29.437053 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterShared        0 times
00:04:29.437060 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveExcl        0 times
00:04:29.437068 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveShared        0 times
00:04:29.437075 /PDM/CritSectsRw/IOM Lock/R3EnterExcl      114 times
00:04:29.437082 /PDM/CritSectsRw/IOM Lock/R3EnterShared    35865 times
00:04:29.437091 /PDM/CritSectsRw/IOM Lock/RZEnterExcl        0 times
00:04:29.437099 /PDM/CritSectsRw/IOM Lock/RZEnterShared   411487 times
00:04:29.437106 /PDM/Queue/DevHlp/AllocFailures         0 times
00:04:29.437113 /PDM/Queue/DevHlp/Flush                 0 calls
00:04:29.437121 /PDM/Queue/DevHlp/FlushLeftovers        0 times
00:04:29.437128 /PDM/Queue/DevHlp/Insert                0 calls
00:04:29.437135 /PDM/Queue/DevHlp/cItems                8 count
00:04:29.437143 /PDM/Queue/DevHlp/cbItem               64 bytes
00:04:29.437150 /PDM/Queue/Keyboard/AllocFailures        0 times
00:04:29.437158 /PDM/Queue/Keyboard/Flush               0 calls
00:04:29.437166 /PDM/Queue/Keyboard/FlushLeftovers        0 times
00:04:29.437173 /PDM/Queue/Keyboard/Insert              0 calls
00:04:29.437180 /PDM/Queue/Keyboard/cItems             64 count
00:04:29.437188 /PDM/Queue/Keyboard/cbItem             32 bytes
00:04:29.437195 /PDM/Queue/Mouse/AllocFailures          0 times
00:04:29.437202 /PDM/Queue/Mouse/Flush                  0 calls
00:04:29.437210 /PDM/Queue/Mouse/FlushLeftovers         0 times
00:04:29.437217 /PDM/Queue/Mouse/Insert                 0 calls
00:04:29.437225 /PDM/Queue/Mouse/cItems               128 count
00:04:29.437232 /PDM/Queue/Mouse/cbItem                48 bytes
00:04:29.437240 /PDM/Queue/SCSI-Eject/AllocFailures        0 times
00:04:29.437247 /PDM/Queue/SCSI-Eject/Flush             0 calls
00:04:29.437254 /PDM/Queue/SCSI-Eject/FlushLeftovers        0 times
00:04:29.437261 /PDM/Queue/SCSI-Eject/Insert            0 calls
00:04:29.437269 /PDM/Queue/SCSI-Eject/cItems            1 count
00:04:29.437276 /PDM/Queue/SCSI-Eject/cbItem           40 bytes
00:04:29.437283 /PDM/Queue/SCSI-Eject_1/AllocFailures        0 times
00:04:29.437291 /PDM/Queue/SCSI-Eject_1/Flush           0 calls
00:04:29.437299 /PDM/Queue/SCSI-Eject_1/FlushLeftovers        0 times
00:04:29.437306 /PDM/Queue/SCSI-Eject_1/Insert          0 calls
00:04:29.437329 /PDM/Queue/SCSI-Eject_1/cItems          1 count
00:04:29.437336 /PDM/Queue/SCSI-Eject_1/cbItem         40 bytes
00:04:29.437344 /PDM/Queue/SCSI-Eject_2/AllocFailures        0 times
00:04:29.437351 /PDM/Queue/SCSI-Eject_2/Flush           0 calls
00:04:29.437371 /PDM/Queue/SCSI-Eject_2/FlushLeftovers        0 times
00:04:29.437379 /PDM/Queue/SCSI-Eject_2/Insert          0 calls
00:04:29.437386 /PDM/Queue/SCSI-Eject_2/cItems          1 count
00:04:29.437393 /PDM/Queue/SCSI-Eject_2/cbItem         40 bytes
00:04:29.437401 /PGM/CPU0/cA20Changes                   2 times
00:04:29.437408 /PGM/CPU0/cGuestModeChanges          3700 times
00:04:29.437416 /PGM/CPU1/cA20Changes                   0 times
00:04:29.437424 /PGM/CPU1/cGuestModeChanges             3 times
00:04:29.437431 /PGM/CPU2/cA20Changes                   0 times
00:04:29.437439 /PGM/CPU2/cGuestModeChanges             3 times
00:04:29.437447 /PGM/CPU3/cA20Changes                   0 times
00:04:29.437455 /PGM/CPU3/cGuestModeChanges             3 times
00:04:29.437462 /PGM/ChunkR3Map/Mapped                518 count
00:04:29.437470 /PGM/ChunkR3Map/Unmapped                0 count
00:04:29.437477 /PGM/ChunkR3Map/c                     518 count
00:04:29.437485 /PGM/ChunkR3Map/cMax             4294967295 count
00:04:29.437492 /PGM/LargePage/Recheck                  0 times
00:04:29.437500 /PGM/LargePage/Refused                  2 times
00:04:29.437507 /PGM/LargePage/Reused                 412 times
00:04:29.437516 /PGM/Mmio2QueryAndResetDirtyBitmap        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437524 /PGM/Page/cAllPages               1577974 count
00:04:29.437531 /PGM/Page/cBalloonedPages               0 count
00:04:29.437539 /PGM/Page/cHandyPages                 128 count
00:04:29.437546 /PGM/Page/cLargePages                 210 count
00:04:29.437554 /PGM/Page/cLargePagesDisabled           0 count
00:04:29.437561 /PGM/Page/cMonitoredPages               0 count
00:04:29.437568 /PGM/Page/cPrivatePages            269810 count
00:04:29.437577 /PGM/Page/cPureMmioPages                4 count
00:04:29.437585 /PGM/Page/cReadLockedPages              0 count
00:04:29.437593 /PGM/Page/cReusedSharedPages            0 count
00:04:29.437600 /PGM/Page/cSharedPages                  0 count
00:04:29.437608 /PGM/Page/cWriteLockedPages             0 count
00:04:29.437615 /PGM/Page/cWrittenToPages               0 count
00:04:29.437622 /PGM/Page/cZeroPages              1308160 count
00:04:29.437630 /PGM/Pool/Grow                    7939128 ticks (     7939128 ticks,       1 times, max   7939128, min 7939128)
00:04:29.437639 /PGM/ShMod/Check                        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437647 /PGM/cRelocations                       0 times
00:04:29.437655 /PROF/CPU0/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437663 /PROF/CPU0/EM/ForcedActions         76159 times
00:04:29.437670 /PROF/CPU0/EM/Halted                10990 times
00:04:29.437678 /PROF/CPU0/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437686 /PROF/CPU0/EM/NEMExecuteCalled          0 times
00:04:29.437693 /PROF/CPU0/EM/RAWTotal                  0 times
00:04:29.437701 /PROF/CPU0/EM/REMTotal                  0 times
00:04:29.437708 /PROF/CPU0/EM/RecordedExits        630959 times
00:04:29.437715 /PROF/CPU0/EM/Total              969165554552 ticks/call (969165554552 ticks,       1 times, max 969165554552, min 969165554552)
00:04:29.437725 /PROF/CPU0/VM/Halt/Block          4233266 ns/call ( 40355727986 ticks,    9533 times, max 473529517, min       1)
00:04:29.437734 /PROF/CPU0/VM/Halt/BlockInsomnia  4233266 ns/call ( 40355727986 ticks,    9533 times, max 473529517, min       1)
00:04:29.437743 /PROF/CPU0/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437750 /PROF/CPU0/VM/Halt/BlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437759 /PROF/CPU0/VM/Halt/R0HaltBlock    6210300 ns/call (212907735814 ticks,   34283 times, max 438129222, min       7)
00:04:29.437768 /PROF/CPU0/VM/Halt/R0HaltBlockInsomnia  6210300 ns/call (212907735814 ticks,   34283 times, max 438129222, min       7)
00:04:29.437776 /PROF/CPU0/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437785 /PROF/CPU0/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437793 /PROF/CPU0/VM/Halt/R0HaltExec       37014 times
00:04:29.437800 /PROF/CPU0/VM/Halt/R0HaltExec/FromBlock    33666 times
00:04:29.437807 /PROF/CPU0/VM/Halt/R0HaltExec/FromSpin     2452 times
00:04:29.437815 /PROF/CPU0/VM/Halt/R0HaltHistoryCounter    47984 times
00:04:29.437822 /PROF/CPU0/VM/Halt/R0HaltHistorySucceeded      108 times
00:04:29.437829 /PROF/CPU0/VM/Halt/R0HaltHistoryToRing3        7 times
00:04:29.437837 /PROF/CPU0/VM/Halt/R0HaltToR3       10970 times
00:04:29.437844 /PROF/CPU0/VM/Halt/R0HaltToR3/FromSpin      166 times
00:04:29.437851 /PROF/CPU0/VM/Halt/R0HaltToR3/Other        0 times
00:04:29.437858 /PROF/CPU0/VM/Halt/R0HaltToR3/PendingFF    10187 times
00:04:29.437866 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitNoInt      111 times
00:04:29.437873 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitPendingFF      506 times
00:04:29.437880 /PROF/CPU0/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:04:29.437887 /PROF/CPU0/VM/Halt/Timers             337 ns/call (     6450782 ticks,   19132 times, max     34816, min       1)
00:04:29.437896 /PROF/CPU0/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437904 /PROF/CPU1/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437913 /PROF/CPU1/EM/ForcedActions         46352 times
00:04:29.437920 /PROF/CPU1/EM/Halted                11139 times
00:04:29.437928 /PROF/CPU1/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.437937 /PROF/CPU1/EM/NEMExecuteCalled          0 times
00:04:29.437945 /PROF/CPU1/EM/RAWTotal                  0 times
00:04:29.437952 /PROF/CPU1/EM/REMTotal                  0 times
00:04:29.437960 /PROF/CPU1/EM/RecordedExits        347497 times
00:04:29.437967 /PROF/CPU1/EM/Total              969165817320 ticks/call (969165817320 ticks,       1 times, max 969165817320, min 969165817320)
00:04:29.437976 /PROF/CPU1/VM/Halt/Block         10394592 ns/call (104735916818 ticks,   10076 times, max 457082146, min       1)
00:04:29.437985 /PROF/CPU1/VM/Halt/BlockInsomnia 10394592 ns/call (104735916818 ticks,   10076 times, max 457082146, min       1)
00:04:29.437993 /PROF/CPU1/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438001 /PROF/CPU1/VM/Halt/BlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438009 /PROF/CPU1/VM/Halt/R0HaltBlock    5680475 ns/call (149663480399 ticks,   26347 times, max 438139110, min      11)
00:04:29.438017 /PROF/CPU1/VM/Halt/R0HaltBlockInsomnia  5680475 ns/call (149663480399 ticks,   26347 times, max 438139110, min      11)
00:04:29.438026 /PROF/CPU1/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438034 /PROF/CPU1/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438042 /PROF/CPU1/VM/Halt/R0HaltExec       28076 times
00:04:29.438049 /PROF/CPU1/VM/Halt/R0HaltExec/FromBlock    25906 times
00:04:29.438057 /PROF/CPU1/VM/Halt/R0HaltExec/FromSpin     1556 times
00:04:29.438064 /PROF/CPU1/VM/Halt/R0HaltHistoryCounter    39186 times
00:04:29.438071 /PROF/CPU1/VM/Halt/R0HaltHistorySucceeded       18 times
00:04:29.438079 /PROF/CPU1/VM/Halt/R0HaltHistoryToRing3        3 times
00:04:29.438086 /PROF/CPU1/VM/Halt/R0HaltToR3       11110 times
00:04:29.438094 /PROF/CPU1/VM/Halt/R0HaltToR3/FromSpin      126 times
00:04:29.438101 /PROF/CPU1/VM/Halt/R0HaltToR3/Other        0 times
00:04:29.438108 /PROF/CPU1/VM/Halt/R0HaltToR3/PendingFF    10543 times
00:04:29.438115 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitNoInt       56 times
00:04:29.438123 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitPendingFF      385 times
00:04:29.438130 /PROF/CPU1/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:04:29.438137 /PROF/CPU1/VM/Halt/Timers             366 ns/call (     7378029 ticks,   20156 times, max     29688, min       1)
00:04:29.438145 /PROF/CPU1/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438153 /PROF/CPU2/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438161 /PROF/CPU2/EM/ForcedActions         46053 times
00:04:29.438169 /PROF/CPU2/EM/Halted                 9341 times
00:04:29.438176 /PROF/CPU2/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438184 /PROF/CPU2/EM/NEMExecuteCalled          0 times
00:04:29.438191 /PROF/CPU2/EM/RAWTotal                  0 times
00:04:29.438199 /PROF/CPU2/EM/REMTotal                  0 times
00:04:29.438206 /PROF/CPU2/EM/RecordedExits        338954 times
00:04:29.438214 /PROF/CPU2/EM/Total              969165831237 ticks/call (969165831237 ticks,       1 times, max 969165831237, min 969165831237)
00:04:29.438222 /PROF/CPU2/VM/Halt/Block          5871972 ns/call ( 49271721646 ticks,    8391 times, max 500527038, min       1)
00:04:29.438231 /PROF/CPU2/VM/Halt/BlockInsomnia  5695147 ns/call ( 47770896788 ticks,    8388 times, max 489340877, min       1)
00:04:29.438239 /PROF/CPU2/VM/Halt/BlockOnTime   500002796 ns/call (   500002796 ticks,       1 times, max 500002796, min 500002796)
00:04:29.438248 /PROF/CPU2/VM/Halt/BlockOverslept   411236 ns/call (      822473 ticks,       2 times, max    527245, min  295228)
00:04:29.438258 /PROF/CPU2/VM/Halt/R0HaltBlock    9516219 ns/call (205759698017 ticks,   21622 times, max 500842584, min       5)
00:04:29.438268 /PROF/CPU2/VM/Halt/R0HaltBlockInsomnia  8720191 ns/call (188242766585 ticks,   21587 times, max 499925852, min       5)
00:04:29.438276 /PROF/CPU2/VM/Halt/R0HaltBlockOnTime 499954446 ns/call (   999908893 ticks,       2 times, max 499957408, min 499951485)
00:04:29.438285 /PROF/CPU2/VM/Halt/R0HaltBlockOverslept   526809 ns/call (    17384727 ticks,      33 times, max    851946, min   55613)
00:04:29.438317 /PROF/CPU2/VM/Halt/R0HaltExec       23299 times
00:04:29.438324 /PROF/CPU2/VM/Halt/R0HaltExec/FromBlock    21190 times
00:04:29.438332 /PROF/CPU2/VM/Halt/R0HaltExec/FromSpin     1510 times
00:04:29.438339 /PROF/CPU2/VM/Halt/R0HaltHistoryCounter    32612 times
00:04:29.438359 /PROF/CPU2/VM/Halt/R0HaltHistorySucceeded       87 times
00:04:29.438367 /PROF/CPU2/VM/Halt/R0HaltHistoryToRing3       16 times
00:04:29.438374 /PROF/CPU2/VM/Halt/R0HaltToR3        9313 times
00:04:29.438381 /PROF/CPU2/VM/Halt/R0HaltToR3/FromSpin       98 times
00:04:29.438388 /PROF/CPU2/VM/Halt/R0HaltToR3/Other        0 times
00:04:29.438395 /PROF/CPU2/VM/Halt/R0HaltToR3/PendingFF     8783 times
00:04:29.438402 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitNoInt       86 times
00:04:29.438410 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitPendingFF      346 times
00:04:29.438417 /PROF/CPU2/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:04:29.438424 /PROF/CPU2/VM/Halt/Timers             351 ns/call (     5897926 ticks,   16765 times, max     50286, min       1)
00:04:29.438432 /PROF/CPU2/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438441 /PROF/CPU3/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438449 /PROF/CPU3/EM/ForcedActions         67805 times
00:04:29.438456 /PROF/CPU3/EM/Halted                27874 times
00:04:29.438463 /PROF/CPU3/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.438471 /PROF/CPU3/EM/NEMExecuteCalled          0 times
00:04:29.438479 /PROF/CPU3/EM/RAWTotal                  0 times
00:04:29.438486 /PROF/CPU3/EM/REMTotal                  0 times
00:04:29.438494 /PROF/CPU3/EM/RecordedExits        366441 times
00:04:29.438501 /PROF/CPU3/EM/Total              969165946691 ticks/call (969165946691 ticks,       1 times, max 969165946691, min 969165946691)
00:04:29.438510 /PROF/CPU3/VM/Halt/Block          5083226 ns/call (152079961520 ticks,   29918 times, max 765027820, min       1)
00:04:29.438519 /PROF/CPU3/VM/Halt/BlockInsomnia  1158645 ns/call ( 14338242845 ticks,   12375 times, max 438229941, min       1)
00:04:29.438527 /PROF/CPU3/VM/Halt/BlockOnTime    4886419 ns/call (  8819986937 ticks,    1805 times, max 209814879, min    6458)
00:04:29.438536 /PROF/CPU3/VM/Halt/BlockOverslept   530792 ns/call (  8353617852 ticks,   15738 times, max  51369714, min   50047)
00:04:29.438544 /PROF/CPU3/VM/Halt/R0HaltBlock    3412877 ns/call (102850482330 ticks,   30136 times, max  99298703, min     686)
00:04:29.438552 /PROF/CPU3/VM/Halt/R0HaltBlockInsomnia   875220 ns/call ( 15682206920 ticks,   17918 times, max  58309658, min     686)
00:04:29.438561 /PROF/CPU3/VM/Halt/R0HaltBlockOnTime  5131445 ns/call (  9718958426 ticks,    1894 times, max  73715618, min    1757)
00:04:29.438569 /PROF/CPU3/VM/Halt/R0HaltBlockOverslept   438412 ns/call (  4526167631 ticks,   10324 times, max  30108563, min   50020)
00:04:29.438577 /PROF/CPU3/VM/Halt/R0HaltExec       16248 times
00:04:29.438585 /PROF/CPU3/VM/Halt/R0HaltExec/FromBlock    15105 times
00:04:29.438592 /PROF/CPU3/VM/Halt/R0HaltExec/FromSpin      610 times
00:04:29.438599 /PROF/CPU3/VM/Halt/R0HaltHistoryCounter    43701 times
00:04:29.438606 /PROF/CPU3/VM/Halt/R0HaltHistorySucceeded       22 times
00:04:29.438613 /PROF/CPU3/VM/Halt/R0HaltHistoryToRing3      162 times
00:04:29.438622 /PROF/CPU3/VM/Halt/R0HaltToR3       27453 times
00:04:29.438630 /PROF/CPU3/VM/Halt/R0HaltToR3/FromSpin        0 times
00:04:29.438637 /PROF/CPU3/VM/Halt/R0HaltToR3/Other        0 times
00:04:29.438644 /PROF/CPU3/VM/Halt/R0HaltToR3/PendingFF    11493 times
00:04:29.438652 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitNoInt    12902 times
00:04:29.438659 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitPendingFF     2129 times
00:04:29.438666 /PROF/CPU3/VM/Halt/R0HaltToR3/SmallDelta      929 times
00:04:29.438673 /PROF/CPU3/VM/Halt/Timers            4369 ns/call (   315085801 ticks,   72113 times, max  47680228, min       2)
00:04:29.438682 /PROF/CPU3/VM/Halt/Yield             5538 ns/call (     1849713 ticks,     334 times, max     34082, min    1264)
00:04:29.438690 /Public/NetAdapter/0/BytesReceived    58425 bytes
00:04:29.438697 /Public/NetAdapter/0/BytesTransmitted    15738 bytes
00:04:29.438705 /Public/NetAdapter/0/virtio-net         0 
00:04:29.438712 /Public/Storage/VIRTIO-SCSI0/Port0/BytesRead 734026240 bytes
00:04:29.438719 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufAttempts        0 count
00:04:29.438727 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufSuccess        0 count
00:04:29.438734 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsRead    12539 count
00:04:29.438742 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSubmitted    12539 count
00:04:29.438749 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSucceeded    12539 count
00:04:29.438757 /Public/Storage/VIRTIO-SCSI0/Port1/BytesRead 88061952 bytes
00:04:29.438764 /Public/Storage/VIRTIO-SCSI0/Port1/BytesWritten 28585984 bytes
00:04:29.438771 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufAttempts        0 count
00:04:29.438778 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufSuccess        0 count
00:04:29.438786 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsRead     3299 count
00:04:29.438793 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSubmitted     4627 count
00:04:29.438800 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSucceeded     4627 count
00:04:29.438808 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsWrite     1328 count
00:04:29.438815 /Public/Storage/VIRTIO-SCSI0/Port2/BytesRead   521216 bytes
00:04:29.438823 /Public/Storage/VIRTIO-SCSI0/Port2/BytesWritten    61440 bytes
00:04:29.438830 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufAttempts        0 count
00:04:29.438837 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufSuccess        0 count
00:04:29.438845 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsRead      128 count
00:04:29.438852 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSubmitted      140 count
00:04:29.438860 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSucceeded      140 count
00:04:29.438867 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsWrite       12 count
00:04:29.438874 /SELM/LoadHidSel/GstReadErrors          0 times
00:04:29.438882 /SELM/LoadHidSel/NoGoodGuest            0 times
00:04:29.438889 /TM/CPU/00/cNsExecuting          11571681573 ns
00:04:29.438897 /TM/CPU/00/cNsHalted             40372406789 ns
00:04:29.438905 /TM/CPU/00/cNsOther              217268459607 ns
00:04:29.438912 /TM/CPU/00/cNsTotal              269212547969 ns
00:04:29.438920 /TM/CPU/00/cPeriodsExecuting       630959 count
00:04:29.438928 /TM/CPU/00/cPeriodsHalted            9599 count
00:04:29.438935 /TM/CPU/00/pctExecuting                 0 %
00:04:29.438943 /TM/CPU/00/pctHalted                   16 %
00:04:29.438950 /TM/CPU/00/pctOther                    82 %
00:04:29.438958 /TM/CPU/01/cNsExecuting          11725176269 ns
00:04:29.438965 /TM/CPU/01/cNsHalted             104756159391 ns
00:04:29.438973 /TM/CPU/01/cNsOther              152731286078 ns
00:04:29.438980 /TM/CPU/01/cNsTotal              269212621738 ns
00:04:29.438988 /TM/CPU/01/cPeriodsExecuting       347497 count
00:04:29.438995 /TM/CPU/01/cPeriodsHalted           10080 count
00:04:29.439003 /TM/CPU/01/pctExecuting                 0 %
00:04:29.439010 /TM/CPU/01/pctHalted                   16 %
00:04:29.439017 /TM/CPU/01/pctOther                    83 %
00:04:29.439025 /TM/CPU/02/cNsExecuting          11448277945 ns
00:04:29.439032 /TM/CPU/02/cNsHalted             49288322071 ns
00:04:29.439041 /TM/CPU/02/cNsOther              208476021247 ns
00:04:29.439050 /TM/CPU/02/cNsTotal              269212621263 ns
00:04:29.439057 /TM/CPU/02/cPeriodsExecuting       338954 count
00:04:29.439064 /TM/CPU/02/cPeriodsHalted            8374 count
00:04:29.439072 /TM/CPU/02/pctExecuting                 0 %
00:04:29.439079 /TM/CPU/02/pctHalted                   10 %
00:04:29.439087 /TM/CPU/02/pctOther                    89 %
00:04:29.439094 /TM/CPU/03/cNsExecuting          10735225610 ns
00:04:29.439102 /TM/CPU/03/cNsHalted             152448782671 ns
00:04:29.439109 /TM/CPU/03/cNsOther              106028647907 ns
00:04:29.439117 /TM/CPU/03/cNsTotal              269212656188 ns
00:04:29.439125 /TM/CPU/03/cPeriodsExecuting       366441 count
00:04:29.439132 /TM/CPU/03/cPeriodsHalted           25437 count
00:04:29.439139 /TM/CPU/03/pctExecuting                 0 %
00:04:29.439146 /TM/CPU/03/pctHalted                   11 %
00:04:29.439154 /TM/CPU/03/pctOther                    88 %
00:04:29.439161 /TM/CPU/pctExecuting                    0 %
00:04:29.439169 /TM/CPU/pctHalted                      13 %
00:04:29.439176 /TM/CPU/pctOther                       85 %
00:04:29.439184 /TM/MaxHzHint                           0 Hz
00:04:29.439191 /TM/PIT/Handler                         0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:04:29.439200 /TM/PIT/Irq                             0 times
00:04:29.439208 /TM/R0/1nsSteps                      1216 times
00:04:29.439215 /TM/R3/1nsSteps                      1622 times
00:04:29.439223 /TM/TSC/offCPU0                  395773554641868 ticks
00:04:29.439231 /TM/TSC/offCPU1                  395773554641868 ticks
00:04:29.439239 /TM/TSC/offCPU2                  395773554641868 ticks
00:04:29.439247 /TM/TSC/offCPU3                  395773554641868 ticks
00:04:29.439255 /TM/VirtualSync/CurrentOffset      702044 ns
00:04:29.439263 ********************* End of statistics **********************
00:04:29.441100 fastpipe: deviceDestruct g_bGuestPowerOff=1
00:04:29.444315 GIM: KVM: Resetting MSRs
00:04:29.448484 Changing the VM state from 'DESTROYING' to 'TERMINATED'
00:04:29.449791 Console: Machine state changed to 'PoweredOff'
00:04:29.449877 VBoxHeadless: processEventQueue: VERR_INTERRUPTED, termination requested
00:04:29.559627 VBoxHeadless: exiting
