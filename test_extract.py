#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试人员信息提取功能
"""

import uiautomator2 as u2
import re
import pandas as pd
import os
from datetime import datetime

# 配置
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")

# 初始化设备连接
d = u2.connect()

def init_csv():
    """初始化CSV文件"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "备注"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        # 获取页面XML
        xml = d.dump_hierarchy()
        
        # 简单解析文本内容
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def extract_person_info_debug():
    """调试版本的人员信息提取"""
    try:
        texts = get_page_text()
        print(f"[DEBUG] 页面总文本数量: {len(texts)}")
        print("[DEBUG] 所有页面文本:")
        for i, text in enumerate(texts):
            print(f"  {i}: {text}")
        
        people = []
        
        # 查找人员信息
        all_text = " ".join(texts)
        print(f"\n[DEBUG] 合并后的文本: {all_text[:500]}...")
        
        # 查找姓名 - 根据截图格式 "名：刘堤"
        name_pattern = r'名\s*[：:]\s*([^\s\d，。！？；：""''（）【】]+)'
        name_matches = re.finditer(name_pattern, all_text)
        
        for name_match in name_matches:
            person = {
                "姓名": name_match.group(1).strip(),
                "身份证号": "",
                "联系电话": "",
                "更新时间": ""
            }
            
            print(f"\n[DEBUG] 找到姓名: {person['姓名']}")
            
            # 查找身份证号 - 18位数字
            id_pattern = r'(\d{18})'
            id_matches = re.findall(id_pattern, all_text)
            print(f"[DEBUG] 找到的18位数字: {id_matches}")
            if id_matches:
                person["身份证号"] = id_matches[0]
            
            # 查找联系电话 - 11位手机号
            phone_pattern = r'(1[3-9]\d{9})'
            phone_matches = re.findall(phone_pattern, all_text)
            print(f"[DEBUG] 找到的手机号: {phone_matches}")
            if phone_matches:
                person["联系电话"] = phone_matches[0]
            
            # 查找更新时间
            time_pattern = r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})'
            time_matches = re.findall(time_pattern, all_text)
            print(f"[DEBUG] 找到的时间: {time_matches}")
            if time_matches:
                person["更新时间"] = time_matches[0]
            
            people.append(person)
            print(f"[INFO] 提取的人员信息: {person}")
        
        # 如果没找到，尝试逐个文本分析
        if not people:
            print("\n[DEBUG] 未找到完整人员信息，尝试逐个文本分析...")
            person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}
            
            for text in texts:
                # 查找姓名
                if "名：" in text or "名:" in text:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    if name_part.strip():
                        person["姓名"] = name_part.strip()
                        print(f"[DEBUG] 在文本 '{text}' 中找到姓名: {person['姓名']}")
                
                # 查找身份证号
                if re.search(r'\d{18}', text):
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person["身份证号"] = id_match.group(1)
                        print(f"[DEBUG] 在文本 '{text}' 中找到身份证: {person['身份证号']}")
                
                # 查找电话
                if re.search(r'1[3-9]\d{9}', text):
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person["联系电话"] = phone_match.group(1)
                        print(f"[DEBUG] 在文本 '{text}' 中找到电话: {person['联系电话']}")
                
                # 查找更新时间
                if re.search(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', text):
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person["更新时间"] = time_match.group(1)
                        print(f"[DEBUG] 在文本 '{text}' 中找到更新时间: {person['更新时间']}")
            
            if person["姓名"]:
                people.append(person)
                print(f"[INFO] 逐个分析提取的人员信息: {person}")
        
        return people
        
    except Exception as e:
        print(f"[ERROR] 提取人员信息失败: {e}")
        import traceback
        print(f"[ERROR] 详细错误: {traceback.format_exc()}")
        return []

def save_people_data(people, house_address):
    """保存人员数据到CSV"""
    if not people:
        print("[INFO] 没有人员数据需要保存")
        return 0
    
    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        
        saved_count = 0
        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) & 
                         (df['身份证号'] == person['身份证号'])]
            
            if existing.empty:
                # 添加新记录
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_address,
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "备注": "测试采集"
                }
                
                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] 保存: {person['姓名']} - {person['身份证号']}")
            else:
                print(f"[SKIP] 已存在: {person['姓名']} - {person['身份证号']}")
        
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    
    except Exception as e:
        print(f"[ERROR] 保存数据失败: {e}")
        return 0

def test_current_page():
    """测试当前页面的人员信息提取"""
    print("[INFO] 开始测试当前页面的人员信息提取...")
    
    # 初始化CSV
    init_csv()
    
    # 提取人员信息
    people = extract_person_info_debug()
    
    if people:
        print(f"\n[SUCCESS] 成功提取到 {len(people)} 个人员信息:")
        for i, person in enumerate(people, 1):
            print(f"  人员{i}: {person}")
        
        # 保存数据
        house_address = "测试地址"
        saved_count = save_people_data(people, house_address)
        print(f"\n[INFO] 保存了 {saved_count} 个人员到CSV文件")
        
    else:
        print("\n[FAIL] 未能提取到任何人员信息")
    
    print(f"\n[INFO] 测试完成，数据保存在: {RESULT_CSV}")

if __name__ == "__main__":
    test_current_page()
