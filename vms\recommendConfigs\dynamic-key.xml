﻿<?xml version="1.0"?>
<all>
	<!-- size指定触摸事件的参考分辨率 -->
	<game name="com.tencent.tmgp.pubgmhd|com.tencent.tmgp.pubgmhdce|com.tencent.ig">
		<!-- config 节点对应游戏内部不同分辨率 -->
		<config frame-size="1920,1080" touch-size="1920,1080">
			<!--  diff 可指定差异(默认5) -->
			<cond name="物品列表">
				<pixel pos="888,187" rgb="28,32,36"/>
				<pixel pos="950,187" rgb="28,32,36"/>
				<pixel pos="1000,187" rgb="28,32,36"/>
			</cond>
			<cond name="跳伞">
				<pixel pos="172,358" rgb="0,0,0"/>
				<pixel pos="244,358" rgb="0,0,0"/>
			</cond>
			<cond name="舔包">
				<pixel pos="969,305" rgb="0,0,0"/>
			</cond>
			<cond name="驾驶">
				<pixel pos="942,427" rgb="17,14,25"/>
			</cond>
			<cond name="开门">
				<pixel pos="938,552" rgb="17,17,17"/>
			</cond>
			
			<!-- 静态显示，无需指定坐标，仅控制是否显示-->
			<!-- 多个条件用|分隔；disable-cond 可指定禁止条件 -->
			<btn id="拾取第二个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="拾取第三个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="打开死亡背包">
				<touch enable-cond="舔包"/>
			</btn>
			
			<!-- 动态显示，需要指定坐标，范围参考touch-size -->
			<btn id="智能F">
				<touch enable-cond="物品列表" pos="1367,285"/>
				<touch enable-cond="跳伞" pos="313,475"/>
				<touch enable-cond="驾驶" pos="1442,635"/>
				<touch enable-cond="开门" pos="1458,840"/>
			</btn>
		</config>
		
		<config frame-size="2560,1440" touch-size="1920,1080">
			<!--  diff 可指定差异(默认5) -->
			<cond name="物品列表">
				<pixel pos="888,187" rgb="28,32,36"/>
				<pixel pos="950,187" rgb="28,32,36"/>
				<pixel pos="1000,187" rgb="28,32,36"/>
			</cond>
			<cond name="跳伞">
				<pixel pos="172,358" rgb="0,0,0"/>
				<pixel pos="244,358" rgb="0,0,0"/>
			</cond>
			<cond name="舔包">
				<pixel pos="969,305" rgb="0,0,0"/>
			</cond>
			<cond name="驾驶">
				<pixel pos="942,427" rgb="17,14,25"/>
			</cond>
			<cond name="开门">
				<pixel pos="938,552" rgb="17,17,17"/>
			</cond>
			
			<!-- 静态显示，无需指定坐标，仅控制是否显示-->
			<!-- 多个条件用|分隔；disable-cond 可指定禁止条件 -->
			<btn id="拾取第二个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="拾取第三个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="打开死亡背包">
				<touch enable-cond="舔包"/>
			</btn>
			
			<!-- 动态显示，需要指定坐标，范围参考touch-size -->
			<btn id="智能F">
				<touch enable-cond="物品列表" pos="1367,285"/>
				<touch enable-cond="跳伞" pos="313,475"/>
				<touch enable-cond="驾驶" pos="1442,635"/>
				<touch enable-cond="开门" pos="1458,840"/>
			</btn>
		</config>
		
		<config frame-size="1280,720" touch-size="1920,1080">
			<!--  diff 可指定差异(默认5) -->
			<cond name="物品列表" diff="8">
				<pixel pos="903,168" rgb="3,3,3"/>
				<pixel pos="903,198" rgb="3,3,3"/>
			</cond>
			<cond name="跳伞">
				<pixel pos="227,248" rgb="0,0,0"/>
				<pixel pos="328,248" rgb="0,0,0"/>
			</cond>
			<cond name="舔包">
				<pixel pos="861,160" rgb="0,0,0"/>
			</cond>
			<cond name="驾驶">
				<pixel pos="812,306" rgb="17,14,25"/>
			</cond>
			<cond name="开门">
				<pixel pos="818,495" rgb="17,17,17"/>
			</cond>
			
			<!-- 静态显示，无需指定坐标，仅控制是否显示-->
			<!-- 多个条件用|分隔；disable-cond 可指定禁止条件 -->
			<btn id="拾取第一个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="拾取第二个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="拾取第三个物品">
				<touch enable-cond="物品列表"/>
			</btn>
			<btn id="死亡物品开关">
				<touch enable-cond="舔包"/>
			</btn>
			
			<!-- 动态显示，需要指定坐标，范围参考touch-size -->
			<btn id="智能F">
				<touch enable-cond="物品列表" pos="1174,412"/>
				<touch enable-cond="跳伞" pos="429,463"/>
				<touch enable-cond="驾驶" pos="1292,470"/>
				<touch enable-cond="开门" pos="1292,759"/>
			</btn>
		</config>
	</game>
</all>

