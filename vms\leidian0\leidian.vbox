<?xml version="1.0" encoding="utf-8"?>
<!--
** DO NOT EDIT THIS FILE.
** If you make changes to this file while any VirtualBox related application
** is running, your changes will be overwritten later, without taking effect.
** Use VBoxManage or the VirtualBox Manager GUI to make changes.
-->
<VirtualBox xmlns="http://www.virtualbox.org/" version="1.18-windows">
  <Machine uuid="{20160302-aaaa-aaaa-0c9f-000000000000}" name="leidian0" OSType="Linux26_64">
    <MediaRegistry>
      <HardDisks>
        <HardDisk uuid="{20160302-bbbb-bbbb-0c9f-bbbb00000000}" location="D:\Program Files\LDPlayer9\system.vmdk" format="VMDK" type="Readonly"/>
        <HardDisk uuid="{20160302-cccc-cccc-0c9f-000000000000}" location="data.vmdk" format="VMDK" type="Normal"/>
      </HardDisks>
    </MediaRegistry>
    <ExtraData>
      <ExtraDataItem name="VBoxInternal/Devices/fastpipe/0/PCIBusNo" value="0"/>
      <ExtraDataItem name="VBoxInternal/Devices/fastpipe/0/PCIDeviceNo" value="18"/>
      <ExtraDataItem name="VBoxInternal/Devices/fastpipe/0/PCIFunctionNo" value="0"/>
      <ExtraDataItem name="VBoxInternal/Devices/fastpipe/0/Trusted" value="1"/>
      <ExtraDataItem name="VBoxInternal/PDM/Devices/fastpipe/Path" value="fastpipe.dll"/>
    </ExtraData>
    <Hardware>
      <CPU count="4">
        <LongMode enabled="true"/>
      </CPU>
      <Memory RAMSize="6144"/>
      <Paravirt provider="Default"/>
      <Boot>
        <Order position="1" device="HardDisk"/>
        <Order position="2" device="None"/>
        <Order position="3" device="None"/>
        <Order position="4" device="None"/>
      </Boot>
      <Display controller="None" VRAMSize="9"/>
      <RemoteDisplay enabled="false">
      </RemoteDisplay>
      <BIOS>
        <IOAPIC enabled="true"/>
        <Logo fadeIn="false" fadeOut="false" displayTime="0"/>
        <BootMenu mode="Disabled"/>
      </BIOS>
      <Network>
        <Adapter slot="0" enabled="true" MACAddress="00DBBAADC7D5" cable="true" type="virtio">
          <DisabledModes>
            <InternalNetwork name="intnet"/>
            <NATNetwork name="NatNetwork"/>
          </DisabledModes>
          <NAT>
            <!-- use it when needed <Forwarding name="Rule 1" proto="1" hostport="2222" guestport="2222"/> -->
          </NAT>
        </Adapter>
      </Network>
      <UART>
        <Port slot="0" enabled="false" IOBase="0x3f8" IRQ="4" path="C:\Users\<USER>\AppData\Roaming\ChangZhi2\kernel.log" hostMode="RawFile"/>
      </UART>
      <LPT>
        <Port slot="1" enabled="false" IOBase="0x378" IRQ="7"/>
      </LPT>
      <AudioAdapter driver="Null" enabled="false"/>
      <RTC localOrUTC="UTC"/>
      <SharedFolders>
	    </SharedFolders>
      <GuestProperties>
        <GuestProperty name="/VirtualBox/HostInfo/GUI/LanguageID" value="zh_CN" timestamp="1477037461514662600" flags=""/>
      </GuestProperties>
    </Hardware>
    <StorageControllers>
      <StorageController name="VirtIO" type="VirtioSCSI" PortCount="3" useHostIOCache="true" Bootable="true">
        <AttachedDevice type="HardDisk" hotpluggable="false" port="1" device="0">
          <Image uuid="{20160302-cccc-cccc-0c9f-000000000000}"/>
        </AttachedDevice>
      </StorageController>
    </StorageControllers>
  </Machine>
</VirtualBox>
