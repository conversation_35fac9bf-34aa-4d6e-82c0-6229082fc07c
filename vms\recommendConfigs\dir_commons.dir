{"commons": [{"packageNameType": 0, "packageNamePattern": "", "fileName": "B站哔哩哔哩.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "CC.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "NieR.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "nikke横屏120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "OPPO游戏中心.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "QQ.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "QQ炫舞.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "soul闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "up直播提示卸载支付宝.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "xp，抹机王，抹机神器，面具修改system.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "Yo语音.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "YY直播.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《A站》推雷电官方.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "《FGO》闪退卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《优酷》推雷电官方.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "《吞食天下归来》.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《微博》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《快手》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《暗黑破坏神》机型未支持.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "《爱奇艺》推雷电官方.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.jielan2|com.jl2.*|com.ztgame.jl2", "fileName": "《街篮2》横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.YiRen", "fileName": "一人之下.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "一念逍遥官服.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "一拳超人：正义执行iOS登录.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "七人传奇：光与暗之交战.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "三十六计-游族.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "三国杀官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "不思议迷宫.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "乱世王者iOS登录教程、启动过慢.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "亮剑跳转雷电9提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "仙凡幻想120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "仙剑世界.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "会玩.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "传奇天下.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "侠剑狂歌.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "修真江湖2.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "倩女幽魂.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "元梦之星新.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "元气骑士前传（九游）.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "全民主公2.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "公主连结.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "冒险岛：枫之传说.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "凌云诺.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.sao.*|com.tencent.tmgp.bilibili.sao", "fileName": "刀剑神域黑衣骑士：王牌.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "剑侠世界3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "剑网1：归来120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "勇士之路.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "千年之旅.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "华为游戏中心.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "原神.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "去哪儿旅行闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "召唤与合成2.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "合金弹头：觉醒.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ming.yun|com.dpstorm.or|com.dpstorm.or.bilibili", "fileName": "命运神界初始号.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "哆咪星球.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "圣斗士星矢（腾讯）iOS教程.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "地下城与勇士：起源.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "地铁跑酷横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "坎公骑冠剑官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "城主天下.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "复古传神.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "大话2口袋版.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "大话西游雷电渠道停运.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "天使之战-华为.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "天使之战120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "天命之子国际服攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "天命之子韩服攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "天地劫：幽城再临.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "天龙八部官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "奇游加速器闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "奥比岛：梦想国度120帧横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "奥特曼：集结.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "妖精的尾巴魔导少年iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "完美世界：诸神之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.pes", "fileName": "实况足球.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "寻仙.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "小小蚁国卡登陆，横屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "小森生活iOS.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "小猫爱消除.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "小芒雷电9白屏.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "小鱼传奇 (1).nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "少女前线2追放.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "少女前线：云图计划官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "山海镜花.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "崩坏：星穹铁道.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "幻世与冒险.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "幻塔.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "幻想名将录120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "异界事务所攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "弹弹堂大冒险.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "影之诗120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "御龙在天iOS.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "忍者必须死3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "忍者龟：归来120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "忘川风华录.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "悠久之树.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "懒人听书闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "戏鲸.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "我的世界.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "我的战争.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "战之刃：幸存者.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "战双帕弥什官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "抖音.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "指尖领主.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "掌上道聚城.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.taomee.moleleiting|com.leiting.mole.*|com.taomee.molenew.huawei|com.taomee.molenew.mi", "fileName": "摩尔庄园.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "文明与征服120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "斗罗大陆：魂师对决120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "斯露德.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "新倚天屠龙记.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "新剑侠情缘.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "新笑傲江湖.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "无神之界.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "明日之后闪退卡顿.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "明日方舟.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "星球：重启PC.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "星骸骑士.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "映客直播.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "春秋封神.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "晶核.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "暗影战姬120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "com.singyeagame.castlevaniatest", "fileName": "月夜狂想曲横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "有道词典闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "机动都市阿尔法9横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.jddsaef|com.netease.jddsaef.*|com.netease.g93na", "fileName": "机动都市阿尔法横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "机动都市阿尔法雷电5异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "极无双2横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "枫之谷M.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "梦幻西游.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "梦幻西游雷电渠道停运.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "梦幻诛仙官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "森之国度.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "次元姬小说.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "欢乐三国杀120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "欢乐斗地主.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "欢迎来到梦乐园.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "止戈之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "武林外传-雷电.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "永辉生活.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "江南百景图.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "沪江开心词场.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "流浪方舟关root.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "海外APEX手游.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "淘宝.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.ma84.*", "fileName": "游戏王：决斗链接.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "潮玩宇宙.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.denachina.g13002010.*|com.tencent.tmgp.g13002010|com.tencent.tmgp.g13002010|com.denachina.g13002014.denacn", "fileName": "灌篮高手.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "火影忍者：忍者新时代120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.ztgame.fir.qyc|com.ztgame.qyc.*", "fileName": "犬夜叉奈落之战提示.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "狐妖小红娘iOS.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "狩猎时刻.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "猎魂觉醒闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "猫和老鼠停运公告.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "猫咪公寓2攻略.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "王国纪元120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "王者猎人.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.tmgp.sgamece|com.tencent.tmgp.sgame", "fileName": "王者荣耀提示.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "王者营地.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "玛法英雄.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "玩吧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "环形战争.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "球球大作战.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "番茄免费小说.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "疾速酷跑.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "白夜极光.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "白荆回廊.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "百度网盘闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "碧蓝档案攻略.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "碧蓝航线官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "神仙道3.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "神曲H5120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "神雕侠侣.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "称王魏蜀吴.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "空之要塞：启航.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "穿越火线.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "站之刃：幸存者.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "第七史诗.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "第七史诗攻略.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "第五人格.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "第五人格雷电五黑屏.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "红警OLiOS+金条问题.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "约战：精灵再临.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "纸嫁衣4点击无响应.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "网易云音乐.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "腾讯视频.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "自由之刃.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "航海王热血航线卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "航海王：启航卡死.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "航海王：梦想指针.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "芒果TV安卓9卡加载.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "英雄连城120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "荒野迷城.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "蓝空幻想.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "蔚蓝档案.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "蚁族崛起渠道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "蜻蜓FM闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "西行纪：燃魂.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "西西语音.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "解忧小村落.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "触动精灵.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "识货.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "诛仙.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "赛马娘(繁中日服)官网.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "超能力冲刺.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "跃迁旅人.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "进击的骑士雷电5支付闪退.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "逆水寒.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "途牛旅游闪退.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "部落冲突.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "重生细胞横幅.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "重返未来：1999.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.netease.lztg|com.netease.lztg.*|com.tencent.tmgp.yongyong.lztg", "fileName": "量子特攻提示.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "金铲铲之战 - 巨龙之巢.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "金铲铲之战手游模式.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "铃兰之剑：为这和平的世界.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "银河境界线.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "镇魂街：天生为王.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "长安幻想官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "闪耀！优俊少女.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "问山海.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "问道官服.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "问道渠道服.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "闲鱼异常.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "雷电防骗指南.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "雷电：觉醒.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "雾境序列.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "霓虹深渊：无限横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "霸气英雄.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "非匿名指令.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "风色幻想命运传说横幅.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "风起苍岚.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "飞书.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "食物语iOS .nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "高能英雄.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "鬼泣：巅峰之战.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "魔之序曲120帧.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.tencent.jkchess", "fileName": "魔力宝贝iOS登录.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "魔力宝贝：旅人开高帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "魔灵召唤官网.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "鸿图之下.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "黎明觉醒：生机.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "黑暗之潮120.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "黑猫奇闻社120帧.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "龙与世界的尽头.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "龙之国物语.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "龙之谷2.nmp"}, {"packageNameType": 0, "packageNamePattern": "", "fileName": "龙族幻想.nmp"}, {"packageNameType": 1, "packageNamePattern": "", "fileName": "龙珠觉醒-buff渠道.nmp"}, {"packageNameType": 1, "packageNamePattern": "com.bilibili.priconne|com.tencent.tmgp.bilibili.priconne|com.bilibili.priconne.*", "fileName": "라그나로크오리진.nmp"}]}