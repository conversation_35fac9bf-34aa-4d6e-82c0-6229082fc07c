00:00:00.027453 VirtualBox VM 4.1.34 r2751252224 win.amd64 (Jul 26 2022 16:16:15) release log
00:00:00.027456 Log opened 2025-08-04T15:11:52.511352900Z
00:00:00.027457 Build Type: release
00:00:00.027462 OS Product: Windows 10
00:00:00.027463 OS Release: 10.0.19045
00:00:00.027464 OS Service Pack: 
00:00:00.044642 DMI Product Name: 10NBCTO1WW
00:00:00.050509 DMI Product Version: ThinkCentre M710t-N000
00:00:00.050528 Firmware type: UEFI
00:00:00.051048 Secure Boot: VERR_PRIVILEGE_NOT_HELD
00:00:00.051083 Host RAM: 32675MB (31.9GB) total, 19514MB (19.0GB) available
00:00:00.051087 Executable: C:\Program Files\ldplayer9box\Ld9BoxHeadless.exe
00:00:00.051088 Process ID: 21180
00:00:00.051088 Package type: WINDOWS_64BITS_GENERIC (OSE)
00:00:00.052998 Installed Extension Packs:
00:00:00.053023   None installed!
00:00:00.053639 Console: Machine state changed to 'Starting'
00:00:00.065668 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.065700 SUP: seg #1: R X 0x00001000 LB 0x00109000
00:00:00.065712 SUP: seg #2: R   0x0010a000 LB 0x0004a000
00:00:00.065719 SUP: seg #3: RW  0x00154000 LB 0x00013000
00:00:00.065727 SUP: seg #4: R   0x00167000 LB 0x0000e000
00:00:00.065734 SUP: seg #5: RW  0x00175000 LB 0x00003000
00:00:00.065744 SUP: seg #6: R   0x00178000 LB 0x0000b000
00:00:00.065754 SUP: seg #7: RWX 0x00183000 LB 0x00002000
00:00:00.065761 SUP: seg #8: R   0x00185000 LB 0x00007000
00:00:00.067373 SUP: Loaded Ld9VMMR0.r0 (C:\Program Files\ldplayer9box\Ld9VMMR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.067429 SUP: VMMR0EntryEx located at XXXXXXXXXXXXXXXX and VMMR0EntryFast at XXXXXXXXXXXXXXXX
00:00:00.067438 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9VMMR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.069821 Guest OS type: 'Linux26_64'
00:00:00.070825 fHMForced=true - No raw-mode support in this build!
00:00:00.073560 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\Snapshots' (snapshots) is unknown
00:00:00.073574 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk' is ntfs
00:00:00.074225 File system of 'D:\Program Files\LDPlayer9\system.vmdk' is ntfs
00:00:00.075109 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk' is ntfs
00:00:00.081442 Shared Clipboard: Service loaded
00:00:00.081462 Shared Clipboard: Mode: Off
00:00:00.081516 Shared Clipboard: Service running in headless mode
00:00:00.082487 Drag and drop service loaded
00:00:00.082507 Drag and drop mode: Off
00:00:00.084258 Extradata overrides:
00:00:00.084282   VBoxInternal/Devices/fastpipe/0/PCIBusNo="0"
00:00:00.084330   VBoxInternal/Devices/fastpipe/0/PCIDeviceNo="18"
00:00:00.084381   VBoxInternal/Devices/fastpipe/0/PCIFunctionNo="0"
00:00:00.084420   VBoxInternal/Devices/fastpipe/0/Trusted="1"
00:00:00.084460   VBoxInternal/PDM/Devices/fastpipe/Path="fastpipe.dll"
00:00:00.084705 ************************* CFGM dump *************************
00:00:00.084706 [/] (level 0)
00:00:00.084709   CpuExecutionCap   <integer> = 0x0000000000000064 (100)
00:00:00.084711   EnablePAE         <integer> = 0x0000000000000000 (0)
00:00:00.084711   HMEnabled         <integer> = 0x0000000000000001 (1)
00:00:00.084712   MemBalloonSize    <integer> = 0x0000000000000000 (0)
00:00:00.084712   Name              <string>  = "leidian0" (cb=9)
00:00:00.084713   NumCPUs           <integer> = 0x0000000000000004 (4)
00:00:00.084714   PageFusionAllowed <integer> = 0x0000000000000000 (0)
00:00:00.084714   RamHoleSize       <integer> = 0x0000000020000000 (536 870 912, 512 MB)
00:00:00.084716   RamSize           <integer> = 0x0000000180000000 (6 442 450 944, 6 144 MB, 6.0 GB)
00:00:00.084717   TimerMillies      <integer> = 0x000000000000000a (10)
00:00:00.084718   UUID              <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.084732 
00:00:00.084732 [/CPUM/] (level 1)
00:00:00.084733   GuestCpuName       <string>  = "host" (cb=5)
00:00:00.084734   NestedHWVirt       <integer> = 0x0000000000000000 (0)
00:00:00.084735   PortableCpuIdLevel <integer> = 0x0000000000000000 (0)
00:00:00.084735   SpecCtrl           <integer> = 0x0000000000000001 (1)
00:00:00.084736 
00:00:00.084736 [/CPUM/IsaExts/] (level 2)
00:00:00.084736 
00:00:00.084737 [/DBGC/] (level 1)
00:00:00.084737   GlobalInitScript <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-init" (cb=39)
00:00:00.084738   HistoryFile      <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-history" (cb=42)
00:00:00.084739   LocalInitScript  <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/dbgc-init" (cb=50)
00:00:00.084739 
00:00:00.084739 [/DBGF/] (level 1)
00:00:00.084740   Path <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/debug/;D:\Program Files\LDPlayer9\vms\leidian0/;cache*D:\Program Files\LDPlayer9\vms\leidian0/dbgcache/;C:\Users\<USER>\" (cb=159)
00:00:00.084741 
00:00:00.084741 [/Devices/] (level 1)
00:00:00.084741 
00:00:00.084741 [/Devices/8237A/] (level 2)
00:00:00.084742 
00:00:00.084742 [/Devices/8237A/0/] (level 3)
00:00:00.084743   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084743 
00:00:00.084744 [/Devices/GIMDev/] (level 2)
00:00:00.084744 
00:00:00.084744 [/Devices/GIMDev/0/] (level 3)
00:00:00.084745   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084745 
00:00:00.084745 [/Devices/VMMDev/] (level 2)
00:00:00.084746 
00:00:00.084746 [/Devices/VMMDev/0/] (level 3)
00:00:00.084751   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084752   PCIDeviceNo   <integer> = 0x0000000000000004 (4)
00:00:00.084752   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084753   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084753 
00:00:00.084753 [/Devices/VMMDev/0/Config/] (level 4)
00:00:00.084754   GuestCoreDumpDir <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\Snapshots" (cb=50)
00:00:00.084755 
00:00:00.084755 [/Devices/VMMDev/0/LUN#0/] (level 4)
00:00:00.084756   Driver <string>  = "HGCM" (cb=5)
00:00:00.084756 
00:00:00.084757 [/Devices/VMMDev/0/LUN#0/Config/] (level 5)
00:00:00.084758   Object <integer> = 0x0000000002b1eb20 (45 214 496)
00:00:00.084758 
00:00:00.084759 [/Devices/VMMDev/0/LUN#999/] (level 4)
00:00:00.084759   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084760 
00:00:00.084760 [/Devices/VMMDev/0/LUN#999/Config/] (level 5)
00:00:00.084761   First   <integer> = 0x0000000000000000 (0)
00:00:00.084761   Last    <integer> = 0x0000000000000000 (0)
00:00:00.084762   papLeds <integer> = 0x0000000002b14338 (45 171 512)
00:00:00.084762 
00:00:00.084762 [/Devices/acpi/] (level 2)
00:00:00.084763 
00:00:00.084763 [/Devices/acpi/0/] (level 3)
00:00:00.084764   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084764   PCIDeviceNo   <integer> = 0x0000000000000007 (7)
00:00:00.084765   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084765   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084766 
00:00:00.084766 [/Devices/acpi/0/Config/] (level 4)
00:00:00.084767   CpuHotPlug          <integer> = 0x0000000000000000 (0)
00:00:00.084768   FdcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.084768   HostBusPciAddress   <integer> = 0x0000000000000000 (0)
00:00:00.084769   HpetEnabled         <integer> = 0x0000000000000000 (0)
00:00:00.084769   IOAPIC              <integer> = 0x0000000000000001 (1)
00:00:00.084770   IocPciAddress       <integer> = 0x0000000000010000 (65 536)
00:00:00.084770   NumCPUs             <integer> = 0x0000000000000004 (4)
00:00:00.084771   Parallel0IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.084771   Parallel0Irq        <integer> = 0x0000000000000000 (0)
00:00:00.084772   Parallel1IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.084772   Parallel1Irq        <integer> = 0x0000000000000000 (0)
00:00:00.084773   Serial0IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.084773   Serial0Irq          <integer> = 0x0000000000000000 (0)
00:00:00.084774   Serial1IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.084774   Serial1Irq          <integer> = 0x0000000000000000 (0)
00:00:00.084775   ShowCpu             <integer> = 0x0000000000000001 (1)
00:00:00.084775   ShowRtc             <integer> = 0x0000000000000000 (0)
00:00:00.084776   SmcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.084776 
00:00:00.084776 [/Devices/acpi/0/LUN#0/] (level 4)
00:00:00.084777   Driver <string>  = "ACPIHost" (cb=9)
00:00:00.084777 
00:00:00.084778 [/Devices/acpi/0/LUN#0/Config/] (level 5)
00:00:00.084778 
00:00:00.084778 [/Devices/acpi/0/LUN#1/] (level 4)
00:00:00.084779   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084779 
00:00:00.084779 [/Devices/acpi/0/LUN#1/Config/] (level 5)
00:00:00.084780 
00:00:00.084780 [/Devices/acpi/0/LUN#2/] (level 4)
00:00:00.084781   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084781 
00:00:00.084782 [/Devices/acpi/0/LUN#2/Config/] (level 5)
00:00:00.084782 
00:00:00.084782 [/Devices/acpi/0/LUN#3/] (level 4)
00:00:00.084783   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084783 
00:00:00.084784 [/Devices/acpi/0/LUN#3/Config/] (level 5)
00:00:00.084784 
00:00:00.084784 [/Devices/apic/] (level 2)
00:00:00.084785 
00:00:00.084785 [/Devices/apic/0/] (level 3)
00:00:00.084786   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084786 
00:00:00.084786 [/Devices/apic/0/Config/] (level 4)
00:00:00.084787   IOAPIC  <integer> = 0x0000000000000001 (1)
00:00:00.084788   Mode    <integer> = 0x0000000000000003 (3)
00:00:00.084788   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.084789 
00:00:00.084789 [/Devices/e1000/] (level 2)
00:00:00.084789 
00:00:00.084789 [/Devices/fastpipe/] (level 2)
00:00:00.084790 
00:00:00.084790 [/Devices/fastpipe/0/] (level 3)
00:00:00.084791   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084791   PCIDeviceNo   <integer> = 0x0000000000000012 (18)
00:00:00.084792   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084792   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084793 
00:00:00.084793 [/Devices/i8254/] (level 2)
00:00:00.084793 
00:00:00.084793 [/Devices/i8254/0/] (level 3)
00:00:00.084794 
00:00:00.084794 [/Devices/i8254/0/Config/] (level 4)
00:00:00.084795 
00:00:00.084795 [/Devices/i8259/] (level 2)
00:00:00.084796 
00:00:00.084796 [/Devices/i8259/0/] (level 3)
00:00:00.084796   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084797 
00:00:00.084797 [/Devices/i8259/0/Config/] (level 4)
00:00:00.084798 
00:00:00.084798 [/Devices/ioapic/] (level 2)
00:00:00.084798 
00:00:00.084798 [/Devices/ioapic/0/] (level 3)
00:00:00.084799   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084800 
00:00:00.084800 [/Devices/ioapic/0/Config/] (level 4)
00:00:00.084801   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.084801 
00:00:00.084801 [/Devices/mc146818/] (level 2)
00:00:00.084802 
00:00:00.084802 [/Devices/mc146818/0/] (level 3)
00:00:00.084802 
00:00:00.084802 [/Devices/mc146818/0/Config/] (level 4)
00:00:00.084803   UseUTC <integer> = 0x0000000000000001 (1)
00:00:00.084804 
00:00:00.084804 [/Devices/parallel/] (level 2)
00:00:00.084804 
00:00:00.084804 [/Devices/pcarch/] (level 2)
00:00:00.084805 
00:00:00.084805 [/Devices/pcarch/0/] (level 3)
00:00:00.084805   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084806 
00:00:00.084806 [/Devices/pcarch/0/Config/] (level 4)
00:00:00.084807 
00:00:00.084807 [/Devices/pcbios/] (level 2)
00:00:00.084807 
00:00:00.084807 [/Devices/pcbios/0/] (level 3)
00:00:00.084808   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084808 
00:00:00.084808 [/Devices/pcbios/0/Config/] (level 4)
00:00:00.084809   APIC           <integer> = 0x0000000000000001 (1)
00:00:00.084810   BootDevice0    <string>  = "IDE" (cb=4)
00:00:00.084810   BootDevice1    <string>  = "NONE" (cb=5)
00:00:00.084811   BootDevice2    <string>  = "NONE" (cb=5)
00:00:00.084812   BootDevice3    <string>  = "NONE" (cb=5)
00:00:00.084812   FloppyDevice   <string>  = "i82078" (cb=7)
00:00:00.084813   HardDiskDevice <string>  = "piix3ide" (cb=9)
00:00:00.084813   IOAPIC         <integer> = 0x0000000000000001 (1)
00:00:00.084814   McfgBase       <integer> = 0x0000000000000000 (0)
00:00:00.084814   McfgLength     <integer> = 0x0000000000000000 (0)
00:00:00.084815   NumCPUs        <integer> = 0x0000000000000004 (4)
00:00:00.084815   PXEDebug       <integer> = 0x0000000000000000 (0)
00:00:00.084816   UUID           <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.084817   UuidLe         <integer> = 0x0000000000000000 (0)
00:00:00.084817 
00:00:00.084817 [/Devices/pcbios/0/Config/NetBoot/] (level 5)
00:00:00.084818 
00:00:00.084818 [/Devices/pcbios/0/Config/NetBoot/0/] (level 6)
00:00:00.084819   NIC           <integer> = 0x0000000000000000 (0)
00:00:00.084820   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084820   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.084821   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084821 
00:00:00.084821 [/Devices/pci/] (level 2)
00:00:00.084822 
00:00:00.084822 [/Devices/pci/0/] (level 3)
00:00:00.084822   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084823 
00:00:00.084823 [/Devices/pci/0/Config/] (level 4)
00:00:00.084823   IOAPIC <integer> = 0x0000000000000001 (1)
00:00:00.084824 
00:00:00.084824 [/Devices/pcibridge/] (level 2)
00:00:00.084825 
00:00:00.084825 [/Devices/pckbd/] (level 2)
00:00:00.084825 
00:00:00.084825 [/Devices/pckbd/0/] (level 3)
00:00:00.084826   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084826 
00:00:00.084827 [/Devices/pckbd/0/Config/] (level 4)
00:00:00.084827 
00:00:00.084827 [/Devices/pckbd/0/LUN#0/] (level 4)
00:00:00.084828   Driver <string>  = "KeyboardQueue" (cb=14)
00:00:00.084829 
00:00:00.084829 [/Devices/pckbd/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.084829   Driver <string>  = "MainKeyboard" (cb=13)
00:00:00.084830 
00:00:00.084830 [/Devices/pckbd/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.084831   Object <integer> = 0x0000000002b17a50 (45 185 616)
00:00:00.084832 
00:00:00.084832 [/Devices/pckbd/0/LUN#0/Config/] (level 5)
00:00:00.084832   QueueSize <integer> = 0x0000000000000040 (64)
00:00:00.084833 
00:00:00.084833 [/Devices/pckbd/0/LUN#1/] (level 4)
00:00:00.084834   Driver <string>  = "MouseQueue" (cb=11)
00:00:00.084834 
00:00:00.084834 [/Devices/pckbd/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.084835   Driver <string>  = "MainMouse" (cb=10)
00:00:00.084835 
00:00:00.084836 [/Devices/pckbd/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.084836   Object <integer> = 0x0000000002b18110 (45 187 344)
00:00:00.084837 
00:00:00.084837 [/Devices/pckbd/0/LUN#1/Config/] (level 5)
00:00:00.084838   QueueSize <integer> = 0x0000000000000080 (128)
00:00:00.084838 
00:00:00.084838 [/Devices/pcnet/] (level 2)
00:00:00.084839 
00:00:00.084839 [/Devices/serial/] (level 2)
00:00:00.084840 
00:00:00.084840 [/Devices/virtio-net/] (level 2)
00:00:00.084841 
00:00:00.084841 [/Devices/virtio-net/0/] (level 3)
00:00:00.084841   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084842   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.084842   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084843   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084843 
00:00:00.084843 [/Devices/virtio-net/0/Config/] (level 4)
00:00:00.084844   CableConnected <integer> = 0x0000000000000001 (1)
00:00:00.084845   LineSpeed      <integer> = 0x0000000000000000 (0)
00:00:00.084845   MAC            <bytes>   = "00 db 30 ef a7 99" (cb=6)
00:00:00.084846 
00:00:00.084846 [/Devices/virtio-net/0/LUN#0/] (level 4)
00:00:00.084847   Driver <string>  = "NAT" (cb=4)
00:00:00.084847 
00:00:00.084847 [/Devices/virtio-net/0/LUN#0/Config/] (level 5)
00:00:00.084848   AliasMode       <integer> = 0x0000000000000000 (0)
00:00:00.084848   BootFile        <string>  = "leidian0.pxe" (cb=13)
00:00:00.084849   DNSProxy        <integer> = 0x0000000000000000 (0)
00:00:00.084849   Network         <string>  = "**********/24" (cb=14)
00:00:00.084850   PassDomain      <integer> = 0x0000000000000001 (1)
00:00:00.084850   SockRcv         <integer> = 0x0000000000020000 (131 072)
00:00:00.084851   SockSnd         <integer> = 0x0000000000020000 (131 072)
00:00:00.084852   TFTPPrefix      <string>  = "C:\Users\<USER>\.Ld9VirtualBox\TFTP" (cb=34)
00:00:00.084852   UseHostResolver <integer> = 0x0000000000000000 (0)
00:00:00.084853 
00:00:00.084853 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/] (level 6)
00:00:00.084854 
00:00:00.084854 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/0/] (level 7)
00:00:00.084855   GuestPort <integer> = 0x00000000000008ae (2 222)
00:00:00.084855   HostPort  <integer> = 0x00000000000008ae (2 222)
00:00:00.084856   Name      <string>  = "tcp_2222_2222" (cb=14)
00:00:00.084856   Protocol  <string>  = "TCP" (cb=4)
00:00:00.084857 
00:00:00.084857 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/1/] (level 7)
00:00:00.084858   GuestPort <integer> = 0x00000000000015b3 (5 555)
00:00:00.084858   HostPort  <integer> = 0x00000000000015b3 (5 555)
00:00:00.084859   Name      <string>  = "tcp_5555_5555" (cb=14)
00:00:00.084859   Protocol  <string>  = "TCP" (cb=4)
00:00:00.084860 
00:00:00.084860 [/Devices/virtio-net/0/LUN#999/] (level 4)
00:00:00.084860   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084861 
00:00:00.084861 [/Devices/virtio-net/0/LUN#999/Config/] (level 5)
00:00:00.084862   First   <integer> = 0x0000000000000000 (0)
00:00:00.084862   Last    <integer> = 0x0000000000000000 (0)
00:00:00.084862   papLeds <integer> = 0x0000000002b14218 (45 171 224)
00:00:00.084863 
00:00:00.084863 [/Devices/virtio-scsi/] (level 2)
00:00:00.084864 
00:00:00.084864 [/Devices/virtio-scsi/0/] (level 3)
00:00:00.084865   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084865   PCIDeviceNo   <integer> = 0x000000000000000f (15)
00:00:00.084866   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084866   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084866 
00:00:00.084866 [/Devices/virtio-scsi/0/Config/] (level 4)
00:00:00.084867   Bootable   <integer> = 0x0000000000000001 (1)
00:00:00.084868   NumTargets <integer> = 0x0000000000000003 (3)
00:00:00.084868 
00:00:00.084868 [/Devices/virtio-scsi/0/LUN#0/] (level 4)
00:00:00.084869   Driver <string>  = "SCSI" (cb=5)
00:00:00.084869 
00:00:00.084869 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.084870   Driver <string>  = "VD" (cb=3)
00:00:00.084870 
00:00:00.084871 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.084871   Format    <string>  = "VMDK" (cb=5)
00:00:00.084872   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084872   Path      <string>  = "D:\Program Files\LDPlayer9\system.vmdk" (cb=39)
00:00:00.084873   ReadOnly  <integer> = 0x0000000000000001 (1)
00:00:00.084873   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084874 
00:00:00.084874 [/Devices/virtio-scsi/0/LUN#1/] (level 4)
00:00:00.084874   Driver <string>  = "SCSI" (cb=5)
00:00:00.084875 
00:00:00.084875 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.084876   Driver <string>  = "VD" (cb=3)
00:00:00.084876 
00:00:00.084876 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.084877   Format    <string>  = "VMDK" (cb=5)
00:00:00.084877   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084878   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk" (cb=50)
00:00:00.084878   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084878 
00:00:00.084879 [/Devices/virtio-scsi/0/LUN#2/] (level 4)
00:00:00.084879   Driver <string>  = "SCSI" (cb=5)
00:00:00.084880 
00:00:00.084880 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/] (level 5)
00:00:00.084880   Driver <string>  = "VD" (cb=3)
00:00:00.084881 
00:00:00.084881 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/Config/] (level 6)
00:00:00.084882   Format    <string>  = "VMDK" (cb=5)
00:00:00.084882   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084882   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk" (cb=52)
00:00:00.084883   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084883 
00:00:00.084883 [/Devices/virtio-scsi/0/LUN#999/] (level 4)
00:00:00.084884   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084884 
00:00:00.084885 [/Devices/virtio-scsi/0/LUN#999/Config/] (level 5)
00:00:00.084885   DeviceInstance        <string>  = "virtio-scsi/0" (cb=14)
00:00:00.084886   First                 <integer> = 0x0000000000000000 (0)
00:00:00.084886   Last                  <integer> = 0x0000000000000002 (2)
00:00:00.084887   pConsole              <integer> = 0x0000000002b12f40 (45 166 400)
00:00:00.084888   papLeds               <integer> = 0x0000000002b13a20 (45 169 184)
00:00:00.084888   pmapMediumAttachments <integer> = 0x0000000002b14358 (45 171 544)
00:00:00.084889 
00:00:00.084889 [/EM/] (level 1)
00:00:00.084890   TripleFaultReset <integer> = 0x0000000000000000 (0)
00:00:00.084890 
00:00:00.084890 [/GIM/] (level 1)
00:00:00.084891   Provider <string>  = "KVM" (cb=4)
00:00:00.084891 
00:00:00.084891 [/HM/] (level 1)
00:00:00.084892   64bitEnabled       <integer> = 0x0000000000000001 (1)
00:00:00.084893   EnableLargePages   <integer> = 0x0000000000000001 (1)
00:00:00.084893   EnableNestedPaging <integer> = 0x0000000000000001 (1)
00:00:00.084894   EnableUX           <integer> = 0x0000000000000001 (1)
00:00:00.084894   EnableVPID         <integer> = 0x0000000000000001 (1)
00:00:00.084894   Exclusive          <integer> = 0x0000000000000000 (0)
00:00:00.084895   HMForced           <integer> = 0x0000000000000001 (1)
00:00:00.084895   IBPBOnVMEntry      <integer> = 0x0000000000000000 (0)
00:00:00.084896   IBPBOnVMExit       <integer> = 0x0000000000000000 (0)
00:00:00.084896   L1DFlushOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.084897   L1DFlushOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.084897   MDSClearOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.084898   MDSClearOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.084898   SpecCtrlByHost     <integer> = 0x0000000000000000 (0)
00:00:00.084898   UseNEMInstead      <integer> = 0x0000000000000000 (0)
00:00:00.084899 
00:00:00.084899 [/MM/] (level 1)
00:00:00.084900   CanUseLargerHeap <integer> = 0x0000000000000000 (0)
00:00:00.084900 
00:00:00.084900 [/NEM/] (level 1)
00:00:00.084901   Allow64BitGuests <integer> = 0x0000000000000001 (1)
00:00:00.084901 
00:00:00.084901 [/PDM/] (level 1)
00:00:00.084901 
00:00:00.084902 [/PDM/AsyncCompletion/] (level 2)
00:00:00.084902 
00:00:00.084902 [/PDM/AsyncCompletion/File/] (level 3)
00:00:00.084903 
00:00:00.084903 [/PDM/AsyncCompletion/File/BwGroups/] (level 4)
00:00:00.084904 
00:00:00.084904 [/PDM/BlkCache/] (level 2)
00:00:00.084904   CacheSize <integer> = 0x0000000000500000 (5 242 880, 5 MB)
00:00:00.084905 
00:00:00.084906 [/PDM/Devices/] (level 2)
00:00:00.084906 
00:00:00.084906 [/PDM/Devices/fastpipe/] (level 3)
00:00:00.084907   Path <string>  = "fastpipe.dll" (cb=13)
00:00:00.084908 
00:00:00.084908 [/PDM/Drivers/] (level 2)
00:00:00.084908 
00:00:00.084908 [/PDM/Drivers/VBoxC/] (level 3)
00:00:00.084909   Path <string>  = "VBoxC" (cb=6)
00:00:00.084909 
00:00:00.084909 [/PDM/NetworkShaper/] (level 2)
00:00:00.084910 
00:00:00.084910 [/PDM/NetworkShaper/BwGroups/] (level 3)
00:00:00.084911 
00:00:00.084911 [/TM/] (level 1)
00:00:00.084911   UTCOffset <integer> = 0x0000000000000000 (0)
00:00:00.084912 
00:00:00.084912 ********************* End of CFGM dump **********************
00:00:00.085081 HM: HMR3Init: VT-x w/ nested paging and unrestricted guest execution hw support
00:00:00.085223 MM: cbHyperHeap=0x840000 (8650752)
00:00:00.096725 CPUM: fXStateHostMask=0x7; initial: 0x7; host XCR0=0x1f
00:00:00.098658 CPUM: Matched host CPU INTEL 0x6/0x9e/0x9 Intel_Core7_KabyLake with CPU DB entry 'Intel Core i7-6700K' (INTEL 0x6/0x5e/0x3 Intel_Core7_Skylake)
00:00:00.098776 CPUM: MXCSR_MASK=0xffff (host: 0xffff)
00:00:00.098803 CPUM: Microcode revision 0x000000B4
00:00:00.098821 CPUM: Changing leaf 13[0]: EBX=0x440 -> 0x340, ECX=0x440 -> 0x340
00:00:00.098844 CPUM: MSR/CPUID reconciliation insert: 0x0000010b IA32_FLUSH_CMD
00:00:00.098896 CPUM: SetGuestCpuIdFeature: Enabled Speculation Control.
00:00:00.099155 PGM: Host paging mode: AMD64+NX
00:00:00.099171 PGM: PGMPool: cMaxPages=3328 (u64MaxPages=3110)
00:00:00.099181 PGM: pgmR3PoolInit: cMaxPages=0xd00 cMaxUsers=0x1a00 cMaxPhysExts=0x1a00 fCacheEnable=true 
00:00:00.134337 TM: GIP - u32Mode=3 (Invariant) u32UpdateHz=93 u32UpdateIntervalNS=10734900 enmUseTscDelta=2 (Practically Zero) fGetGipCpu=0x1b cCpus=8
00:00:00.134405 TM: GIP - u64CpuHz=3 600 001 324 (0xd693a92c)  SUPGetCpuHzFromGip => 3 600 001 324
00:00:00.134414 TM: GIP - CPU: iCpuSet=0x0 idCpu=0x0 idApic=0x0 iGipCpu=0x6 i64TSCDelta=0 enmState=3 u64CpuHz=3600001113(*) cErrors=0
00:00:00.134420 TM: GIP - CPU: iCpuSet=0x1 idCpu=0x1 idApic=0x1 iGipCpu=0x7 i64TSCDelta=0 enmState=3 u64CpuHz=3600001264(*) cErrors=0
00:00:00.134427 TM: GIP - CPU: iCpuSet=0x2 idCpu=0x2 idApic=0x2 iGipCpu=0x3 i64TSCDelta=0 enmState=3 u64CpuHz=3599955467(*) cErrors=0
00:00:00.134432 TM: GIP - CPU: iCpuSet=0x3 idCpu=0x3 idApic=0x3 iGipCpu=0x4 i64TSCDelta=0 enmState=3 u64CpuHz=3600000949(*) cErrors=0
00:00:00.134437 TM: GIP - CPU: iCpuSet=0x4 idCpu=0x4 idApic=0x4 iGipCpu=0x5 i64TSCDelta=0 enmState=3 u64CpuHz=3600001155(*) cErrors=0
00:00:00.134443 TM: GIP - CPU: iCpuSet=0x5 idCpu=0x5 idApic=0x5 iGipCpu=0x0 i64TSCDelta=0 enmState=3 u64CpuHz=3600001324(*) cErrors=0
00:00:00.134449 TM: GIP - CPU: iCpuSet=0x6 idCpu=0x6 idApic=0x6 iGipCpu=0x2 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.134454 TM: GIP - CPU: iCpuSet=0x7 idCpu=0x7 idApic=0x7 iGipCpu=0x1 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.134519 TM: cTSCTicksPerSecond=3 600 001 324 (0xd693a92c) enmTSCMode=1 (VirtTscEmulated)
00:00:00.134522 TM: TSCTiedToExecution=false TSCNotTiedToHalt=false
00:00:00.135330 EMR3Init: fIemExecutesAll=false fGuruOnTripleFault=true 
00:00:00.136030 IEM: TargetCpu=CURRENT, Microarch=Intel_Core7_KabyLake
00:00:00.136295 GIM: Using provider 'KVM' (Implementation version: 0)
00:00:00.136310 CPUM: SetGuestCpuIdFeature: Enabled Hypervisor Present bit
00:00:00.136405 AIOMgr: Default manager type is 'Async'
00:00:00.136413 AIOMgr: Default file backend is 'NonBuffered'
00:00:00.136636 BlkCache: Cache successfully initialized. Cache size is 5242880 bytes
00:00:00.136656 BlkCache: Cache commit interval is 10000 ms
00:00:00.136663 BlkCache: Cache commit threshold is 2621440 bytes
00:00:00.138721 fastpipe::VBoxDevicesRegister: u32Version=0x60001 pCallbacks->u32Version=0xffe30010
00:00:00.138895 PcBios: [SMP] BIOS with 4 CPUs
00:00:00.138920 PcBios: Using the 386+ BIOS image.
00:00:00.138997 PcBios: MPS table at 000e1300
00:00:00.141247 PcBios: fCheckShutdownStatusForSoftReset=true   fClearShutdownStatusOnHardReset=true 
00:00:00.144919 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.144935 SUP: seg #1: R X 0x00001000 LB 0x0001f000
00:00:00.144941 SUP: seg #2: R   0x00020000 LB 0x0000c000
00:00:00.144946 SUP: seg #3: RW  0x0002c000 LB 0x00001000
00:00:00.144951 SUP: seg #4: R   0x0002d000 LB 0x00002000
00:00:00.144956 SUP: seg #5: RW  0x0002f000 LB 0x00001000
00:00:00.144961 SUP: seg #6: R   0x00030000 LB 0x00001000
00:00:00.144966 SUP: seg #7: RWX 0x00031000 LB 0x00001000
00:00:00.144971 SUP: seg #8: R   0x00032000 LB 0x00002000
00:00:00.145023 SUP: Loaded Ld9BoxDDR0.r0 (C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.145032 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.145318 CPUM: SetGuestCpuIdFeature: Enabled xAPIC
00:00:00.145329 CPUM: SetGuestCpuIdFeature: Enabled x2APIC
00:00:00.145679 IOAPIC: Using implementation 2.0! Chipset type ICH9
00:00:00.145770 PIT: mode=3 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.146021 VMMDev: cbDefaultBudget: 535 351 424 (1fe8d080)
00:00:00.152692 Shared Folders service loaded
00:00:00.153261 Guest Control service loaded
00:00:00.154032 VIRTIOSCSI0: Targets=3 Bootable=true  (unimplemented) R0Enabled=true  RCEnabled=false
00:00:00.154704 DrvVD: Flushes will be ignored
00:00:00.154729 DrvVD: Async flushes will be passed to the disk
00:00:00.156241 VD: VDInit finished with VINF_SUCCESS
00:00:00.156470 VD: Opening the disk took 634074 ns
00:00:00.156609 DrvVD: Flushes will be ignored
00:00:00.156620 DrvVD: Async flushes will be passed to the disk
00:00:00.171138 VD: Opening the disk took 13478944 ns
00:00:00.171312 DrvVD: Flushes will be ignored
00:00:00.171320 DrvVD: Async flushes will be passed to the disk
00:00:00.182969 VD: Opening the disk took 10817644 ns
00:00:00.190734 NAT: Guest address guess set to *********** by initialization
00:00:00.196878 NAT: DNS#0: *******
00:00:00.196938 NAT: DNS#1: *******
00:00:00.196957 NAT: ("SOCKET_RCVBUF":131072) has been ignored, because out of range (8, 1024)
00:00:00.196965 NAT: ("SOCKET_SNDBUF":131072) has been ignored, because out of range (8, 1024)
00:00:00.197971 NAT: Set redirect TCP 0.0.0.0:2222 -> 0.0.0.0:2222
00:00:00.198067 NAT: Set redirect TCP 0.0.0.0:5555 -> 0.0.0.0:5555
00:00:00.205419 fastpipe: load host successs mod=0000000006227850, path=C:\Program Files\ldplayer9box\libOpenglRender.dll
00:00:00.205440 fastpipe: GetFunctionAddr success mod=0000000006227850, lpszFuncName=OnLoad
00:00:00.205838 fastpipe: load host successs mod=0000000006227a90, path=C:\Program Files\ldplayer9box\host_manager.dll
00:00:00.205851 fastpipe: GetFunctionAddr success mod=0000000006227a90, lpszFuncName=OnLoad
00:00:00.207695 PGM: The CPU physical address width is 39 bits
00:00:00.207714 PGM: PGMR3InitFinalize: 4 MB PSE mask 0000007fffffffff -> VINF_SUCCESS
00:00:00.207901 TM: TMR3InitFinalize: fTSCModeSwitchAllowed=true 
00:00:00.208173 VMM: Thread-context hooks unavailable
00:00:00.208189 VMM: RTThreadPreemptIsPending() can be trusted
00:00:00.208197 VMM: Kernel preemption is possible
00:00:00.211013 HM: fWorldSwitcher=0x0 (fIbpbOnVmExit=false fIbpbOnVmEntry=false fL1dFlushOnVmEntry=false); fL1dFlushOnSched=false fMdsClearOnVmEntry=false
00:00:00.211031 HM: Using VT-x implementation 3.0
00:00:00.211036 HM: Max resume loops                  = 8192
00:00:00.211036 HM: Host CR4                          = 0x370678
00:00:00.211037 HM: Host EFER                         = 0xd01
00:00:00.211038 HM: MSR_IA32_SMM_MONITOR_CTL          = 0x0
00:00:00.211038 HM: MSR_IA32_FEATURE_CONTROL          = 0x5
00:00:00.211039 HM:   LOCK
00:00:00.211039 HM:   VMXON
00:00:00.211039 HM: MSR_IA32_VMX_BASIC                = 0xda040000000004
00:00:00.211040 HM:   VMCS id                           = 0x4
00:00:00.211042 HM:   VMCS size                         = 1024 bytes
00:00:00.211042 HM:   VMCS physical address limit       = None
00:00:00.211043 HM:   VMCS memory type                  = Write Back (WB)
00:00:00.211043 HM:   Dual-monitor treatment support    = true 
00:00:00.211044 HM:   OUTS & INS instruction-info       = true 
00:00:00.211044 HM:   Supports true-capability MSRs     = true 
00:00:00.211044 HM:   VM-entry Xcpt error-code optional = false
00:00:00.211046 HM: MSR_IA32_VMX_PINBASED_CTLS        = 0x7f00000016
00:00:00.211047 HM:   EXT_INT_EXIT
00:00:00.211047 HM:   NMI_EXIT
00:00:00.211047 HM:   VIRTUAL_NMI
00:00:00.211047 HM:   PREEMPT_TIMER
00:00:00.211048 HM:   POSTED_INT (must be cleared)
00:00:00.211048 HM: MSR_IA32_VMX_PROCBASED_CTLS       = 0xfff9fffe0401e172
00:00:00.211049 HM:   INT_WINDOW_EXIT
00:00:00.211049 HM:   USE_TSC_OFFSETTING
00:00:00.211049 HM:   HLT_EXIT
00:00:00.211050 HM:   INVLPG_EXIT
00:00:00.211050 HM:   MWAIT_EXIT
00:00:00.211050 HM:   RDPMC_EXIT
00:00:00.211051 HM:   RDTSC_EXIT
00:00:00.211051 HM:   CR3_LOAD_EXIT (must be set)
00:00:00.211051 HM:   CR3_STORE_EXIT (must be set)
00:00:00.211051 HM:   CR8_LOAD_EXIT
00:00:00.211052 HM:   CR8_STORE_EXIT
00:00:00.211052 HM:   USE_TPR_SHADOW
00:00:00.211052 HM:   NMI_WINDOW_EXIT
00:00:00.211052 HM:   MOV_DR_EXIT
00:00:00.211053 HM:   UNCOND_IO_EXIT
00:00:00.211053 HM:   USE_IO_BITMAPS
00:00:00.211053 HM:   MONITOR_TRAP_FLAG
00:00:00.211053 HM:   USE_MSR_BITMAPS
00:00:00.211054 HM:   MONITOR_EXIT
00:00:00.211054 HM:   PAUSE_EXIT
00:00:00.211054 HM:   USE_SECONDARY_CTLS
00:00:00.211056 HM: MSR_IA32_VMX_PROCBASED_CTLS2      = 0x5ffcff00000000
00:00:00.211057 HM:   VIRT_APIC_ACCESS
00:00:00.211057 HM:   EPT
00:00:00.211057 HM:   DESC_TABLE_EXIT
00:00:00.211058 HM:   RDTSCP
00:00:00.211058 HM:   VIRT_X2APIC_MODE
00:00:00.211058 HM:   VPID
00:00:00.211058 HM:   WBINVD_EXIT
00:00:00.211058 HM:   UNRESTRICTED_GUEST
00:00:00.211059 HM:   APIC_REG_VIRT (must be cleared)
00:00:00.211059 HM:   VIRT_INT_DELIVERY (must be cleared)
00:00:00.211059 HM:   PAUSE_LOOP_EXIT
00:00:00.211060 HM:   RDRAND_EXIT
00:00:00.211060 HM:   INVPCID
00:00:00.211060 HM:   VMFUNC
00:00:00.211062 HM:   VMCS_SHADOWING
00:00:00.211062 HM:   ENCLS_EXIT
00:00:00.211062 HM:   RDSEED_EXIT
00:00:00.211062 HM:   PML
00:00:00.211063 HM:   EPT_VE
00:00:00.211063 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211063 HM:   XSAVES_XRSTORS
00:00:00.211063 HM:   MODE_BASED_EPT_PERM
00:00:00.211064 HM:   SPPTP_EPT (must be cleared)
00:00:00.211064 HM:   PT_EPT (must be cleared)
00:00:00.211064 HM:   TSC_SCALING (must be cleared)
00:00:00.211064 HM:   USER_WAIT_PAUSE (must be cleared)
00:00:00.211065 HM:   ENCLV_EXIT (must be cleared)
00:00:00.211065 HM: MSR_IA32_VMX_ENTRY_CTLS           = 0x3ffff000011ff
00:00:00.211065 HM:   LOAD_DEBUG (must be set)
00:00:00.211066 HM:   IA32E_MODE_GUEST
00:00:00.211066 HM:   ENTRY_TO_SMM
00:00:00.211066 HM:   DEACTIVATE_DUAL_MON
00:00:00.211066 HM:   LOAD_PERF_MSR
00:00:00.211067 HM:   LOAD_PAT_MSR
00:00:00.211067 HM:   LOAD_EFER_MSR
00:00:00.211067 HM:   LOAD_BNDCFGS_MSR
00:00:00.211067 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211067 HM:   LOAD_RTIT_CTL_MSR (must be cleared)
00:00:00.211068 HM: MSR_IA32_VMX_EXIT_CTLS            = 0x1ffffff00036dff
00:00:00.211068 HM:   SAVE_DEBUG (must be set)
00:00:00.211069 HM:   HOST_ADDR_SPACE_SIZE
00:00:00.211069 HM:   LOAD_PERF_MSR
00:00:00.211069 HM:   ACK_EXT_INT
00:00:00.211069 HM:   SAVE_PAT_MSR
00:00:00.211069 HM:   LOAD_PAT_MSR
00:00:00.211070 HM:   SAVE_EFER_MSR
00:00:00.211070 HM:   LOAD_EFER_MSR
00:00:00.211070 HM:   SAVE_PREEMPT_TIMER
00:00:00.211070 HM:   CLEAR_BNDCFGS_MSR
00:00:00.211071 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211071 HM:   CLEAR_RTIT_CTL_MSR (must be cleared)
00:00:00.211071 HM: MSR_IA32_VMX_TRUE_PINBASED_CTLS   = 0x7f00000016
00:00:00.211072 HM: MSR_IA32_VMX_TRUE_PROCBASED_CTLS  = 0xfff9fffe04006172
00:00:00.211072 HM: MSR_IA32_VMX_TRUE_ENTRY_CTLS      = 0x3ffff000011fb
00:00:00.211073 HM: MSR_IA32_VMX_TRUE_EXIT_CTLS       = 0x1ffffff00036dfb
00:00:00.211075 HM: MSR_IA32_VMX_MISC                 = 0x7004c1e7
00:00:00.211075 HM:   PREEMPT_TIMER_TSC                 = 0x7
00:00:00.211076 HM:   EXIT_SAVE_EFER_LMA                = true 
00:00:00.211076 HM:   ACTIVITY_STATES                   = 0x7 ( HLT SHUTDOWN SIPI_WAIT )
00:00:00.211077 HM:   INTEL_PT                          = true 
00:00:00.211077 HM:   SMM_READ_SMBASE_MSR               = true 
00:00:00.211077 HM:   CR3_TARGET                        = 0x4
00:00:00.211078 HM:   MAX_MSR                           = 0x0 ( 512 )
00:00:00.211078 HM:   VMXOFF_BLOCK_SMI                  = true 
00:00:00.211078 HM:   VMWRITE_ALL                       = true 
00:00:00.211079 HM:   ENTRY_INJECT_SOFT_INT             = 0x1
00:00:00.211079 HM:   MSEG_ID                           = 0x0
00:00:00.211079 HM: MSR_IA32_VMX_VMCS_ENUM            = 0x2e
00:00:00.211080 HM:   HIGHEST_IDX                       = 0x17
00:00:00.211080 HM: MSR_IA32_VMX_EPT_VPID_CAP         = 0xf0106734141
00:00:00.211080 HM:   RWX_X_ONLY
00:00:00.211081 HM:   PAGE_WALK_LENGTH_4
00:00:00.211081 HM:   EMT_UC
00:00:00.211081 HM:   EMT_WB
00:00:00.211081 HM:   PDE_2M
00:00:00.211081 HM:   PDPTE_1G
00:00:00.211082 HM:   INVEPT
00:00:00.211082 HM:   EPT_ACCESS_DIRTY
00:00:00.211082 HM:   ADVEXITINFO_EPT
00:00:00.211082 HM:   INVEPT_SINGLE_CONTEXT
00:00:00.211083 HM:   INVEPT_ALL_CONTEXTS
00:00:00.211083 HM:   INVVPID
00:00:00.211083 HM:   INVVPID_INDIV_ADDR
00:00:00.211083 HM:   INVVPID_SINGLE_CONTEXT
00:00:00.211083 HM:   INVVPID_ALL_CONTEXTS
00:00:00.211084 HM:   INVVPID_SINGLE_CONTEXT_RETAIN_GLOBALS
00:00:00.211084 HM: MSR_IA32_VMX_VMFUNC               = 0x1
00:00:00.211084 HM:   EPTP_SWITCHING
00:00:00.211085 HM: MSR_IA32_VMX_CR0_FIXED0           = 0x80000021
00:00:00.211085 HM: MSR_IA32_VMX_CR0_FIXED1           = 0xffffffff
00:00:00.211085 HM: MSR_IA32_VMX_CR4_FIXED0           = 0x2000
00:00:00.211086 HM: MSR_IA32_VMX_CR4_FIXED1           = 0x3767ff
00:00:00.211086 HM: APIC-access page physaddr         = 0x000000054be98000
00:00:00.211087 HM: VCPU  0: MSR bitmap physaddr      = 0x00000007c159c000
00:00:00.211088 HM: VCPU  0: VMCS physaddr            = 0x00000007f1899000
00:00:00.211089 HM: VCPU  1: MSR bitmap physaddr      = 0x000000075ada0000
00:00:00.211089 HM: VCPU  1: VMCS physaddr            = 0x000000013d49d000
00:00:00.211090 HM: VCPU  2: MSR bitmap physaddr      = 0x000000012e3a4000
00:00:00.211090 HM: VCPU  2: VMCS physaddr            = 0x00000001861a1000
00:00:00.211091 HM: VCPU  3: MSR bitmap physaddr      = 0x00000003d5da8000
00:00:00.211091 HM: VCPU  3: VMCS physaddr            = 0x00000007fdda5000
00:00:00.211092 HM: Guest support: 32-bit and 64-bit
00:00:00.211101 HM: Supports VMCS EFER fields         = true 
00:00:00.211102 HM: Enabled VMX
00:00:00.211105 CPUM: SetGuestCpuIdFeature: Enabled SYSENTER/EXIT
00:00:00.211105 CPUM: SetGuestCpuIdFeature: Enabled PAE
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled LONG MODE
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled SYSCALL/RET
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled LAHF/SAHF
00:00:00.211107 CPUM: SetGuestCpuIdFeature: Enabled NX
00:00:00.211107 HM: Enabled nested paging
00:00:00.211107 HM:   EPT flush type                  = Single context
00:00:00.211108 HM: Enabled unrestricted guest execution
00:00:00.211108 HM: Enabled large page support
00:00:00.211108 HM: Enabled VPID
00:00:00.211108 HM:   VPID flush type                 = Single context
00:00:00.211109 HM: Enabled VMX-preemption timer (cPreemptTimerShift=7)
00:00:00.211109 HM: VT-x/AMD-V init method: Local
00:00:00.211111 EM: Exit history optimizations: enabled=true  enabled-r0=true  enabled-r0-no-preemption=false
00:00:00.211138 APIC: fPostedIntrsEnabled=false fVirtApicRegsEnabled=false fSupportsTscDeadline=false
00:00:00.211147 TMR3UtcNow: nsNow=1 754 320 312 694 585 100 nsPrev=0 -> cNsDelta=1 754 320 312 694 585 100 (offLag=0 offVirtualSync=0 offVirtualSyncGivenUp=0, NowAgain=1 754 320 312 694 585 100)
00:00:00.211160 VMM: fUsePeriodicPreemptionTimers=false
00:00:00.211211 CPUM: Logical host processors: 8 present, 8 max, 8 online, online mask: 00000000000000ff
00:00:00.211212 CPUM: Physical host cores: 4
00:00:00.211212 ************************* CPUID dump ************************
00:00:00.211219          Raw Standard CPUID Leaves
00:00:00.211219      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211221 Gst: 00000000/0000  00000016 756e6547 6c65746e 49656e69
00:00:00.211223 Hst:                00000016 756e6547 6c65746e 49656e69
00:00:00.211223 Gst: 00000001/0000  000906e9 00040800 d6fa2203 178bfbff
00:00:00.211224 Hst:                000906e9 03100800 7ffafbff bfebfbff
00:00:00.211225 Gst: 00000002/0000  76036301 00f0b5ff 00000000 00c30000
00:00:00.211226 Hst:                76036301 00f0b5ff 00000000 00c30000
00:00:00.211227 Gst: 00000003/0000  00000000 00000000 00000000 00000000
00:00:00.211227 Hst:                00000000 00000000 00000000 00000000
00:00:00.211228 Gst: 00000004/0000  0c000121 01c0003f 0000003f 00000000
00:00:00.211228 Hst:                1c004121 01c0003f 0000003f 00000000
00:00:00.211229 Gst: 00000004/0001  0c000122 01c0003f 0000003f 00000000
00:00:00.211230 Hst:                1c004122 01c0003f 0000003f 00000000
00:00:00.211230 Gst: 00000004/0002  0c000143 00c0003f 000003ff 00000000
00:00:00.211231 Hst:                1c004143 00c0003f 000003ff 00000000
00:00:00.211231 Gst: 00000004/0003  0c000163 03c0003f 00001fff 00000006
00:00:00.211232 Hst:                1c03c163 03c0003f 00001fff 00000006
00:00:00.211233 Gst: 00000004/0004  0c000000 00000000 00000000 00000000
00:00:00.211233 Hst:                00000000 00000000 00000000 00000000
00:00:00.211234 Gst: 00000005/0000  00000000 00000000 00000000 00000000
00:00:00.211234 Hst:                00000040 00000040 00000003 00142120
00:00:00.211235 Gst: 00000006/0000  00000000 00000000 00000000 00000000
00:00:00.211235 Hst:                000027f7 00000002 00000009 00000000
00:00:00.211236 Gst: 00000007/0000  00000000 00842421 00000000 1c000400
00:00:00.211237 Hst:                00000000 029c6fbf 00000000 9c002400
00:00:00.211237 Gst: 00000007/0001  00000000 00000000 00000000 00000000
00:00:00.211238 Hst:                00000000 00000000 00000000 00000000
00:00:00.211238 Gst: 00000008/0000  00000000 00000000 00000000 00000000
00:00:00.211238 Hst:                00000000 00000000 00000000 00000000
00:00:00.211239 Gst: 00000009/0000  00000000 00000000 00000000 00000000
00:00:00.211239 Hst:                00000000 00000000 00000000 00000000
00:00:00.211240 Gst: 0000000a/0000  00000000 00000000 00000000 00000000
00:00:00.211240 Hst:                07300404 00000000 00000000 00000603
00:00:00.211241 Gst: 0000000b/0000  00000000 00000001 00000100 00000000
00:00:00.211241 Hst:                00000001 00000002 00000100 00000003
00:00:00.211242 Gst: 0000000b/0001  00000002 00000004 00000201 00000000
00:00:00.211242 Hst:                00000004 00000008 00000201 00000003
00:00:00.211243 Gst: 0000000b/0002  00000000 00000000 00000002 00000000
00:00:00.211243 Hst:                00000000 00000000 00000002 00000003
00:00:00.211244 Gst: 0000000c/0000  00000000 00000000 00000000 00000000
00:00:00.211244 Hst:                00000000 00000000 00000000 00000000
00:00:00.211244 Gst: 0000000d/0000  00000007 00000340 00000340 00000000
00:00:00.211245 Hst:                0000001f 00000440 00000440 00000000
00:00:00.211245 Gst: 0000000d/0001  00000000 00000440 00000000 00000000
00:00:00.211246 Hst:                0000000f 00000440 00000100 00000000
00:00:00.211246 Gst: 0000000d/0002  00000100 00000240 00000000 00000000
00:00:00.211247 Hst:                00000100 00000240 00000000 00000000
00:00:00.211247 Gst: 0000000d/0003  00000000 00000000 00000000 00000000
00:00:00.211248 Hst:                00000040 000003c0 00000000 00000000
00:00:00.211248 Gst: 0000000d/0004  00000000 00000000 00000000 00000000
00:00:00.211249 Hst:                00000040 00000400 00000000 00000000
00:00:00.211249 Gst: 0000000d/0005  00000000 00000000 00000000 00000000
00:00:00.211250 Hst:                00000000 00000000 00000000 00000000
00:00:00.211250 Gst: 0000000d/0006  00000000 00000000 00000000 00000000
00:00:00.211250 Hst:                00000000 00000000 00000000 00000000
00:00:00.211251 Gst: 0000000d/0007  00000000 00000000 00000000 00000000
00:00:00.211251 Hst:                00000000 00000000 00000000 00000000
00:00:00.211252 Gst: 0000000d/0008  00000000 00000000 00000000 00000000
00:00:00.211252 Hst:                00000080 00000000 00000001 00000000
00:00:00.211253 Gst: 0000000d/0009  00000000 00000000 00000000 00000000
00:00:00.211253 Hst:                00000000 00000000 00000000 00000000
00:00:00.211268 Gst: 0000000e/0000  00000000 00000000 00000000 00000000
00:00:00.211268 Hst:                00000000 00000000 00000000 00000000
00:00:00.211269 Gst: 0000000f/0000  00000000 00000000 00000000 00000000
00:00:00.211269 Hst:                00000000 00000000 00000000 00000000
00:00:00.211269 Gst: 00000010/0000  00000000 00000000 00000000 00000000
00:00:00.211270 Hst:                00000000 00000000 00000000 00000000
00:00:00.211270 Gst: 00000011/0000  00000000 00000000 00000000 00000000
00:00:00.211271 Hst:                00000000 00000000 00000000 00000000
00:00:00.211271 Gst: 00000012/0000  00000000 00000000 00000000 00000000
00:00:00.211272 Hst:                00000000 00000000 00000000 00000000
00:00:00.211272 Gst: 00000013/0000  00000000 00000000 00000000 00000000
00:00:00.211272 Hst:                00000000 00000000 00000000 00000000
00:00:00.211273 Gst: 00000014/0000  00000000 00000000 00000000 00000000
00:00:00.211273 Hst:                00000001 0000000f 00000007 00000000
00:00:00.211274 Hst: 00000015/0000  00000002 0000012c 00000000 00000000
00:00:00.211275 Hst: 00000016/0000  00000e10 00001068 00000064 00000000
00:00:00.211275                                Name: GenuineIntel
00:00:00.211276                            Supports: 0x00000000-0x00000016
00:00:00.211279                              Family:  6 	Extended: 0 	Effective: 6
00:00:00.211280                               Model: 14 	Extended: 9 	Effective: 158
00:00:00.211280                            Stepping: 9
00:00:00.211281                                Type: 0 (primary)
00:00:00.211281                             APIC ID: 0x00
00:00:00.211282                        Logical CPUs: 4
00:00:00.211282                        CLFLUSH Size: 8
00:00:00.211283                            Brand ID: 0x00
00:00:00.211283 Features
00:00:00.211284   Mnemonic - Description                                  = guest (host)
00:00:00.211286   FPU - x87 FPU on Chip                                   = 1 (1)
00:00:00.211287   VME - Virtual 8086 Mode Enhancements                    = 1 (1)
00:00:00.211287   DE - Debugging extensions                               = 1 (1)
00:00:00.211288   PSE - Page Size Extension                               = 1 (1)
00:00:00.211289   TSC - Time Stamp Counter                                = 1 (1)
00:00:00.211290   MSR - Model Specific Registers                          = 1 (1)
00:00:00.211290   PAE - Physical Address Extension                        = 1 (1)
00:00:00.211291   MCE - Machine Check Exception                           = 1 (1)
00:00:00.211291   CX8 - CMPXCHG8B instruction                             = 1 (1)
00:00:00.211292   APIC - APIC On-Chip                                     = 1 (1)
00:00:00.211293   SEP - SYSENTER and SYSEXIT Present                      = 1 (1)
00:00:00.211293   MTRR - Memory Type Range Registers                      = 1 (1)
00:00:00.211294   PGE - PTE Global Bit                                    = 1 (1)
00:00:00.211294   MCA - Machine Check Architecture                        = 1 (1)
00:00:00.211295   CMOV - Conditional Move instructions                    = 1 (1)
00:00:00.211296   PAT - Page Attribute Table                              = 1 (1)
00:00:00.211296   PSE-36 - 36-bit Page Size Extension                     = 1 (1)
00:00:00.211297   PSN - Processor Serial Number                           = 0 (0)
00:00:00.211297   CLFSH - CLFLUSH instruction                             = 1 (1)
00:00:00.211298   DS - Debug Store                                        = 0 (1)
00:00:00.211299   ACPI - Thermal Mon. & Soft. Clock Ctrl.                 = 0 (1)
00:00:00.211299   MMX - Intel MMX Technology                              = 1 (1)
00:00:00.211300   FXSR - FXSAVE and FXRSTOR instructions                  = 1 (1)
00:00:00.211301   SSE - SSE support                                       = 1 (1)
00:00:00.211301   SSE2 - SSE2 support                                     = 1 (1)
00:00:00.211302   SS - Self Snoop                                         = 0 (1)
00:00:00.211303   HTT - Hyper-Threading Technology                        = 1 (1)
00:00:00.211303   TM - Therm. Monitor                                     = 0 (1)
00:00:00.211304   PBE - Pending Break Enabled                             = 0 (1)
00:00:00.211304   SSE3 - SSE3 support                                     = 1 (1)
00:00:00.211305   PCLMUL - PCLMULQDQ support (for AES-GCM)                = 1 (1)
00:00:00.211306   DTES64 - DS Area 64-bit Layout                          = 0 (1)
00:00:00.211306   MONITOR - MONITOR/MWAIT instructions                    = 0 (1)
00:00:00.211307   CPL-DS - CPL Qualified Debug Store                      = 0 (1)
00:00:00.211307   VMX - Virtual Machine Extensions                        = 0 (1)
00:00:00.211308   SMX - Safer Mode Extensions                             = 0 (1)
00:00:00.211309   EST - Enhanced SpeedStep Technology                     = 0 (1)
00:00:00.211309   TM2 - Terminal Monitor 2                                = 0 (1)
00:00:00.211310   SSSE3 - Supplemental Streaming SIMD Extensions 3        = 1 (1)
00:00:00.211310   CNTX-ID - L1 Context ID                                 = 0 (0)
00:00:00.211311   SDBG - Silicon Debug interface                          = 0 (1)
00:00:00.211311   FMA - Fused Multiply Add extensions                     = 0 (1)
00:00:00.211312   CX16 - CMPXCHG16B instruction                           = 1 (1)
00:00:00.211313   TPRUPDATE - xTPR Update Control                         = 0 (1)
00:00:00.211313   PDCM - Perf/Debug Capability MSR                        = 0 (1)
00:00:00.211314   PCID - Process Context Identifiers                      = 1 (1)
00:00:00.211314   DCA - Direct Cache Access                               = 0 (0)
00:00:00.211315   SSE4_1 - SSE4_1 support                                 = 1 (1)
00:00:00.211316   SSE4_2 - SSE4_2 support                                 = 1 (1)
00:00:00.211316   X2APIC - x2APIC support                                 = 1 (1)
00:00:00.211317   MOVBE - MOVBE instruction                               = 1 (1)
00:00:00.211318   POPCNT - POPCNT instruction                             = 1 (1)
00:00:00.211318   TSCDEADL - Time Stamp Counter Deadline                  = 0 (1)
00:00:00.211319   AES - AES instructions                                  = 1 (1)
00:00:00.211319   XSAVE - XSAVE instruction                               = 1 (1)
00:00:00.211320   OSXSAVE - OSXSAVE instruction                           = 0 (1)
00:00:00.211321   AVX - AVX support                                       = 1 (1)
00:00:00.211321   F16C - 16-bit floating point conversion instructions    = 0 (1)
00:00:00.211322   RDRAND - RDRAND instruction                             = 1 (1)
00:00:00.211322   HVP - Hypervisor Present (we're a guest)                = 1 (0)
00:00:00.211323 Structured Extended Feature Flags Enumeration (leaf 7):
00:00:00.211324   Mnemonic - Description                                  = guest (host)
00:00:00.211324   FSGSBASE - RDFSBASE/RDGSBASE/WRFSBASE/WRGSBASE instr.   = 1 (1)
00:00:00.211324   TSCADJUST - Supports MSR_IA32_TSC_ADJUST                = 0 (1)
00:00:00.211325   SGX - Supports Software Guard Extensions                = 0 (1)
00:00:00.211325   BMI1 - Advanced Bit Manipulation extension 1            = 0 (1)
00:00:00.211326   HLE - Hardware Lock Elision                             = 0 (1)
00:00:00.211327   AVX2 - Advanced Vector Extensions 2                     = 1 (1)
00:00:00.211327   FDP_EXCPTN_ONLY - FPU DP only updated on exceptions     = 0 (0)
00:00:00.211328   SMEP - Supervisor Mode Execution Prevention             = 0 (1)
00:00:00.211328   BMI2 - Advanced Bit Manipulation extension 2            = 0 (1)
00:00:00.211329   ERMS - Enhanced REP MOVSB/STOSB instructions            = 0 (1)
00:00:00.211329   INVPCID - INVPCID instruction                           = 1 (1)
00:00:00.211330   RTM - Restricted Transactional Memory                   = 0 (1)
00:00:00.211330   PQM - Platform Quality of Service Monitoring            = 0 (0)
00:00:00.211331   DEPFPU_CS_DS - Deprecates FPU CS, FPU DS values if set  = 1 (1)
00:00:00.211331   MPE - Intel Memory Protection Extensions                = 0 (1)
00:00:00.211332   PQE - Platform Quality of Service Enforcement           = 0 (0)
00:00:00.211332   AVX512F - AVX512 Foundation instructions                = 0 (0)
00:00:00.211333   RDSEED - RDSEED instruction                             = 1 (1)
00:00:00.211334   ADX - ADCX/ADOX instructions                            = 0 (1)
00:00:00.211334   SMAP - Supervisor Mode Access Prevention                = 0 (1)
00:00:00.211335   CLFLUSHOPT - CLFLUSHOPT (Cache Line Flush) instruction  = 1 (1)
00:00:00.211335   INTEL_PT - Intel Processor Trace                        = 0 (1)
00:00:00.211336   AVX512PF - AVX512 Prefetch instructions                 = 0 (0)
00:00:00.211336   AVX512ER - AVX512 Exponential & Reciprocal instructions = 0 (0)
00:00:00.211337   AVX512CD - AVX512 Conflict Detection instructions       = 0 (0)
00:00:00.211337   SHA - Secure Hash Algorithm extensions                  = 0 (0)
00:00:00.211338   PREFETCHWT1 - PREFETCHWT1 instruction                   = 0 (0)
00:00:00.211339   UMIP - User mode insturction prevention                 = 0 (0)
00:00:00.211339   PKU - Protection Key for Usermode pages                 = 0 (0)
00:00:00.211340   OSPKE - CR4.PKU mirror                                  = 0 (0)
00:00:00.211341   MAWAU - Value used by BNDLDX & BNDSTX                   = 0x0 (0x0)
00:00:00.211341   RDPID - Read processor ID support                       = 0 (0)
00:00:00.211342   SGX_LC - Supports SGX Launch Configuration              = 0 (0)
00:00:00.211342   MD_CLEAR - Supports MDS related buffer clearing         = 1 (1)
00:00:00.211343   13 - Reserved                                           = 0 (1)
00:00:00.211344   IBRS_IBPB - IA32_SPEC_CTRL.IBRS and IA32_PRED_CMD.IBPB  = 1 (1)
00:00:00.211344   STIBP - Supports IA32_SPEC_CTRL.STIBP                   = 1 (1)
00:00:00.211345   FLUSH_CMD - Supports IA32_FLUSH_CMD                     = 1 (1)
00:00:00.211345   ARCHCAP - Supports IA32_ARCH_CAP                        = 0 (0)
00:00:00.211346   CORECAP - Supports IA32_CORE_CAP                        = 0 (0)
00:00:00.211347   SSBD - Supports IA32_SPEC_CTRL.SSBD                     = 0 (1)
00:00:00.211347 Processor Extended State Enumeration (leaf 0xd):
00:00:00.211348    XSAVE area cur/max size by XCR0, guest: 0x340/0x340
00:00:00.211348     XSAVE area cur/max size by XCR0, host: 0x440/0x440
00:00:00.211349                    Valid XCR0 bits, guest: 0x00000000`00000007 ( x87 SSE YMM_Hi128 )
00:00:00.211350                     Valid XCR0 bits, host: 0x00000000`0000001f ( x87 SSE YMM_Hi128 BNDREGS BNDCSR )
00:00:00.211352                     XSAVE features, guest:
00:00:00.211352                      XSAVE features, host: XSAVEOPT XSAVEC XGETBC1 XSAVES
00:00:00.211355       XSAVE area cur size XCR0|XSS, guest: 0x440
00:00:00.211355        XSAVE area cur size XCR0|XSS, host: 0x440
00:00:00.211356                Valid IA32_XSS bits, guest: 0x00000000`00000000
00:00:00.211356                 Valid IA32_XSS bits, host: 0x00000100`00000000 ( 40 )
00:00:00.211358   State #2, guest: off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.211359   State #2, host:  off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.211360   State #3, host:  off=0x03c0, cb=0x0040 IA32_XSS-bit -- BNDREGS
00:00:00.211361   State #4, host:  off=0x0400, cb=0x0040 IA32_XSS-bit -- BNDCSR
00:00:00.211362   State #8, host:  off=0x0000, cb=0x0080 XCR0-bit -- 8
00:00:00.211369          Unknown CPUID Leaves
00:00:00.211369      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211370 Gst: 00000014/0001  00000000 00000000 00000000 00000000
00:00:00.211370 Hst:                02490002 003f3fff 00000000 00000000
00:00:00.211371 Gst: 00000014/0002  00000000 00000000 00000000 00000000
00:00:00.211372 Hst:                00000000 00000000 00000000 00000000
00:00:00.211372 Gst: 00000015/0000  00000000 00000000 00000000 00000000
00:00:00.211373 Hst:                00000002 0000012c 00000000 00000000
00:00:00.211373 Gst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:00.211374 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211375          Raw Hypervisor CPUID Leaves
00:00:00.211375      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211376 Gst: 40000000/0000  40000001 4b4d564b 564b4d56 0000004d
00:00:00.211376 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211377 Gst: 40000001/0000  01000089 00000000 00000000 00000000
00:00:00.211378 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211378          Raw Extended CPUID Leaves
00:00:00.211379      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211379 Gst: 80000000/0000  80000008 00000000 00000000 00000000
00:00:00.211380 Hst:                80000008 00000000 00000000 00000000
00:00:00.211380 Gst: 80000001/0000  00000000 00000000 00000121 28100800
00:00:00.211381 Hst:                00000000 00000000 00000121 2c100800
00:00:00.211382 Gst: 80000002/0000  65746e49 2952286c 726f4320 4d542865
00:00:00.211382 Hst:                65746e49 2952286c 726f4320 4d542865
00:00:00.211384 Gst: 80000003/0000  37692029 3037372d 50432030 20402055
00:00:00.211384 Hst:                37692029 3037372d 50432030 20402055
00:00:00.211385 Gst: 80000004/0000  30362e33 007a4847 00000000 00000000
00:00:00.211386 Hst:                30362e33 007a4847 00000000 00000000
00:00:00.211387 Gst: 80000005/0000  00000000 00000000 00000000 00000000
00:00:00.211387 Hst:                00000000 00000000 00000000 00000000
00:00:00.211387 Gst: 80000006/0000  00000000 00000000 01006040 00000000
00:00:00.211388 Hst:                00000000 00000000 01006040 00000000
00:00:00.211389 Gst: 80000007/0000  00000000 00000000 00000000 00000100
00:00:00.211389 Hst:                00000000 00000000 00000000 00000100
00:00:00.211390 Gst: 80000008/0000  00003027 00000000 00000000 00000000
00:00:00.211390 Hst:                00003027 00000000 00000000 00000000
00:00:00.211391 Ext Name:                        
00:00:00.211391 Ext Supports:                    0x80000000-0x80000008
00:00:00.211392 Family:                          0  	Extended: 0 	Effective: 0
00:00:00.211392 Model:                           0  	Extended: 0 	Effective: 0
00:00:00.211393 Stepping:                        0
00:00:00.211393 Brand ID:                        0x000
00:00:00.211393 Ext Features
00:00:00.211393   Mnemonic - Description                                  = guest (host)
00:00:00.211394   FPU - x87 FPU on Chip                                   = 0 (0)
00:00:00.211395   VME - Virtual 8086 Mode Enhancements                    = 0 (0)
00:00:00.211395   DE - Debugging extensions                               = 0 (0)
00:00:00.211396   PSE - Page Size Extension                               = 0 (0)
00:00:00.211396   TSC - Time Stamp Counter                                = 0 (0)
00:00:00.211397   MSR - K86 Model Specific Registers                      = 0 (0)
00:00:00.211398   PAE - Physical Address Extension                        = 0 (0)
00:00:00.211398   MCE - Machine Check Exception                           = 0 (0)
00:00:00.211399   CX8 - CMPXCHG8B instruction                             = 0 (0)
00:00:00.211399   APIC - APIC On-Chip                                     = 0 (0)
00:00:00.211400   SEP - SYSCALL/SYSRET                                    = 1 (1)
00:00:00.211401   MTRR - Memory Type Range Registers                      = 0 (0)
00:00:00.211401   PGE - PTE Global Bit                                    = 0 (0)
00:00:00.211402   MCA - Machine Check Architecture                        = 0 (0)
00:00:00.211402   CMOV - Conditional Move instructions                    = 0 (0)
00:00:00.211403   PAT - Page Attribute Table                              = 0 (0)
00:00:00.211404   PSE-36 - 36-bit Page Size Extension                     = 0 (0)
00:00:00.211404   NX - No-Execute/Execute-Disable                         = 1 (1)
00:00:00.211405   AXMMX - AMD Extensions to MMX instructions              = 0 (0)
00:00:00.211405   MMX - Intel MMX Technology                              = 0 (0)
00:00:00.211406   FXSR - FXSAVE and FXRSTOR Instructions                  = 0 (0)
00:00:00.211406   FFXSR - AMD fast FXSAVE and FXRSTOR instructions        = 0 (0)
00:00:00.211407   Page1GB - 1 GB large page                               = 0 (1)
00:00:00.211408   RDTSCP - RDTSCP instruction                             = 1 (1)
00:00:00.211408   LM - AMD64 Long Mode                                    = 1 (1)
00:00:00.211409   3DNOWEXT - AMD Extensions to 3DNow                      = 0 (0)
00:00:00.211409   3DNOW - AMD 3DNow                                       = 0 (0)
00:00:00.211410   LahfSahf - LAHF/SAHF support in 64-bit mode             = 1 (1)
00:00:00.211411   CmpLegacy - Core multi-processing legacy mode           = 0 (0)
00:00:00.211411   SVM - AMD Secure Virtual Machine extensions             = 0 (0)
00:00:00.211412   EXTAPIC - AMD Extended APIC registers                   = 0 (0)
00:00:00.211412   CR8L - AMD LOCK MOV CR0 means MOV CR8                   = 0 (0)
00:00:00.211413   ABM - AMD Advanced Bit Manipulation                     = 1 (1)
00:00:00.211413   SSE4A - SSE4A instructions                              = 0 (0)
00:00:00.211414   MISALIGNSSE - AMD Misaligned SSE mode                   = 0 (0)
00:00:00.211415   3DNOWPRF - AMD PREFETCH and PREFETCHW instructions      = 1 (1)
00:00:00.211415   OSVW - AMD OS Visible Workaround                        = 0 (0)
00:00:00.211416   IBS - Instruct Based Sampling                           = 0 (0)
00:00:00.211416   XOP - Extended Operation support                        = 0 (0)
00:00:00.211417   SKINIT - SKINIT, STGI, and DEV support                  = 0 (0)
00:00:00.211417   WDT - AMD Watchdog Timer support                        = 0 (0)
00:00:00.211418   LWP - Lightweight Profiling support                     = 0 (0)
00:00:00.211419   FMA4 - Four operand FMA instruction support             = 0 (0)
00:00:00.211419   NodeId - NodeId in MSR C001_100C                        = 0 (0)
00:00:00.211421   TBM - Trailing Bit Manipulation instructions            = 0 (0)
00:00:00.211421   TOPOEXT - Topology Extensions                           = 0 (0)
00:00:00.211422   PRFEXTCORE - Performance Counter Extensions support     = 0 (0)
00:00:00.211423   PRFEXTNB - NB Performance Counter Extensions support    = 0 (0)
00:00:00.211423   DATABPEXT - Data-access Breakpoint Extension            = 0 (0)
00:00:00.211424   PERFTSC - Performance Time Stamp Counter                = 0 (0)
00:00:00.211424   PCX_L2I - L2I/L3 Performance Counter Extensions         = 0 (0)
00:00:00.211425   MWAITX - MWAITX and MONITORX instructions               = 0 (0)
00:00:00.211426 Full Name:                       "Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz"
00:00:00.211426 TLB 2/4M Instr/Uni:              res0     0 entries
00:00:00.211428 TLB 2/4M Data:                   res0     0 entries
00:00:00.211428 TLB 4K Instr/Uni:                res0     0 entries
00:00:00.211429 TLB 4K Data:                     res0     0 entries
00:00:00.211429 L1 Instr Cache Line Size:        0 bytes
00:00:00.211429 L1 Instr Cache Lines Per Tag:    0
00:00:00.211430 L1 Instr Cache Associativity:    res0  
00:00:00.211430 L1 Instr Cache Size:             0 KB
00:00:00.211430 L1 Data Cache Line Size:         0 bytes
00:00:00.211430 L1 Data Cache Lines Per Tag:     0
00:00:00.211431 L1 Data Cache Associativity:     res0  
00:00:00.211431 L1 Data Cache Size:              0 KB
00:00:00.211431 L2 TLB 2/4M Instr/Uni:           off       0 entries
00:00:00.211432 L2 TLB 2/4M Data:                off       0 entries
00:00:00.211432 L2 TLB 4K Instr/Uni:             off       0 entries
00:00:00.211432 L2 TLB 4K Data:                  off       0 entries
00:00:00.211433 L2 Cache Line Size:              0 bytes
00:00:00.211433 L2 Cache Lines Per Tag:          0
00:00:00.211433 L2 Cache Associativity:          off   
00:00:00.211433 L2 Cache Size:                   0 KB
00:00:00.211434   TS - Temperature Sensor                                 = 0 (0)
00:00:00.211434   FID - Frequency ID control                              = 0 (0)
00:00:00.211435   VID - Voltage ID control                                = 0 (0)
00:00:00.211436   TscInvariant - Invariant Time Stamp Counter             = 1 (1)
00:00:00.211436   CBP - Core Performance Boost                            = 0 (0)
00:00:00.211437   EffFreqRO - Read-only Effective Frequency Interface     = 0 (0)
00:00:00.211438   ProcFdbkIf - Processor Feedback Interface               = 0 (0)
00:00:00.211438   ProcPwrRep - Core power reporting interface support     = 0 (0)
00:00:00.211439 Physical Address Width:          39 bits
00:00:00.211439 Virtual Address Width:           48 bits
00:00:00.211439 Guest Physical Address Width:    0 bits
00:00:00.211441 Physical Core Count:             1
00:00:00.211442 
00:00:00.211442 ******************** End of CPUID dump **********************
00:00:00.211442 *********************** VT-x features ***********************
00:00:00.211443 Nested hardware virtualization - VMX features
00:00:00.211444   Mnemonic - Description                                  = guest (host)
00:00:00.211444   VMX - Virtual-Machine Extensions                        = 0 (1)
00:00:00.211444   InsOutInfo - INS/OUTS instruction info.                 = 0 (1)
00:00:00.211445   ExtIntExit - External interrupt exiting                 = 0 (1)
00:00:00.211445   NmiExit - NMI exiting                                   = 0 (1)
00:00:00.211445   VirtNmi - Virtual NMIs                                  = 0 (1)
00:00:00.211446   PreemptTimer - VMX preemption timer                     = 0 (1)
00:00:00.211446   PostedInt - Posted interrupts                           = 0 (0)
00:00:00.211446   IntWindowExit - Interrupt-window exiting                = 0 (1)
00:00:00.211447   TscOffsetting - TSC offsetting                          = 0 (1)
00:00:00.211447   HltExit - HLT exiting                                   = 0 (1)
00:00:00.211447   InvlpgExit - INVLPG exiting                             = 0 (1)
00:00:00.211448   MwaitExit - MWAIT exiting                               = 0 (1)
00:00:00.211450   RdpmcExit - RDPMC exiting                               = 0 (1)
00:00:00.211450   RdtscExit - RDTSC exiting                               = 0 (1)
00:00:00.211451   Cr3LoadExit - CR3-load exiting                          = 0 (1)
00:00:00.211451   Cr3StoreExit - CR3-store exiting                        = 0 (1)
00:00:00.211451   Cr8LoadExit  - CR8-load exiting                         = 0 (1)
00:00:00.211452   Cr8StoreExit - CR8-store exiting                        = 0 (1)
00:00:00.211452   UseTprShadow - Use TPR shadow                           = 0 (1)
00:00:00.211452   NmiWindowExit - NMI-window exiting                      = 0 (1)
00:00:00.211453   MovDRxExit - Mov-DR exiting                             = 0 (1)
00:00:00.211453   UncondIoExit - Unconditional I/O exiting                = 0 (1)
00:00:00.211453   UseIoBitmaps - Use I/O bitmaps                          = 0 (1)
00:00:00.211456   MonitorTrapFlag - Monitor Trap Flag                     = 0 (1)
00:00:00.211456   UseMsrBitmaps - MSR bitmaps                             = 0 (1)
00:00:00.211456   MonitorExit - MONITOR exiting                           = 0 (1)
00:00:00.211457   PauseExit - PAUSE exiting                               = 0 (1)
00:00:00.211457   SecondaryExecCtl - Activate secondary controls          = 0 (1)
00:00:00.211457   VirtApic - Virtualize-APIC accesses                     = 0 (1)
00:00:00.211458   Ept - Extended Page Tables                              = 0 (1)
00:00:00.211458   DescTableExit - Descriptor-table exiting                = 0 (1)
00:00:00.211458   Rdtscp - Enable RDTSCP                                  = 0 (1)
00:00:00.211459   VirtX2ApicMode - Virtualize-x2APIC mode                 = 0 (1)
00:00:00.211459   Vpid - Enable VPID                                      = 0 (1)
00:00:00.211459   WbinvdExit - WBINVD exiting                             = 0 (1)
00:00:00.211460   UnrestrictedGuest - Unrestricted guest                  = 0 (1)
00:00:00.211460   ApicRegVirt - APIC-register virtualization              = 0 (0)
00:00:00.211460   VirtIntDelivery - Virtual-interrupt delivery            = 0 (0)
00:00:00.211461   PauseLoopExit - PAUSE-loop exiting                      = 0 (1)
00:00:00.211461   RdrandExit - RDRAND exiting                             = 0 (1)
00:00:00.211461   Invpcid - Enable INVPCID                                = 0 (1)
00:00:00.211462   VmFuncs - Enable VM Functions                           = 0 (1)
00:00:00.211462   VmcsShadowing - VMCS shadowing                          = 0 (1)
00:00:00.211462   RdseedExiting - RDSEED exiting                          = 0 (1)
00:00:00.211463   PML - Page-Modification Log (PML)                       = 0 (1)
00:00:00.211463   EptVe - EPT violations can cause #VE                    = 0 (1)
00:00:00.211463   XsavesXRstors - Enable XSAVES/XRSTORS                   = 0 (1)
00:00:00.211464   EntryLoadDebugCtls - Load debug controls on VM-entry    = 0 (1)
00:00:00.211464   Ia32eModeGuest - IA-32e mode guest                      = 0 (1)
00:00:00.211464   EntryLoadEferMsr - Load IA32_EFER MSR on VM-entry       = 0 (1)
00:00:00.211464   EntryLoadPatMsr - Load IA32_PAT MSR on VM-entry         = 0 (1)
00:00:00.211465   ExitSaveDebugCtls - Save debug controls on VM-exit      = 0 (1)
00:00:00.211465   HostAddrSpaceSize - Host address-space size             = 0 (1)
00:00:00.211465   ExitAckExtInt - Acknowledge interrupt on VM-exit        = 0 (1)
00:00:00.211467   ExitSavePatMsr - Save IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.211467   ExitLoadPatMsr - Load IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.211468   ExitSaveEferMsr - Save IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.211468   ExitLoadEferMsr - Load IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.211468   SavePreemptTimer - Save VMX-preemption timer            = 0 (1)
00:00:00.211469   ExitSaveEferLma - Save IA32_EFER.LMA on VM-exit         = 0 (1)
00:00:00.211469   IntelPt - Intel PT (Processor Trace) in VMX operation   = 0 (1)
00:00:00.211469   VmwriteAll - VMWRITE to any supported VMCS field        = 0 (1)
00:00:00.211470   EntryInjectSoftInt - Inject softint. with 0-len instr.  = 0 (1)
00:00:00.211470 
00:00:00.211470 ******************* End of VT-x features ********************
00:00:00.211493 VMEmt: Halt method global1 (5)
00:00:00.211597 VMEmt: HaltedGlobal1 config: cNsSpinBlockThresholdCfg=50000
00:00:00.211645 Changing the VM state from 'CREATING' to 'CREATED'
00:00:00.213499 SharedFolders host service: Adding host mapping
00:00:00.213518     Host path 'C:/Users/<USER>/Documents/leidian9/Applications', map name 'Applications', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.213775 SharedFolders host service: Adding host mapping
00:00:00.213785     Host path 'C:\Users\<USER>\AppData\Roaming\leidian9\android_bug', map name 'Bug', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214013 SharedFolders host service: Adding host mapping
00:00:00.214022     Host path 'C:/Users/<USER>/Documents/leidian9/Misc', map name 'Misc', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214266 SharedFolders host service: Adding host mapping
00:00:00.214276     Host path 'C:/Users/<USER>/Documents/leidian9/Pictures', map name 'Pictures', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214421 Changing the VM state from 'CREATED' to 'POWERING_ON'
00:00:00.214490 virtioCoreVirtqAvailBufCount: Driver not ready or queue controlq not enabled
00:00:00.214513 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<0> not enabled
00:00:00.214530 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<1> not enabled
00:00:00.214549 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<2> not enabled
00:00:00.214569 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<3> not enabled
00:00:00.214637 Changing the VM state from 'POWERING_ON' to 'RUNNING'
00:00:00.214656 Console: Machine state changed to 'Running'
00:00:00.218391 VMMDev: Guest Log: BIOS: VirtualBox 6.1.34
00:00:00.218493 PCI: Setting up resources and interrupts
00:00:00.218623 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.243105 VMMDev: Guest Log: CPUID EDX: 0x178bfbff
00:00:00.243920 VMMDev: Guest Log: BIOS: No PCI IDE controller, not probing IDE
00:00:00.246659 VMMDev: Guest Log: BIOS: SCSI 0-ID#0: LCHS=326/255/63 0x0000000000503f2a sectors
00:00:00.247180 VMMDev: Guest Log: BIOS: SCSI 1-ID#1: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.247720 VMMDev: Guest Log: BIOS: SCSI 2-ID#2: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.249329 PIT: mode=2 count=0x48d3 (18643) - 64.00 Hz (ch=0)
00:00:00.249386 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.249628 VMMDev: Guest Log: BIOS: Boot : bseqnr=1, bootseq=0002
00:00:00.250163 VMMDev: Guest Log: BIOS: Booting from Hard Disk...
00:00:00.252960 VMMDev: Guest Log: int13_harddisk_ext: function 41, unmapped device for ELDL=83
00:00:00.253480 VMMDev: Guest Log: int13_harddisk: function 08, unmapped device for ELDL=83
00:00:00.462455 VMMDev: Guest Log: BIOS: KBD: unsupported int 16h function 03
00:00:00.462858 VMMDev: Guest Log: BIOS: AX=0305 BX=0000 CX=0000 DX=0000 
00:00:00.555769 VBoxHeadless: starting event loop
00:00:00.679505 GIM: KVM: VCPU  0: Enabled system-time struct. at 0x000000019ff7c000 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x63aee321 uVirtNanoTS=0x1bb093c1 TscKHz=3600000
00:00:00.679577 TM: Switching TSC mode from 'VirtTscEmulated' to 'RealTscOffset'
00:00:00.737625 GIM: KVM: Enabled wall-clock struct. at 0x00000000010c32a8 - u32Sec=1754320313 u32Nano=220869559 uVersion=2
00:00:00.761371 PIT: mode=2 count=0xf89 (3977) - 300.02 Hz (ch=0)
00:00:00.771203 APIC0: Switched mode to x2APIC
00:00:00.877885 PIT: mode=0 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.909123 APIC1: Switched mode to x2APIC
00:00:00.909139 GIM: KVM: VCPU  1: Enabled system-time struct. at 0x000000019ff7c040 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x94f54a0e uVirtNanoTS=0x29609392 TscKHz=3600000
00:00:00.923549 APIC2: Switched mode to x2APIC
00:00:00.923551 GIM: KVM: VCPU  2: Enabled system-time struct. at 0x000000019ff7c080 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x980d618d uVirtNanoTS=0x2a3c9a14 TscKHz=3600000
00:00:00.938158 APIC3: Switched mode to x2APIC
00:00:00.938176 GIM: KVM: VCPU  3: Enabled system-time struct. at 0x000000019ff7c0c0 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x9b301fe0 uVirtNanoTS=0x2b1b9609 TscKHz=3600000
00:00:02.293041 VMMDev: Guest Additions information report: Version 6.1.36 r152435 '6.1.36'
00:00:02.293123 VMMDev: Guest Additions information report: Interface = 0x00010004 osType = 0x00053100 (Linux >= 2.6, 64-bit)
00:00:02.293326 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.293646 VMMDev: vmmDevReqHandler_HeartbeatConfigure: No change (fHeartbeatActive=false)
00:00:02.293682 VMMDev: Heartbeat flatline timer set to trigger after 4 000 000 000 ns
00:00:02.293735 VMMDev: Guest Log: vgdrvHeartbeatInit: Setting up heartbeat to trigger every 2000 milliseconds
00:00:02.294735 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.294903 VMMDev: Guest Log: vboxguest: Successfully loaded version 6.1.36 r152435
00:00:02.295198 VMMDev: Guest Log: vboxguest: misc device minor 53, IRQ 20, I/O port d020, MMIO at 00000000f0000000 (size 0x400000)
00:00:02.297715 VMMDev: Guest Log: vboxsf: g_fHostFeatures=0x8000000f g_fSfFeatures=0x1 g_uSfLastFunction=29
00:00:02.298648 VMMDev: Guest Log: vboxsf: Successfully loaded version 6.1.36 r152435 on 4.4.146 SMP preempt mod_unload modversions  (LINUX_VERSION_CODE=0x40492)
00:00:04.799493 NAT: IPv6 not supported
00:00:12.983596 denglibo ld_get_start_addr return 15
00:00:12.983629 NAT: DHCP offered IP address ***********
00:00:13.089608 denglibo ld_get_start_addr return 15
00:00:13.089636 NAT: DHCP offered IP address ***********
00:00:13.681732 NAT: Old socket recv size: 64KB
00:00:13.681781 NAT: Old socket send size: 64KB
10:59:45.668068 VMMDev: vmmDevHeartbeatFlatlinedTimer: Guest seems to be unresponsive. Last heartbeat received 9 seconds ago
10:59:45.668271 VMMDev: GuestHeartBeat: Guest is alive (gone 9 317 623 815 ns)
11:05:35.696453 TM: Giving up catch-up attempt at a 60 003 290 797 ns lag; new total: 60 003 290 797 ns
11:08:22.714758 VMMDev: vmmDevHeartbeatFlatlinedTimer: Guest seems to be unresponsive. Last heartbeat received 4 seconds ago
11:08:22.744889 VMMDev: GuestHeartBeat: Guest is alive (gone 4 070 084 738 ns)
11:17:17.358557 TM: Giving up catch-up attempt at a 60 005 843 026 ns lag; new total: 120 009 133 823 ns
11:22:18.682405 TM: Giving up catch-up attempt at a 60 001 631 147 ns lag; new total: 180 010 764 970 ns
11:27:52.478776 TM: Giving up catch-up attempt at a 60 015 396 589 ns lag; new total: 240 026 161 559 ns
11:35:40.795895 TM: Giving up catch-up attempt at a 60 058 414 462 ns lag; new total: 300 084 576 021 ns
11:42:26.677112 TM: Giving up catch-up attempt at a 60 010 979 458 ns lag; new total: 360 095 555 479 ns
11:47:12.156918 TM: Giving up catch-up attempt at a 60 005 926 827 ns lag; new total: 420 101 482 306 ns
11:54:52.629431 TM: Giving up catch-up attempt at a 60 001 814 659 ns lag; new total: 480 103 296 965 ns
12:00:13.188481 denglibo ld_get_start_addr return 15
12:00:13.188518 NAT: DHCP offered IP address ***********
12:14:51.757134 TM: Giving up catch-up attempt at a 60 025 185 047 ns lag; new total: 540 128 482 012 ns
12:31:49.872655 TM: Giving up catch-up attempt at a 60 036 333 425 ns lag; new total: 600 164 815 437 ns
12:53:29.640488 TM: Giving up catch-up attempt at a 60 023 079 608 ns lag; new total: 660 187 895 045 ns
13:30:11.646249 TM: Giving up catch-up attempt at a 60 004 236 705 ns lag; new total: 720 192 131 750 ns
14:31:17.242874 Console: Machine state changed to 'Stopping'
14:31:17.246107 Console::powerDown(): A request to power off the VM has been issued (mMachineState=Stopping, InUninit=0)
14:31:17.247842 Changing the VM state from 'RUNNING' to 'POWERING_OFF'
14:31:17.247861 ****************** Guest state at power off for VCpu 3 ******************
14:31:17.248272 Guest CPUM (VCPU 3) state: 
14:31:17.248534 rax=0000000000000003 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
14:31:17.248539 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
14:31:17.248541 r10=0000000100ededd7 r11=0000000000000002 r12=0000000000000000 r13=0000000000000000
14:31:17.248542 r14=ffff880198970000 r15=ffff880198974000
14:31:17.248543 rip=ffffffff80239162 rsp=ffff880198973ef8 rbp=ffff880198974000 iopl=0         nv up ei pl zr na pe nc
14:31:17.248546 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
14:31:17.248547 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.248548 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.248548 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.248549 gs={0000 base=ffff88019fd80000 limit=ffffffff flags=0001c000}
14:31:17.248598 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
14:31:17.248599 cr0=0000000080050033 cr2=00007fff68e64000 cr3=00000000dae0a000 cr4=00000000000606b0
14:31:17.248601 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
14:31:17.248602 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
14:31:17.248603 gdtr=ffff88019fd8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
14:31:17.248605 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
14:31:17.248606 tr  ={0040 base=ffff88019fd84840 limit=00002087 flags=0000008b}
14:31:17.248607 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
14:31:17.248609 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
14:31:17.248612 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
14:31:17.248614 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
14:31:17.248615 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248617 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248619 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248620 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248622 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248623 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248625 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248626 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248628 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248629 YMM1 =00000000'00000000'00000000'00000000'6c6c6163'206f7420'64656c69'61662065
14:31:17.248631 YMM2 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248633 YMM3 =00000000'00000000'00000000'00000000'00000000'ffffffff'00000000'ffffffff
14:31:17.248634 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248635 YMM5 =00000000'00000000'00000000'00000000'61502e6d'702e7265'76726573'2e64696f
14:31:17.248637 YMM6 =00000000'00000000'00000000'00000000'76726553'72656761'6e614d65'67616b63
14:31:17.248639 YMM7 =00000000'00000000'00000000'00000000'6d726550'6469556b'63656863'2e656369
14:31:17.248640 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
14:31:17.248642 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
14:31:17.248643 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248645 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248646 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248647 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248649 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248650 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248652 EFER         =0000000000000d01
14:31:17.248652 PAT          =0007040600070406
14:31:17.248653 STAR         =0023001000000000
14:31:17.248653 CSTAR        =ffffffff809e4e70
14:31:17.248654 LSTAR        =ffffffff809e3690
14:31:17.248654 SFMASK       =0000000000047700
14:31:17.248655 KERNELGSBASE =0000000000000000
14:31:17.248657 ***
14:31:17.248663 VCPU[3] hardware virtualization state:
14:31:17.248664 fLocalForcedActions          = 0x0
14:31:17.248664 No/inactive hwvirt state
14:31:17.248666 ***
14:31:17.248681 Guest paging mode (VCPU #3):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
14:31:17.248683 Shadow paging mode (VCPU #3): EPT
14:31:17.248873 Host paging mode:             AMD64+NX
14:31:17.248875 ***
14:31:17.248875 ************** End of Guest state at power off for VCpu 3 ***************
14:31:17.248906 ****************** Guest state at power off for VCpu 2 ******************
14:31:17.248910 Guest CPUM (VCPU 2) state: 
14:31:17.248924 rax=000000000000000a rbx=00007fff4da5b5e0 rcx=0000000000000005 rdx=0000000000000005
14:31:17.248926 rsi=00000000000000fa rdi=0000000000000007 r8 =00007fff4d65ca80 r9 =0000000000000000
14:31:17.248927 r10=00007ffff5153808 r11=000000007fffffff r12=00007fff579491b0 r13=0000000000000006
14:31:17.248929 r14=00007fff4e9adfc0 r15=0000000000000000
14:31:17.248930 rip=00007ffff52b0240 rsp=00007fff57948fc8 rbp=00000000000000fa iopl=0         nv up ei pl zr na pe nc
14:31:17.248931 cs={0033 base=0000000000000000 limit=ffffffff flags=0000a0fb}
14:31:17.248932 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.248933 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.248934 fs={0000 base=00007fff5794a588 limit=ffffffff flags=0001c000}
14:31:17.248934 gs={0000 base=00007fff728d8000 limit=ffffffff flags=0001c000}
14:31:17.248935 ss={002b base=0000000000000000 limit=ffffffff flags=0000c0f3}
14:31:17.248936 cr0=0000000080050033 cr2=0000000013170000 cr3=00000001952e4000 cr4=00000000000606b0
14:31:17.248937 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
14:31:17.248938 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
14:31:17.248939 gdtr=ffff88019fd0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
14:31:17.248941 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
14:31:17.248941 tr  ={0040 base=ffff88019fd04840 limit=00002087 flags=0000008b}
14:31:17.248942 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
14:31:17.248943 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
14:31:17.248944 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa1 MXCSR_MASK=0000ffff
14:31:17.248945 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
14:31:17.248946 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248948 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248950 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248951 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248952 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248953 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248954 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248955 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.248956 YMM0 =00000000'00000000'00000000'00000000'00000000'0000842d'00000000'41035ddf
14:31:17.248957 YMM1 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000004
14:31:17.248958 YMM2 =00000000'00000000'00000000'00000000'00000000'00008431'00000000'41035de3
14:31:17.248959 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248960 YMM4 =00000000'00000000'00000000'00000000'00002030'0000202a'0000200b'00001681
14:31:17.248961 YMM5 =00000000'00000000'00000000'00000000'00002030'0000202f'0000202a'00002028
14:31:17.248962 YMM6 =00000000'00000000'00000000'00000000'000000a0'00000085'00000020'00000009
14:31:17.248963 YMM7 =00000000'00000000'00000000'00000000'0000202f'00002028'00002000'00001680
14:31:17.248964 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
14:31:17.248965 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
14:31:17.248966 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'40528000'00000000
14:31:17.248967 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'40740000'00000000
14:31:17.248968 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248969 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248970 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248971 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.248972 EFER         =0000000000000d01
14:31:17.248972 PAT          =0007040600070406
14:31:17.248973 STAR         =0023001000000000
14:31:17.248973 CSTAR        =ffffffff809e4e70
14:31:17.248974 LSTAR        =ffffffff809e3690
14:31:17.248974 SFMASK       =0000000000047700
14:31:17.248974 KERNELGSBASE =ffff88019fd00000
14:31:17.248976 ***
14:31:17.248979 VCPU[2] hardware virtualization state:
14:31:17.248979 fLocalForcedActions          = 0x0
14:31:17.248980 No/inactive hwvirt state
14:31:17.248981 ***
14:31:17.248983 Guest paging mode (VCPU #2):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
14:31:17.248984 Shadow paging mode (VCPU #2): EPT
14:31:17.248985 Host paging mode:             AMD64+NX
14:31:17.248986 ***
14:31:17.248986 ************** End of Guest state at power off for VCpu 2 ***************
14:31:17.249008 ****************** Guest state at power off for VCpu 1 ******************
14:31:17.249011 Guest CPUM (VCPU 1) state: 
14:31:17.249012 rax=0000000000000001 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
14:31:17.249014 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
14:31:17.249015 r10=00000000017a5fc9 r11=00007fff72944000 r12=0000000000000000 r13=0000000000000000
14:31:17.249016 r14=ffff880198960000 r15=ffff880198964000
14:31:17.249017 rip=ffffffff80239162 rsp=ffff880198963ef8 rbp=ffff880198964000 iopl=0         nv up ei pl zr na pe nc
14:31:17.249019 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
14:31:17.249020 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249021 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249021 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249022 gs={0000 base=ffff88019fc80000 limit=ffffffff flags=0001c000}
14:31:17.249023 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
14:31:17.249024 cr0=0000000080050033 cr2=000000001310d000 cr3=0000000000e08000 cr4=00000000000606b0
14:31:17.249025 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
14:31:17.249026 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
14:31:17.249027 gdtr=ffff88019fc8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
14:31:17.249028 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
14:31:17.249029 tr  ={0040 base=ffff88019fc84840 limit=00002087 flags=0000008b}
14:31:17.249030 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
14:31:17.249031 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
14:31:17.249032 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa3 MXCSR_MASK=0000ffff
14:31:17.249033 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
14:31:17.249034 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249035 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249037 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249038 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249039 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249040 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249041 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249042 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249043 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249044 YMM1 =00000000'00000000'00000000'00000000'535f4946'49575f45'474e4148'432e6e6f
14:31:17.249045 YMM2 =00000000'00000000'00000000'00000000'45454545'45454545'535f4946'49575f45
14:31:17.249047 YMM3 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249048 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249048 YMM5 =00000000'00000000'00000000'00000000'00000000'00000000'ffffffff'ffffff00
14:31:17.249049 YMM6 =00000000'00000000'00000000'00000000'00000000'00000000'436e6f69'7461636f
14:31:17.249050 YMM7 =00000000'00000000'00000000'00000000'00000000'00000000'28646568'73696e69
14:31:17.249051 YMM8 =00000000'00000000'00000000'00000000'00000000'00004000'00000000'00004000
14:31:17.249052 YMM9 =00000000'00000000'00000000'00000000'00000000'00000004'00000000'00000004
14:31:17.249053 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'ebad807a
14:31:17.249054 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'ebad807b
14:31:17.249055 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249056 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249057 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249058 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249059 EFER         =0000000000000d01
14:31:17.249059 PAT          =0007040600070406
14:31:17.249060 STAR         =0023001000000000
14:31:17.249060 CSTAR        =ffffffff809e4e70
14:31:17.249061 LSTAR        =ffffffff809e3690
14:31:17.249062 SFMASK       =0000000000047700
14:31:17.249062 KERNELGSBASE =0000000000000000
14:31:17.249063 ***
14:31:17.249064 VCPU[1] hardware virtualization state:
14:31:17.249065 fLocalForcedActions          = 0x0
14:31:17.249065 No/inactive hwvirt state
14:31:17.249066 ***
14:31:17.249068 Guest paging mode (VCPU #1):  AMD64+NX (changed 3 times), A20 enabled (changed 0 times)
14:31:17.249069 Shadow paging mode (VCPU #1): EPT
14:31:17.249070 Host paging mode:             AMD64+NX
14:31:17.249071 ***
14:31:17.249071 ************** End of Guest state at power off for VCpu 1 ***************
14:31:17.249092 ****************** Guest state at power off for VCpu 0 ******************
14:31:17.249095 Guest CPUM (VCPU 0) state: 
14:31:17.249109 rax=0000000000000000 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
14:31:17.249111 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
14:31:17.249113 r10=0000000100ededd3 r11=0000000000000000 r12=0000000000000000 r13=0000000000000000
14:31:17.249114 r14=ffffffff80e00000 r15=ffffffff80e04000
14:31:17.249114 rip=ffffffff80239162 rsp=ffffffff80e03f08 rbp=ffffffff80e04000 iopl=0         nv up ei pl zr na pe nc
14:31:17.249116 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
14:31:17.249117 ds={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249118 es={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249119 fs={0000 base=0000000000000000 limit=ffffffff flags=0001c000}
14:31:17.249119 gs={0000 base=ffff88019fc00000 limit=ffffffff flags=0001c000}
14:31:17.249120 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
14:31:17.249121 cr0=0000000080050033 cr2=00000000131e1004 cr3=00000000ba9f4000 cr4=00000000000606b0
14:31:17.249122 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
14:31:17.249123 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000fffe0ff0 dr7=0000000000000400
14:31:17.249124 gdtr=ffff88019fc0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00200202
14:31:17.249125 ldtr={0000 base=00000000 limit=ffffffff flags=0001c000}
14:31:17.249126 tr  ={0040 base=ffff88019fc04840 limit=00002087 flags=0000008b}
14:31:17.249127 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
14:31:17.249128 xcr=0000000000000007 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000007)
14:31:17.249129 FCW=037f FSW=0020 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
14:31:17.249130 FPUIP=da4aeb05 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
14:31:17.249131 ST(0)=FPR0={ffff'00000000'f4892a0e} t0 -0.0000000000004102629902 * 2 ^ 16384 (*)
14:31:17.249133 ST(1)=FPR1={ffff'00000000'b1ec577f} t0 -0.0000000000002985056127 * 2 ^ 16384 (*)
14:31:17.249135 ST(2)=FPR2={ffff'b1ec577f'5d048580} t0 -1.0003597346406895224192 * 2 ^ 16384 (*)
14:31:17.249136 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249137 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249138 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249139 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
14:31:17.249140 ST(7)=FPR7={4000'80000000'00000000} t0 +1.0000000000000000000000 * 2 ^ 1 (*)
14:31:17.249141 YMM0 =00000000'00000000'00000000'00000000'00000000'00000000'00000010'00000031
14:31:17.249143 YMM1 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'0000009c
14:31:17.249144 YMM2 =00000000'00000000'00000000'00000000'0043002e'006e006f'00690073'00730069
14:31:17.249145 YMM3 =00000000'00000000'00000000'00000000'006d0072'00650070'002e0064'0069006f
14:31:17.249146 YMM4 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249147 YMM5 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249148 YMM6 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249148 YMM7 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'0000edcd
14:31:17.249149 YMM8 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249150 YMM9 =00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249151 YMM10=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249152 YMM11=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249153 YMM12=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249154 YMM13=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249154 YMM14=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249155 YMM15=00000000'00000000'00000000'00000000'00000000'00000000'00000000'00000000
14:31:17.249156 EFER         =0000000000000d01
14:31:17.249157 PAT          =0007040600070406
14:31:17.249157 STAR         =0023001000000000
14:31:17.249158 CSTAR        =ffffffff809e4e70
14:31:17.249158 LSTAR        =ffffffff809e3690
14:31:17.249159 SFMASK       =0000000000047700
14:31:17.249159 KERNELGSBASE =0000000000000000
14:31:17.249161 ***
14:31:17.249163 VCPU[0] hardware virtualization state:
14:31:17.249163 fLocalForcedActions          = 0x0
14:31:17.249164 No/inactive hwvirt state
14:31:17.249165 ***
14:31:17.249166 Guest paging mode (VCPU #0):  AMD64+NX (changed 3700 times), A20 enabled (changed 2 times)
14:31:17.249168 Shadow paging mode (VCPU #0): EPT
14:31:17.249168 Host paging mode:             AMD64+NX
14:31:17.249169 ***
14:31:17.249171 Active Timers (pVM=0000000003a10000)
14:31:17.249172 pTimerR3         offNext  offPrev  offSched Clock               Time             Expire HzHint State                     Description
14:31:17.249174 0000000003cc2210 00000000 00000000 00000000 Real           162472458          162473344      0 2-ACTIVE                  CPU Load Timer
14:31:17.249176 0000000003cc1d10 00000000 00000000 00000000 Virt      52277034339455     52279537098674      0 2-ACTIVE                  Heartbeat flatlined
14:31:17.249179 0000000003cbd6e0 00000080 00000000 00000000 VrSy      51506529988462     51506529988462    301 2-ACTIVE                  APIC Timer 1
14:31:17.249181 0000000003cbd760 00000080 ffffff80 00000000 VrSy      51506529988462     51506529988630    300 2-ACTIVE                  APIC Timer 2
14:31:17.249183 0000000003cbd7e0 fffffe80 ffffff80 00000000 VrSy      51506529988462     51506533321996    150 2-ACTIVE                  APIC Timer 3
14:31:17.249185 0000000003cbd660 000003f0 00000180 00000000 VrSy      51506529988462     51506536178757     78 2-ACTIVE                  APIC Timer 0
14:31:17.249187 0000000003cbda50 00004540 fffffc10 00000000 VrSy      51506529988462     51506990000000      0 2-ACTIVE                  MC146818 RTC (CMOS) - Second
14:31:17.249189 0000000003cc1f90 00000000 ffffbac0 00000000 VrSy      51506529988462     51594153370926      0 2-ACTIVE                  ACPI PM Timer
14:31:17.249192 ***
14:31:17.249193 Guest GDT (GCAddr=ffff88019fc0c000 limit=7f):
14:31:17.249200 0008 - 0000ffff 00cf9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 32-bit 
14:31:17.249201 0010 - 0000ffff 00af9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 16-bit 
14:31:17.249202 0018 - 0000ffff 00cf9300 - base=00000000 limit=ffffffff dpl=0 DataRW Accessed Present Page 32-bit 
14:31:17.249203 0020 - 0000ffff 00cffb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 32-bit 
14:31:17.249204 0028 - 0000ffff 00cff300 - base=00000000 limit=ffffffff dpl=3 DataRW Accessed Present Page 32-bit 
14:31:17.249205 0030 - 0000ffff 00affb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 16-bit 
14:31:17.249206 0040 - 48402087 9f008bc0 - base=9fc04840 limit=00002087 dpl=0 TSS32Busy Present 16-bit 
14:31:17.249208 0078 - 00000000 0040f500 - base=00000000 limit=00000000 dpl=3 DataDownRO Accessed Present 32-bit 
14:31:17.249209 ************** End of Guest state at power off ***************
14:31:17.353685 PDMR3PowerOff: after   104 ms, 1 loops: 1 async tasks - virtio-scsi/0
14:31:17.448864 PDMR3PowerOff: 199 635 887 ns run time
14:31:17.449483 Changing the VM state from 'POWERING_OFF' to 'OFF'
14:31:17.452117 Changing the VM state from 'OFF' to 'DESTROYING'
14:31:17.452204 ************************* Statistics *************************
14:31:17.452221 /CPUM/MSR-Totals/Reads            1624938 times
14:31:17.452230 /CPUM/MSR-Totals/ReadsRaisingGP         0 times
14:31:17.452238 /CPUM/MSR-Totals/ReadsUnknown           0 times
14:31:17.452246 /CPUM/MSR-Totals/Writes          100355248 times
14:31:17.452255 /CPUM/MSR-Totals/WritesRaisingGP        0 times
14:31:17.452263 /CPUM/MSR-Totals/WritesToIgnoredBits        4 times
14:31:17.452271 /CPUM/MSR-Totals/WritesUnknown          0 times
14:31:17.452282 /Devices/8237A/DmaRun                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.452332 /Devices/VMMDev/BalloonChunks           0 count
14:31:17.452344 /Devices/VMMDev/FastIrqAckR3            0 count
14:31:17.452352 /Devices/VMMDev/FastIrqAckRZ          143 count
14:31:17.452364 /Devices/VMMDev/HGCM-Guest/BudgetAvailable 535351424 bytes
14:31:17.452374 /Devices/VMMDev/HGCM-Guest/BudgetConfig 535351424 bytes
14:31:17.452383 /Devices/VMMDev/HGCM-Guest/cTotalMessages        0 count
14:31:17.452405 /Devices/VMMDev/HGCM-Guest/cbHeapTotal        0 bytes
14:31:17.452429 /Devices/VMMDev/HGCM-Legacy/BudgetAvailable 535351424 bytes
14:31:17.452437 /Devices/VMMDev/HGCM-Legacy/BudgetConfig 535351424 bytes
14:31:17.452444 /Devices/VMMDev/HGCM-Legacy/cTotalMessages        0 count
14:31:17.452452 /Devices/VMMDev/HGCM-Legacy/cbHeapTotal        0 bytes
14:31:17.452460 /Devices/VMMDev/HGCM-OtherDrv/BudgetAvailable 535351424 bytes
14:31:17.452467 /Devices/VMMDev/HGCM-OtherDrv/BudgetConfig 535351424 bytes
14:31:17.452475 /Devices/VMMDev/HGCM-OtherDrv/cTotalMessages      144 count
14:31:17.452483 /Devices/VMMDev/HGCM-OtherDrv/cbHeapTotal   350998 bytes
14:31:17.452490 /Devices/VMMDev/HGCM-Reserved1/BudgetAvailable 535351424 bytes
14:31:17.452498 /Devices/VMMDev/HGCM-Reserved1/BudgetConfig 535351424 bytes
14:31:17.452506 /Devices/VMMDev/HGCM-Reserved1/cTotalMessages        0 count
14:31:17.452526 /Devices/VMMDev/HGCM-Reserved1/cbHeapTotal        0 bytes
14:31:17.452549 /Devices/VMMDev/HGCM-Root/BudgetAvailable 535351424 bytes
14:31:17.452557 /Devices/VMMDev/HGCM-Root/BudgetConfig 535351424 bytes
14:31:17.452567 /Devices/VMMDev/HGCM-Root/cTotalMessages        0 count
14:31:17.452575 /Devices/VMMDev/HGCM-Root/cbHeapTotal        0 bytes
14:31:17.452582 /Devices/VMMDev/HGCM-System/BudgetAvailable 535351424 bytes
14:31:17.452590 /Devices/VMMDev/HGCM-System/BudgetConfig 535351424 bytes
14:31:17.452598 /Devices/VMMDev/HGCM-System/cTotalMessages        0 count
14:31:17.452605 /Devices/VMMDev/HGCM-System/cbHeapTotal        0 bytes
14:31:17.452612 /Devices/VMMDev/HGCM-User/BudgetAvailable 535351424 bytes
14:31:17.452620 /Devices/VMMDev/HGCM-User/BudgetConfig 535351424 bytes
14:31:17.452628 /Devices/VMMDev/HGCM-User/cTotalMessages        0 count
14:31:17.452635 /Devices/VMMDev/HGCM-User/cbHeapTotal        0 bytes
14:31:17.452643 /Devices/VMMDev/HGCM-VBoxGuest/BudgetAvailable 535351424 bytes
14:31:17.452663 /Devices/VMMDev/HGCM-VBoxGuest/BudgetConfig 535351424 bytes
14:31:17.452671 /Devices/VMMDev/HGCM-VBoxGuest/cTotalMessages        0 count
14:31:17.452693 /Devices/VMMDev/HGCM-VBoxGuest/cbHeapTotal        0 bytes
14:31:17.452700 /Devices/VMMDev/LargeReqBufAllocs        0 count
14:31:17.452708 /Devices/VMMDev/SlowIrqAck              0 count
14:31:17.452716 /Devices/mc146818/Irq                   0 times
14:31:17.452724 /Devices/mc146818/TimerCB               0 times
14:31:17.452732 /Devices/virtio-net#0/Interrupts/Raised   727131 times
14:31:17.452744 /Devices/virtio-net#0/Interrupts/Skipped   375723 times
14:31:17.452767 /Devices/virtio-net#0/Packets/ReceiveGSO        0 count
14:31:17.452790 /Devices/virtio-net#0/Packets/Transmit   151945 count
14:31:17.452798 /Devices/virtio-net#0/Packets/Transmit-Csum   105853 count
14:31:17.452805 /Devices/virtio-net#0/Packets/Transmit-Gso    45878 count
14:31:17.452813 /Devices/virtio-net#0/ReceiveBytes 131556947 bytes
14:31:17.452821 /Devices/virtio-net#0/TransmitBytes 1137796093 bytes
14:31:17.452828 /Devices/virtio-scsi#0/DescChainsAllocated   328193 count
14:31:17.452836 /Devices/virtio-scsi#0/DescChainsFreed   328193 count
14:31:17.452857 /Devices/virtio-scsi#0/DescChainsSegsIn  2094240 count
14:31:17.452864 /Devices/virtio-scsi#0/DescChainsSegsOut   449824 count
14:31:17.452955 /EM/CPU0/ExitHashing/Used               0 times
14:31:17.452980 /EM/CPU0/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.452989 /EM/CPU0/ExitOpt/ExecInstructions        0 times
14:31:17.452997 /EM/CPU0/ExitOpt/ExecSavedExit          0 times
14:31:17.453005 /EM/CPU0/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453013 /EM/CPU0/ExitOpt/ProbeInstructions        0 times
14:31:17.453020 /EM/CPU0/ExitOpt/ProbedExecWithMax        0 times
14:31:17.453028 /EM/CPU0/ExitOpt/ProbedNormal           0 times
14:31:17.453035 /EM/CPU0/ExitOpt/ProbedToRing3          0 times
14:31:17.453052 /EM/CPU1/ExitHashing/Used               0 times
14:31:17.453060 /EM/CPU1/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453068 /EM/CPU1/ExitOpt/ExecInstructions        0 times
14:31:17.453075 /EM/CPU1/ExitOpt/ExecSavedExit          0 times
14:31:17.453083 /EM/CPU1/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453091 /EM/CPU1/ExitOpt/ProbeInstructions        0 times
14:31:17.453099 /EM/CPU1/ExitOpt/ProbedExecWithMax        0 times
14:31:17.453106 /EM/CPU1/ExitOpt/ProbedNormal           0 times
14:31:17.453114 /EM/CPU1/ExitOpt/ProbedToRing3          0 times
14:31:17.453131 /EM/CPU2/ExitHashing/Used               0 times
14:31:17.453139 /EM/CPU2/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453162 /EM/CPU2/ExitOpt/ExecInstructions        0 times
14:31:17.453172 /EM/CPU2/ExitOpt/ExecSavedExit          0 times
14:31:17.453179 /EM/CPU2/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453187 /EM/CPU2/ExitOpt/ProbeInstructions        0 times
14:31:17.453195 /EM/CPU2/ExitOpt/ProbedExecWithMax        0 times
14:31:17.453202 /EM/CPU2/ExitOpt/ProbedNormal           0 times
14:31:17.453210 /EM/CPU2/ExitOpt/ProbedToRing3          0 times
14:31:17.453220 /EM/CPU3/ExitHashing/Used               0 times
14:31:17.453228 /EM/CPU3/ExitOpt/Exec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453236 /EM/CPU3/ExitOpt/ExecInstructions        0 times
14:31:17.453244 /EM/CPU3/ExitOpt/ExecSavedExit          0 times
14:31:17.453251 /EM/CPU3/ExitOpt/Probe                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.453259 /EM/CPU3/ExitOpt/ProbeInstructions        0 times
14:31:17.453267 /EM/CPU3/ExitOpt/ProbedExecWithMax        0 times
14:31:17.453274 /EM/CPU3/ExitOpt/ProbedNormal           0 times
14:31:17.453281 /EM/CPU3/ExitOpt/ProbedToRing3          0 times
14:31:17.453289 /GMM/ChunkTlbHits                    9400 times
14:31:17.453297 /GMM/ChunkTlbMisses                  1870 times
14:31:17.453376 /GMM/VM/Allocated/cBasePages       357982 pages
14:31:17.453386 /GMM/VM/Allocated/cFixedPages           0 pages
14:31:17.453394 /GMM/VM/Allocated/cShadowPages          0 pages
14:31:17.453432 /GMM/VM/Reserved/cBasePages       1572958 pages
14:31:17.453455 /GMM/VM/Reserved/cFixedPages         5140 pages
14:31:17.453478 /GMM/VM/Reserved/cShadowPages           1 pages
14:31:17.453484 /GMM/VM/cBalloonedPages                 0 pages
14:31:17.453490 /GMM/VM/cMaxBalloonedPages              0 pages
14:31:17.453494 /GMM/VM/cPrivatePages              357982 pages
14:31:17.453499 /GMM/VM/cReqActuallyBalloonedPages        0 pages
14:31:17.453504 /GMM/VM/cReqBalloonedPages              0 pages
14:31:17.453509 /GMM/VM/cReqDeflatePages                0 pages
14:31:17.453514 /GMM/VM/cShareableModules               0 count
14:31:17.453519 /GMM/VM/cSharedPages                    0 pages
14:31:17.453524 /GMM/VM/enmPolicy                       1 
14:31:17.453529 /GMM/VM/enmPriority                     2 
14:31:17.453533 /GMM/VM/fBallooningEnabled       false    
14:31:17.453538 /GMM/VM/fMayAllocate             true     
14:31:17.453543 /GMM/VM/fSharedPagingEnabled     false    
14:31:17.453548 /GMM/cAllocatedPages               357982 pages
14:31:17.453552 /GMM/cBalloonedPages                    0 pages
14:31:17.453557 /GMM/cChunks                          700 count
14:31:17.453562 /GMM/cDuplicatePages                    0 pages
14:31:17.453567 /GMM/cFreedChunks                       0 count
14:31:17.453572 /GMM/cLeftBehindSharedPages             0 pages
14:31:17.453576 /GMM/cMaxPages                   4294967295 pages
14:31:17.453581 /GMM/cOverCommittedPages                0 pages
14:31:17.453586 /GMM/cReservedPages               1578099 pages
14:31:17.453591 /GMM/cShareableModules                  0 count
14:31:17.453596 /GMM/cSharedPages                       0 pages
14:31:17.453601 /GMM/idFreeGeneration            4611686018427387775 
14:31:17.453759 /GVMM/EMTs                              4 calls
14:31:17.453766 /GVMM/HostCPUs                          8 calls
14:31:17.453771 /GVMM/HostCpus/0                        0 
14:31:17.453776 /GVMM/HostCpus/0/CurTimerHz             0 Hz
14:31:17.453781 /GVMM/HostCpus/0/DesiredHz              0 Hz
14:31:17.453785 /GVMM/HostCpus/0/PPTChanges             0 times
14:31:17.453790 /GVMM/HostCpus/0/PPTStarts              0 times
14:31:17.453795 /GVMM/HostCpus/0/idxCpuSet              0 
14:31:17.453799 /GVMM/HostCpus/1                        1 
14:31:17.453804 /GVMM/HostCpus/1/CurTimerHz             0 Hz
14:31:17.453809 /GVMM/HostCpus/1/DesiredHz              0 Hz
14:31:17.453813 /GVMM/HostCpus/1/PPTChanges             0 times
14:31:17.453818 /GVMM/HostCpus/1/PPTStarts              0 times
14:31:17.453823 /GVMM/HostCpus/1/idxCpuSet              1 
14:31:17.453827 /GVMM/HostCpus/2                        2 
14:31:17.453832 /GVMM/HostCpus/2/CurTimerHz             0 Hz
14:31:17.453837 /GVMM/HostCpus/2/DesiredHz              0 Hz
14:31:17.453841 /GVMM/HostCpus/2/PPTChanges             0 times
14:31:17.453846 /GVMM/HostCpus/2/PPTStarts              0 times
14:31:17.453851 /GVMM/HostCpus/2/idxCpuSet              2 
14:31:17.453855 /GVMM/HostCpus/3                        3 
14:31:17.453860 /GVMM/HostCpus/3/CurTimerHz             0 Hz
14:31:17.453865 /GVMM/HostCpus/3/DesiredHz              0 Hz
14:31:17.453870 /GVMM/HostCpus/3/PPTChanges             0 times
14:31:17.453875 /GVMM/HostCpus/3/PPTStarts              0 times
14:31:17.453879 /GVMM/HostCpus/3/idxCpuSet              3 
14:31:17.453884 /GVMM/HostCpus/4                        4 
14:31:17.453889 /GVMM/HostCpus/4/CurTimerHz             0 Hz
14:31:17.453893 /GVMM/HostCpus/4/DesiredHz              0 Hz
14:31:17.453898 /GVMM/HostCpus/4/PPTChanges             0 times
14:31:17.453902 /GVMM/HostCpus/4/PPTStarts              0 times
14:31:17.453907 /GVMM/HostCpus/4/idxCpuSet              4 
14:31:17.453912 /GVMM/HostCpus/5                        5 
14:31:17.453916 /GVMM/HostCpus/5/CurTimerHz             0 Hz
14:31:17.453921 /GVMM/HostCpus/5/DesiredHz              0 Hz
14:31:17.453926 /GVMM/HostCpus/5/PPTChanges             0 times
14:31:17.453930 /GVMM/HostCpus/5/PPTStarts              0 times
14:31:17.453935 /GVMM/HostCpus/5/idxCpuSet              5 
14:31:17.453941 /GVMM/HostCpus/6                        6 
14:31:17.453947 /GVMM/HostCpus/6/CurTimerHz             0 Hz
14:31:17.453951 /GVMM/HostCpus/6/DesiredHz              0 Hz
14:31:17.453957 /GVMM/HostCpus/6/PPTChanges             0 times
14:31:17.453962 /GVMM/HostCpus/6/PPTStarts              0 times
14:31:17.453983 /GVMM/HostCpus/6/idxCpuSet              6 
14:31:17.453992 /GVMM/HostCpus/7                        7 
14:31:17.453999 /GVMM/HostCpus/7/CurTimerHz             0 Hz
14:31:17.454006 /GVMM/HostCpus/7/DesiredHz              0 Hz
14:31:17.454012 /GVMM/HostCpus/7/PPTChanges             0 times
14:31:17.454018 /GVMM/HostCpus/7/PPTStarts              0 times
14:31:17.454023 /GVMM/HostCpus/7/idxCpuSet              7 
14:31:17.454041 /GVMM/Sum/HaltBlocking           30560783 calls
14:31:17.454046 /GVMM/Sum/HaltCalls              30558680 calls
14:31:17.454066 /GVMM/Sum/HaltNotBlocking            1055 calls
14:31:17.454071 /GVMM/Sum/HaltTimeouts           13645540 calls
14:31:17.454076 /GVMM/Sum/HaltWakeUps                   0 calls
14:31:17.454081 /GVMM/Sum/PokeCalls              13042473 calls
14:31:17.454086 /GVMM/Sum/PokeNotBusy              175607 calls
14:31:17.454091 /GVMM/Sum/PollCalls                270679 calls
14:31:17.454096 /GVMM/Sum/PollHalts                     0 calls
14:31:17.454100 /GVMM/Sum/PollWakeUps                   0 calls
14:31:17.454105 /GVMM/Sum/WakeUpCalls            16937275 calls
14:31:17.454110 /GVMM/Sum/WakeUpNotHalted          177360 calls
14:31:17.454115 /GVMM/Sum/WakeUpWakeUps                 0 calls
14:31:17.454120 /GVMM/VM/HaltBlocking            30560783 calls
14:31:17.454125 /GVMM/VM/HaltCalls               30558680 calls
14:31:17.454131 /GVMM/VM/HaltNotBlocking             1055 calls
14:31:17.454138 /GVMM/VM/HaltTimeouts            13645540 calls
14:31:17.454145 /GVMM/VM/HaltWakeUps                    0 calls
14:31:17.454150 /GVMM/VM/PokeCalls               13042473 calls
14:31:17.454155 /GVMM/VM/PokeNotBusy               175607 calls
14:31:17.454161 /GVMM/VM/PollCalls                 270679 calls
14:31:17.454170 /GVMM/VM/PollHalts                      0 calls
14:31:17.454181 /GVMM/VM/PollWakeUps                    0 calls
14:31:17.454187 /GVMM/VM/WakeUpCalls             16937275 calls
14:31:17.454193 /GVMM/VM/WakeUpNotHalted           177360 calls
14:31:17.454205 /GVMM/VM/WakeUpWakeUps                  0 calls
14:31:17.454213 /GVMM/VMs                               1 calls
14:31:17.454223 /HGCM/FailedPageListLocking             0 count
14:31:17.454232 /HGCM/LargeCmdAllocs                    4 count
14:31:17.454252 /HGCM/MsgArrival                    45595 ticks/call (     6428997 ticks,     141 times, max    304409, min    9886)
14:31:17.454262 /HGCM/MsgCompletion                 29847 ticks/call (     4298004 ticks,     144 times, max    107670, min    4626)
14:31:17.454270 /HGCM/MsgTotal                     490445 ticks/call (    69152801 ticks,     141 times, max   3926251, min   12064)
14:31:17.454281 /HM/CPU0/Exit/HostNmiInGC               0 times
14:31:17.454289 /HM/CPU0/Exit/HostNmiInGCIpi            0 times
14:31:17.454294 /HM/CPU0/Exit/Trap/Gst/#AC              0 times
14:31:17.454299 /HM/CPU0/Exit/Trap/Gst/#AC-split-lock        0 times
14:31:17.454304 /HM/CPU0/Switch/Preempting              0 times
14:31:17.454309 /HM/CPU1/Exit/HostNmiInGC               0 times
14:31:17.454329 /HM/CPU1/Exit/HostNmiInGCIpi            0 times
14:31:17.454334 /HM/CPU1/Exit/Trap/Gst/#AC              0 times
14:31:17.454340 /HM/CPU1/Exit/Trap/Gst/#AC-split-lock        0 times
14:31:17.454362 /HM/CPU1/Switch/Preempting              0 times
14:31:17.454386 /HM/CPU2/Exit/HostNmiInGC               0 times
14:31:17.454393 /HM/CPU2/Exit/HostNmiInGCIpi            0 times
14:31:17.454401 /HM/CPU2/Exit/Trap/Gst/#AC              0 times
14:31:17.454413 /HM/CPU2/Exit/Trap/Gst/#AC-split-lock        0 times
14:31:17.454431 /HM/CPU2/Switch/Preempting              0 times
14:31:17.454444 /HM/CPU3/Exit/HostNmiInGC               0 times
14:31:17.454454 /HM/CPU3/Exit/HostNmiInGCIpi            0 times
14:31:17.454464 /HM/CPU3/Exit/Trap/Gst/#AC              0 times
14:31:17.454471 /HM/CPU3/Exit/Trap/Gst/#AC-split-lock        0 times
14:31:17.454481 /HM/CPU3/Switch/Preempting              0 times
14:31:17.454489 /IEM/CPU0/CodeTlb-Misses                0 count
14:31:17.454503 /IEM/CPU0/CodeTlb-PhysRev        ffffffffffff9c00 
14:31:17.454524 /IEM/CPU0/CodeTlb-Revision       fffff38000000000 
14:31:17.454544 /IEM/CPU0/CodeTlb-SlowReads             0 
14:31:17.454558 /IEM/CPU0/DataTlb-Misses                0 count
14:31:17.454569 /IEM/CPU0/DataTlb-PhysRev        ffffffffffff9c00 
14:31:17.454575 /IEM/CPU0/DataTlb-Revision       fffff38000000000 
14:31:17.454593 /IEM/CPU0/cInstructions           4234898 count
14:31:17.454598 /IEM/CPU0/cLongJumps                    6 bytes
14:31:17.454618 /IEM/CPU0/cPendingCommit                0 bytes
14:31:17.454636 /IEM/CPU0/cPotentialExits         4227401 count
14:31:17.454641 /IEM/CPU0/cRetAspectNotImplemented        0 count
14:31:17.454661 /IEM/CPU0/cRetErrStatuses               0 count
14:31:17.454666 /IEM/CPU0/cRetInfStatuses           11319 count
14:31:17.454671 /IEM/CPU0/cRetInstrNotImplemented        0 count
14:31:17.454675 /IEM/CPU0/cbWritten              12452876 bytes
14:31:17.454681 /IEM/CPU1/CodeTlb-Misses                0 count
14:31:17.454685 /IEM/CPU1/CodeTlb-PhysRev        ffffffffffff9c00 
14:31:17.454703 /IEM/CPU1/CodeTlb-Revision       fffff38000000000 
14:31:17.454708 /IEM/CPU1/CodeTlb-SlowReads             0 
14:31:17.454729 /IEM/CPU1/DataTlb-Misses                0 count
14:31:17.454734 /IEM/CPU1/DataTlb-PhysRev        ffffffffffff9c00 
14:31:17.454752 /IEM/CPU1/DataTlb-Revision       fffff38000000000 
14:31:17.454757 /IEM/CPU1/cInstructions             87657 count
14:31:17.454777 /IEM/CPU1/cLongJumps                    0 bytes
14:31:17.454795 /IEM/CPU1/cPendingCommit                0 bytes
14:31:17.454800 /IEM/CPU1/cPotentialExits           81337 count
14:31:17.454819 /IEM/CPU1/cRetAspectNotImplemented        0 count
14:31:17.454825 /IEM/CPU1/cRetErrStatuses               0 count
14:31:17.454833 /IEM/CPU1/cRetInfStatuses            7425 count
14:31:17.454839 /IEM/CPU1/cRetInstrNotImplemented        0 count
14:31:17.454859 /IEM/CPU1/cbWritten                160464 bytes
14:31:17.454866 /IEM/CPU2/CodeTlb-Misses                0 count
14:31:17.454885 /IEM/CPU2/CodeTlb-PhysRev        ffffffffffff9c00 
14:31:17.454890 /IEM/CPU2/CodeTlb-Revision       fffff38000000000 
14:31:17.454908 /IEM/CPU2/CodeTlb-SlowReads             0 
14:31:17.454913 /IEM/CPU2/DataTlb-Misses                0 count
14:31:17.454918 /IEM/CPU2/DataTlb-PhysRev        ffffffffffff9c00 
14:31:17.454923 /IEM/CPU2/DataTlb-Revision       fffff38000000000 
14:31:17.454928 /IEM/CPU2/cInstructions             93267 count
14:31:17.454946 /IEM/CPU2/cLongJumps                    0 bytes
14:31:17.454952 /IEM/CPU2/cPendingCommit                0 bytes
14:31:17.454956 /IEM/CPU2/cPotentialExits           87022 count
14:31:17.454961 /IEM/CPU2/cRetAspectNotImplemented        0 count
14:31:17.454966 /IEM/CPU2/cRetErrStatuses               0 count
14:31:17.454971 /IEM/CPU2/cRetInfStatuses            7329 count
14:31:17.454976 /IEM/CPU2/cRetInstrNotImplemented        0 count
14:31:17.454980 /IEM/CPU2/cbWritten                171876 bytes
14:31:17.454985 /IEM/CPU3/CodeTlb-Misses                0 count
14:31:17.454990 /IEM/CPU3/CodeTlb-PhysRev        ffffffffffff9c00 
14:31:17.454995 /IEM/CPU3/CodeTlb-Revision       fffff38000000000 
14:31:17.455000 /IEM/CPU3/CodeTlb-SlowReads             0 
14:31:17.455006 /IEM/CPU3/DataTlb-Misses                0 count
14:31:17.455013 /IEM/CPU3/DataTlb-PhysRev        ffffffffffff9c00 
14:31:17.455024 /IEM/CPU3/DataTlb-Revision       fffff38000000000 
14:31:17.455032 /IEM/CPU3/cInstructions             82297 count
14:31:17.455040 /IEM/CPU3/cLongJumps                    0 bytes
14:31:17.455053 /IEM/CPU3/cPendingCommit                0 bytes
14:31:17.455067 /IEM/CPU3/cPotentialExits           79953 count
14:31:17.455078 /IEM/CPU3/cRetAspectNotImplemented        0 count
14:31:17.455085 /IEM/CPU3/cRetErrStatuses               0 count
14:31:17.455095 /IEM/CPU3/cRetInfStatuses            3329 count
14:31:17.455107 /IEM/CPU3/cRetInstrNotImplemented        0 count
14:31:17.455116 /IEM/CPU3/cbWritten                157936 bytes
14:31:17.455123 /IOM/MmioMappingsStale                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.455131 /MM/HyperHeap/cbFree              8535024 bytes
14:31:17.455159 /MM/HyperHeap/cbHeap              8650432 bytes
14:31:17.455168 /PDM/BlkCache/cbCached                  0 bytes
14:31:17.455176 /PDM/BlkCache/cbCachedFru               0 bytes
14:31:17.455183 /PDM/BlkCache/cbCachedMruIn             0 bytes
14:31:17.455197 /PDM/BlkCache/cbCachedMruOut            0 bytes
14:31:17.455210 /PDM/BlkCache/cbMax               5242880 bytes
14:31:17.455218 /PDM/CritSects/8237A#0Auto/ContentionR3        0 times
14:31:17.455225 /PDM/CritSects/8237A#0Auto/ContentionRZLock        0 times
14:31:17.455232 /PDM/CritSects/8237A#0Auto/ContentionRZUnlock        0 times
14:31:17.455250 /PDM/CritSects/GIMDev#0Auto/ContentionR3        0 times
14:31:17.455259 /PDM/CritSects/GIMDev#0Auto/ContentionRZLock        0 times
14:31:17.455266 /PDM/CritSects/GIMDev#0Auto/ContentionRZUnlock        0 times
14:31:17.455272 /PDM/CritSects/MM-HYPER/ContentionR3        0 times
14:31:17.455277 /PDM/CritSects/MM-HYPER/ContentionRZLock        0 times
14:31:17.455286 /PDM/CritSects/MM-HYPER/ContentionRZUnlock        0 times
14:31:17.455305 /PDM/CritSects/NOP/ContentionR3         0 times
14:31:17.455320 /PDM/CritSects/NOP/ContentionRZLock        0 times
14:31:17.455331 /PDM/CritSects/NOP/ContentionRZUnlock        0 times
14:31:17.455338 /PDM/CritSects/PDM/ContentionR3      1860 times
14:31:17.455343 /PDM/CritSects/PDM/ContentionRZLock      582 times
14:31:17.455348 /PDM/CritSects/PDM/ContentionRZUnlock        0 times
14:31:17.455353 /PDM/CritSects/PGM/ContentionR3     83342 times
14:31:17.455372 /PDM/CritSects/PGM/ContentionRZLock    12465 times
14:31:17.455377 /PDM/CritSects/PGM/ContentionRZUnlock        0 times
14:31:17.455382 /PDM/CritSects/TM Timer Lock/ContentionR3   236390 times
14:31:17.455387 /PDM/CritSects/TM Timer Lock/ContentionRZLock        0 times
14:31:17.455391 /PDM/CritSects/TM Timer Lock/ContentionRZUnlock        0 times
14:31:17.455396 /PDM/CritSects/TM VirtualSync Lock/ContentionR3   363681 times
14:31:17.455401 /PDM/CritSects/TM VirtualSync Lock/ContentionRZLock  7885661 times
14:31:17.455406 /PDM/CritSects/TM VirtualSync Lock/ContentionRZUnlock     3465 times
14:31:17.455411 /PDM/CritSects/VMMDev#0/ContentionR3        0 times
14:31:17.455416 /PDM/CritSects/VMMDev#0/ContentionRZLock        0 times
14:31:17.455421 /PDM/CritSects/VMMDev#0/ContentionRZUnlock        0 times
14:31:17.455426 /PDM/CritSects/VNet0/ContentionR3        0 times
14:31:17.455430 /PDM/CritSects/VNet0/ContentionRZLock        0 times
14:31:17.455435 /PDM/CritSects/VNet0/ContentionRZUnlock        0 times
14:31:17.455440 /PDM/CritSects/acpi#0/ContentionR3        0 times
14:31:17.455445 /PDM/CritSects/acpi#0/ContentionRZLock        0 times
14:31:17.455450 /PDM/CritSects/acpi#0/ContentionRZUnlock        0 times
14:31:17.455455 /PDM/CritSects/fastpipe#0Auto/ContentionR3     3025 times
14:31:17.455460 /PDM/CritSects/fastpipe#0Auto/ContentionRZLock        0 times
14:31:17.455465 /PDM/CritSects/fastpipe#0Auto/ContentionRZUnlock        0 times
14:31:17.455470 /PDM/CritSects/mc146818#0Auto/ContentionR3        0 times
14:31:17.455475 /PDM/CritSects/mc146818#0Auto/ContentionRZLock        0 times
14:31:17.455479 /PDM/CritSects/mc146818#0Auto/ContentionRZUnlock        0 times
14:31:17.455484 /PDM/CritSects/pcarch#0Auto/ContentionR3        0 times
14:31:17.455489 /PDM/CritSects/pcarch#0Auto/ContentionRZLock        0 times
14:31:17.455494 /PDM/CritSects/pcarch#0Auto/ContentionRZUnlock        0 times
14:31:17.455502 /PDM/CritSects/pcbios#0Auto/ContentionR3        0 times
14:31:17.455509 /PDM/CritSects/pcbios#0Auto/ContentionRZLock        0 times
14:31:17.455514 /PDM/CritSects/pcbios#0Auto/ContentionRZUnlock        0 times
14:31:17.455520 /PDM/CritSects/pckbd#0Auto/ContentionR3        0 times
14:31:17.455524 /PDM/CritSects/pckbd#0Auto/ContentionRZLock        0 times
14:31:17.455529 /PDM/CritSects/pckbd#0Auto/ContentionRZUnlock        0 times
14:31:17.455534 /PDM/CritSects/pit#0/ContentionR3        0 times
14:31:17.455539 /PDM/CritSects/pit#0/ContentionRZLock        0 times
14:31:17.455544 /PDM/CritSects/pit#0/ContentionRZUnlock        0 times
14:31:17.455549 /PDM/CritSects/virtio-scsi#0Auto/ContentionR3      548 times
14:31:17.455553 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZLock      832 times
14:31:17.455558 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZUnlock        0 times
14:31:17.455563 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterExcl        0 times
14:31:17.455568 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterShared        0 times
14:31:17.455574 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterExcl        0 times
14:31:17.455580 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterShared        0 times
14:31:17.455584 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveExcl        0 times
14:31:17.455589 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveShared        0 times
14:31:17.455594 /PDM/CritSectsRw/IOM Lock/R3EnterExcl      114 times
14:31:17.455599 /PDM/CritSectsRw/IOM Lock/R3EnterShared  2468718 times
14:31:17.455604 /PDM/CritSectsRw/IOM Lock/RZEnterExcl        0 times
14:31:17.455624 /PDM/CritSectsRw/IOM Lock/RZEnterShared  5644719 times
14:31:17.455630 /PDM/Queue/DevHlp/AllocFailures         0 times
14:31:17.455644 /PDM/Queue/DevHlp/Flush                 0 calls
14:31:17.455650 /PDM/Queue/DevHlp/FlushLeftovers        0 times
14:31:17.455655 /PDM/Queue/DevHlp/Insert                0 calls
14:31:17.455660 /PDM/Queue/DevHlp/cItems                8 count
14:31:17.455665 /PDM/Queue/DevHlp/cbItem               64 bytes
14:31:17.455722 /PDM/Queue/Keyboard/AllocFailures        0 times
14:31:17.455729 /PDM/Queue/Keyboard/Flush               0 calls
14:31:17.455734 /PDM/Queue/Keyboard/FlushLeftovers        0 times
14:31:17.455739 /PDM/Queue/Keyboard/Insert              0 calls
14:31:17.455744 /PDM/Queue/Keyboard/cItems             64 count
14:31:17.455749 /PDM/Queue/Keyboard/cbItem             32 bytes
14:31:17.455754 /PDM/Queue/Mouse/AllocFailures          0 times
14:31:17.455758 /PDM/Queue/Mouse/Flush                  0 calls
14:31:17.455763 /PDM/Queue/Mouse/FlushLeftovers         0 times
14:31:17.455768 /PDM/Queue/Mouse/Insert                 0 calls
14:31:17.455773 /PDM/Queue/Mouse/cItems               128 count
14:31:17.455778 /PDM/Queue/Mouse/cbItem                48 bytes
14:31:17.455783 /PDM/Queue/SCSI-Eject/AllocFailures        0 times
14:31:17.455788 /PDM/Queue/SCSI-Eject/Flush             0 calls
14:31:17.455792 /PDM/Queue/SCSI-Eject/FlushLeftovers        0 times
14:31:17.455797 /PDM/Queue/SCSI-Eject/Insert            0 calls
14:31:17.455802 /PDM/Queue/SCSI-Eject/cItems            1 count
14:31:17.455807 /PDM/Queue/SCSI-Eject/cbItem           40 bytes
14:31:17.455811 /PDM/Queue/SCSI-Eject_1/AllocFailures        0 times
14:31:17.455816 /PDM/Queue/SCSI-Eject_1/Flush           0 calls
14:31:17.455821 /PDM/Queue/SCSI-Eject_1/FlushLeftovers        0 times
14:31:17.455826 /PDM/Queue/SCSI-Eject_1/Insert          0 calls
14:31:17.455830 /PDM/Queue/SCSI-Eject_1/cItems          1 count
14:31:17.455835 /PDM/Queue/SCSI-Eject_1/cbItem         40 bytes
14:31:17.455840 /PDM/Queue/SCSI-Eject_2/AllocFailures        0 times
14:31:17.455845 /PDM/Queue/SCSI-Eject_2/Flush           0 calls
14:31:17.455849 /PDM/Queue/SCSI-Eject_2/FlushLeftovers        0 times
14:31:17.455854 /PDM/Queue/SCSI-Eject_2/Insert          0 calls
14:31:17.455859 /PDM/Queue/SCSI-Eject_2/cItems          1 count
14:31:17.455863 /PDM/Queue/SCSI-Eject_2/cbItem         40 bytes
14:31:17.455869 /PGM/CPU0/cA20Changes                   2 times
14:31:17.455927 /PGM/CPU0/cGuestModeChanges          3700 times
14:31:17.455935 /PGM/CPU1/cA20Changes                   0 times
14:31:17.455939 /PGM/CPU1/cGuestModeChanges             3 times
14:31:17.455944 /PGM/CPU2/cA20Changes                   0 times
14:31:17.455951 /PGM/CPU2/cGuestModeChanges             3 times
14:31:17.455956 /PGM/CPU3/cA20Changes                   0 times
14:31:17.455960 /PGM/CPU3/cGuestModeChanges             3 times
14:31:17.455965 /PGM/ChunkR3Map/Mapped                700 count
14:31:17.455970 /PGM/ChunkR3Map/Unmapped                0 count
14:31:17.455975 /PGM/ChunkR3Map/c                     700 count
14:31:17.455979 /PGM/ChunkR3Map/cMax             4294967295 count
14:31:17.455984 /PGM/LargePage/Recheck                  0 times
14:31:17.455989 /PGM/LargePage/Refused                  2 times
14:31:17.455994 /PGM/LargePage/Reused                 446 times
14:31:17.456018 /PGM/Mmio2QueryAndResetDirtyBitmap        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456024 /PGM/Page/cAllPages               1577974 count
14:31:17.456029 /PGM/Page/cBalloonedPages               0 count
14:31:17.456034 /PGM/Page/cHandyPages                 128 count
14:31:17.456039 /PGM/Page/cLargePages                 206 count
14:31:17.456043 /PGM/Page/cLargePagesDisabled           0 count
14:31:17.456049 /PGM/Page/cMonitoredPages               0 count
14:31:17.456053 /PGM/Page/cPrivatePages            362994 count
14:31:17.456058 /PGM/Page/cPureMmioPages                4 count
14:31:17.456063 /PGM/Page/cReadLockedPages              0 count
14:31:17.456068 /PGM/Page/cReusedSharedPages            0 count
14:31:17.456073 /PGM/Page/cSharedPages                  0 count
14:31:17.456078 /PGM/Page/cWriteLockedPages             0 count
14:31:17.456082 /PGM/Page/cWrittenToPages               0 count
14:31:17.456087 /PGM/Page/cZeroPages              1214976 count
14:31:17.456092 /PGM/Pool/Grow                    8821322 ticks (    17642645 ticks,       2 times, max   9413140, min 8229505)
14:31:17.456098 /PGM/ShMod/Check                        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456104 /PGM/cRelocations                       0 times
14:31:17.456109 /PROF/CPU0/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456115 /PROF/CPU0/EM/ForcedActions       9817714 times
14:31:17.456122 /PROF/CPU0/EM/Halted              1578246 times
14:31:17.456128 /PROF/CPU0/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456133 /PROF/CPU0/EM/NEMExecuteCalled          0 times
14:31:17.456138 /PROF/CPU0/EM/RAWTotal                  0 times
14:31:17.456143 /PROF/CPU0/EM/REMTotal                  0 times
14:31:17.456148 /PROF/CPU0/EM/RecordedExits      62700492 times
14:31:17.456153 /PROF/CPU0/EM/Total              188198118343758 ticks/call (188198118343758 ticks,       1 times, max 188198118343758, min 188198118343758)
14:31:17.456160 /PROF/CPU0/VM/Halt/Block          9690151 ns/call (13836334159283 ticks, 1427876 times, max 1786000590, min       1)
14:31:17.456166 /PROF/CPU0/VM/Halt/BlockInsomnia  9098547 ns/call (12976002179991 ticks, 1426162 times, max 499949309, min       1)
14:31:17.456173 /PROF/CPU0/VM/Halt/BlockOnTime   500005384 ns/call ( 45000484610 ticks,      90 times, max 500048572, min 499952652)
14:31:17.456182 /PROF/CPU0/VM/Halt/BlockOverslept  2051768 ns/call (  3332072622 ticks,    1624 times, max 1286001505, min   50194)
14:31:17.456196 /PROF/CPU0/VM/Halt/R0HaltBlock    9669922 ns/call (35220642628533 ticks, 3642288 times, max 2134452099, min       2)
14:31:17.456209 /PROF/CPU0/VM/Halt/R0HaltBlockInsomnia  9205795 ns/call (33498638357012 ticks, 3638864 times, max 499941114, min       2)
14:31:17.456223 /PROF/CPU0/VM/Halt/R0HaltBlockOnTime 499992942 ns/call ( 99998588435 ticks,     200 times, max 500041353, min 499929308)
14:31:17.456231 /PROF/CPU0/VM/Halt/R0HaltBlockOverslept  3113754 ns/call ( 10038744587 ticks,    3224 times, max 1634465593, min   50356)
14:31:17.456238 /PROF/CPU0/VM/Halt/R0HaltExec     4075489 times
14:31:17.456247 /PROF/CPU0/VM/Halt/R0HaltExec/FromBlock  3586968 times
14:31:17.456258 /PROF/CPU0/VM/Halt/R0HaltExec/FromSpin   356570 times
14:31:17.456266 /PROF/CPU0/VM/Halt/R0HaltHistoryCounter  5653699 times
14:31:17.456273 /PROF/CPU0/VM/Halt/R0HaltHistorySucceeded      185 times
14:31:17.456280 /PROF/CPU0/VM/Halt/R0HaltHistoryToRing3       13 times
14:31:17.456288 /PROF/CPU0/VM/Halt/R0HaltToR3     1578210 times
14:31:17.456295 /PROF/CPU0/VM/Halt/R0HaltToR3/FromSpin    19149 times
14:31:17.456324 /PROF/CPU0/VM/Halt/R0HaltToR3/Other        0 times
14:31:17.456337 /PROF/CPU0/VM/Halt/R0HaltToR3/PendingFF  1503741 times
14:31:17.456346 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitNoInt     9082 times
14:31:17.456367 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitPendingFF    46238 times
14:31:17.456389 /PROF/CPU0/VM/Halt/R0HaltToR3/SmallDelta        0 times
14:31:17.456397 /PROF/CPU0/VM/Halt/Timers             343 ns/call (   982283100 ticks, 2861941 times, max   5597445, min       1)
14:31:17.456419 /PROF/CPU0/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456442 /PROF/CPU1/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456464 /PROF/CPU1/EM/ForcedActions       8412216 times
14:31:17.456472 /PROF/CPU1/EM/Halted              1615048 times
14:31:17.456479 /PROF/CPU1/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456487 /PROF/CPU1/EM/NEMExecuteCalled          0 times
14:31:17.456495 /PROF/CPU1/EM/RAWTotal                  0 times
14:31:17.456502 /PROF/CPU1/EM/REMTotal                  0 times
14:31:17.456510 /PROF/CPU1/EM/RecordedExits      52356316 times
14:31:17.456540 /PROF/CPU1/EM/Total              188198118361413 ticks/call (188198118361413 ticks,       1 times, max 188198118361413, min 188198118361413)
14:31:17.456565 /PROF/CPU1/VM/Halt/Block          9589992 ns/call (14019207962042 ticks, 1461858 times, max 1398381610, min       1)
14:31:17.456574 /PROF/CPU1/VM/Halt/BlockInsomnia  9101867 ns/call (13292477014495 ticks, 1460412 times, max 499949484, min       1)
14:31:17.456583 /PROF/CPU1/VM/Halt/BlockOnTime   500008823 ns/call ( 44000776433 ticks,      88 times, max 500049064, min 499951164)
14:31:17.456592 /PROF/CPU1/VM/Halt/BlockOverslept  2747222 ns/call (  3730727581 ticks,    1358 times, max 898381945, min   50390)
14:31:17.456600 /PROF/CPU1/VM/Halt/R0HaltBlock   11001487 ns/call (35064864075188 ticks, 3187284 times, max 3158210730, min       2)
14:31:17.456632 /PROF/CPU1/VM/Halt/R0HaltBlockInsomnia 10546316 ns/call (33583003888704 ticks, 3184335 times, max 499941894, min       2)
14:31:17.456642 /PROF/CPU1/VM/Halt/R0HaltBlockOnTime 499992847 ns/call (103998512317 ticks,     208 times, max 500041100, min 499932392)
14:31:17.456651 /PROF/CPU1/VM/Halt/R0HaltBlockOverslept  2702402 ns/call (  7407285078 ticks,    2741 times, max 2658220626, min   51420)
14:31:17.456660 /PROF/CPU1/VM/Halt/R0HaltExec     3431676 times
14:31:17.456668 /PROF/CPU1/VM/Halt/R0HaltExec/FromBlock  3133929 times
14:31:17.456676 /PROF/CPU1/VM/Halt/R0HaltExec/FromSpin   213678 times
14:31:17.456684 /PROF/CPU1/VM/Halt/R0HaltHistoryCounter  5046685 times
14:31:17.456691 /PROF/CPU1/VM/Halt/R0HaltHistorySucceeded      112 times
14:31:17.456699 /PROF/CPU1/VM/Halt/R0HaltHistoryToRing3       48 times
14:31:17.456706 /PROF/CPU1/VM/Halt/R0HaltToR3     1615009 times
14:31:17.456714 /PROF/CPU1/VM/Halt/R0HaltToR3/FromSpin    19242 times
14:31:17.456723 /PROF/CPU1/VM/Halt/R0HaltToR3/Other        0 times
14:31:17.456753 /PROF/CPU1/VM/Halt/R0HaltToR3/PendingFF  1542412 times
14:31:17.456763 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitNoInt     6349 times
14:31:17.456770 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitPendingFF    47006 times
14:31:17.456778 /PROF/CPU1/VM/Halt/R0HaltToR3/SmallDelta        0 times
14:31:17.456787 /PROF/CPU1/VM/Halt/Timers             434 ns/call (  1271160206 ticks, 2928792 times, max 199396326, min       1)
14:31:17.456795 /PROF/CPU1/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456814 /PROF/CPU2/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456819 /PROF/CPU2/EM/ForcedActions       7903119 times
14:31:17.456840 /PROF/CPU2/EM/Halted              1500703 times
14:31:17.456845 /PROF/CPU2/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.456851 /PROF/CPU2/EM/NEMExecuteCalled          0 times
14:31:17.456864 /PROF/CPU2/EM/RAWTotal                  0 times
14:31:17.456875 /PROF/CPU2/EM/REMTotal                  0 times
14:31:17.456881 /PROF/CPU2/EM/RecordedExits      49595715 times
14:31:17.456886 /PROF/CPU2/EM/Total              188198118382029 ticks/call (188198118382029 ticks,       1 times, max 188198118382029, min 188198118382029)
14:31:17.456906 /PROF/CPU2/VM/Halt/Block         13042766 ns/call (17642780722833 ticks, 1352687 times, max 1400789004, min       1)
14:31:17.456912 /PROF/CPU2/VM/Halt/BlockInsomnia 13009889 ns/call (17597176138859 ticks, 1352600 times, max 499938428, min       1)
14:31:17.456917 /PROF/CPU2/VM/Halt/BlockOnTime   500006963 ns/call (  5500076597 ticks,      11 times, max 500039476, min 499964669)
14:31:17.456923 /PROF/CPU2/VM/Halt/BlockOverslept 27696087 ns/call (  2104902684 ticks,      76 times, max 900789362, min   51435)
14:31:17.456929 /PROF/CPU2/VM/Halt/R0HaltBlock   11188025 ns/call (31527330753850 ticks, 2817953 times, max 1446509228, min       2)
14:31:17.456934 /PROF/CPU2/VM/Halt/R0HaltBlockInsomnia 11153992 ns/call (31429330534518 ticks, 2817765 times, max 499931226, min       2)
14:31:17.456940 /PROF/CPU2/VM/Halt/R0HaltBlockOnTime 499992221 ns/call ( 10499836652 ticks,      21 times, max 500039808, min 499948191)
14:31:17.456945 /PROF/CPU2/VM/Halt/R0HaltBlockOverslept 23965616 ns/call (  4002257892 ticks,     167 times, max 946518040, min   61710)
14:31:17.456951 /PROF/CPU2/VM/Halt/R0HaltExec     3052256 times
14:31:17.456956 /PROF/CPU2/VM/Halt/R0HaltExec/FromBlock  2765572 times
14:31:17.456961 /PROF/CPU2/VM/Halt/R0HaltExec/FromSpin   208130 times
14:31:17.456966 /PROF/CPU2/VM/Halt/R0HaltHistoryCounter  4552941 times
14:31:17.456970 /PROF/CPU2/VM/Halt/R0HaltHistorySucceeded      177 times
14:31:17.456975 /PROF/CPU2/VM/Halt/R0HaltHistoryToRing3       63 times
14:31:17.456980 /PROF/CPU2/VM/Halt/R0HaltToR3     1500685 times
14:31:17.456985 /PROF/CPU2/VM/Halt/R0HaltToR3/FromSpin    16564 times
14:31:17.456989 /PROF/CPU2/VM/Halt/R0HaltToR3/Other        0 times
14:31:17.456994 /PROF/CPU2/VM/Halt/R0HaltToR3/PendingFF  1431740 times
14:31:17.456999 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitNoInt     3142 times
14:31:17.457003 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitPendingFF    49239 times
14:31:17.457008 /PROF/CPU2/VM/Halt/R0HaltToR3/SmallDelta        0 times
14:31:17.457013 /PROF/CPU2/VM/Halt/Timers             360 ns/call (   977771633 ticks, 2711597 times, max   5191991, min       1)
14:31:17.457018 /PROF/CPU2/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.457024 /PROF/CPU3/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.457029 /PROF/CPU3/EM/ForcedActions      12292320 times
14:31:17.457034 /PROF/CPU3/EM/Halted              5887994 times
14:31:17.457039 /PROF/CPU3/EM/NEMExec                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.457044 /PROF/CPU3/EM/NEMExecuteCalled          0 times
14:31:17.457048 /PROF/CPU3/EM/RAWTotal                  0 times
14:31:17.457053 /PROF/CPU3/EM/REMTotal                  0 times
14:31:17.457058 /PROF/CPU3/EM/RecordedExits      56534470 times
14:31:17.457063 /PROF/CPU3/EM/Total              188198118382772 ticks/call (188198118382772 ticks,       1 times, max 188198118382772, min 188198118382772)
14:31:17.457070 /PROF/CPU3/VM/Halt/Block          2743629 ns/call (34772969590946 ticks, 12674077 times, max 2362364733, min       1)
14:31:17.457077 /PROF/CPU3/VM/Halt/BlockInsomnia   861217 ns/call (1237204432603 ticks, 1436576 times, max 512270921, min       1)
14:31:17.457082 /PROF/CPU3/VM/Halt/BlockOnTime    2954362 ns/call (2126145242248 ticks,  719663 times, max 914050259, min    1636)
14:31:17.457088 /PROF/CPU3/VM/Halt/BlockOverslept   418245 ns/call (4399042450015 ticks, 10517838 times, max 2316150265, min   50001)
14:31:17.457094 /PROF/CPU3/VM/Halt/R0HaltBlock    3548746 ns/call (14204478246189 ticks, 4002675 times, max 605683350, min       2)
14:31:17.457099 /PROF/CPU3/VM/Halt/R0HaltBlockInsomnia   567778 ns/call (742874378578 ticks, 1308387 times, max 332686830, min       2)
14:31:17.457105 /PROF/CPU3/VM/Halt/R0HaltBlockOnTime  3461044 ns/call (1309884291195 ticks,  378465 times, max 160813230, min     504)
14:31:17.457110 /PROF/CPU3/VM/Halt/R0HaltBlockOverslept   475327 ns/call (1100773602762 ticks, 2315823 times, max 597962422, min   50001)
14:31:17.457116 /PROF/CPU3/VM/Halt/R0HaltExec     1102979 times
14:31:17.457121 /PROF/CPU3/VM/Halt/R0HaltExec/FromBlock  1019786 times
14:31:17.457126 /PROF/CPU3/VM/Halt/R0HaltExec/FromSpin    13295 times
14:31:17.457131 /PROF/CPU3/VM/Halt/R0HaltHistoryCounter  6909362 times
14:31:17.457135 /PROF/CPU3/VM/Halt/R0HaltHistorySucceeded       20 times
14:31:17.457140 /PROF/CPU3/VM/Halt/R0HaltHistoryToRing3      161 times
14:31:17.457145 /PROF/CPU3/VM/Halt/R0HaltToR3     5806383 times
14:31:17.457150 /PROF/CPU3/VM/Halt/R0HaltToR3/FromSpin       31 times
14:31:17.457155 /PROF/CPU3/VM/Halt/R0HaltToR3/Other        0 times
14:31:17.457159 /PROF/CPU3/VM/Halt/R0HaltToR3/PendingFF  1693337 times
14:31:17.457164 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitNoInt  2839034 times
14:31:17.457169 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitPendingFF   143855 times
14:31:17.457174 /PROF/CPU3/VM/Halt/R0HaltToR3/SmallDelta  1130126 times
14:31:17.457178 /PROF/CPU3/VM/Halt/Timers            1081 ns/call (103891538391 ticks, 96046044 times, max 8891478531, min       2)
14:31:17.457184 /PROF/CPU3/VM/Halt/Yield             3185 ns/call (   862345583 ticks,  270679 times, max   1281145, min       1)
14:31:17.457190 /Public/NetAdapter/0/BytesReceived 131556947 bytes
14:31:17.457194 /Public/NetAdapter/0/BytesTransmitted 1137796093 bytes
14:31:17.457199 /Public/NetAdapter/0/virtio-net         0 
14:31:17.457204 /Public/Storage/VIRTIO-SCSI0/Port0/BytesRead 6657452544 bytes
14:31:17.457209 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufAttempts        0 count
14:31:17.457214 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufSuccess        0 count
14:31:17.457220 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsRead   206607 count
14:31:17.457224 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSubmitted   206607 count
14:31:17.457229 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSucceeded   206607 count
14:31:17.457234 /Public/Storage/VIRTIO-SCSI0/Port1/BytesRead 1399916544 bytes
14:31:17.457239 /Public/Storage/VIRTIO-SCSI0/Port1/BytesWritten 518090752 bytes
14:31:17.457244 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufAttempts        0 count
14:31:17.457249 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufSuccess        0 count
14:31:17.457253 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsRead    84531 count
14:31:17.457258 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSubmitted   120424 count
14:31:17.457264 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSucceeded   120424 count
14:31:17.457269 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsWrite    35893 count
14:31:17.457289 /Public/Storage/VIRTIO-SCSI0/Port2/BytesRead  3503104 bytes
14:31:17.457294 /Public/Storage/VIRTIO-SCSI0/Port2/BytesWritten  1032192 bytes
14:31:17.457299 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufAttempts        0 count
14:31:17.457304 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufSuccess        0 count
14:31:17.457309 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsRead      856 count
14:31:17.457404 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSubmitted     1040 count
14:31:17.457411 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSucceeded     1040 count
14:31:17.457416 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsWrite      184 count
14:31:17.457421 /SELM/LoadHidSel/GstReadErrors          0 times
14:31:17.457426 /SELM/LoadHidSel/NoGoodGuest            0 times
14:31:17.457431 /TM/CPU/00/cNsExecuting          2620824161505 ns
14:31:17.457436 /TM/CPU/00/cNsHalted             13838907366498 ns
14:31:17.457441 /TM/CPU/00/cNsOther              35817503102373 ns
14:31:17.457446 /TM/CPU/00/cNsTotal              52277234630376 ns
14:31:17.457451 /TM/CPU/00/cPeriodsExecuting     62700492 count
14:31:17.457456 /TM/CPU/00/cPeriodsHalted         1434065 count
14:31:17.457461 /TM/CPU/00/pctExecuting                28 %
14:31:17.457465 /TM/CPU/00/pctHalted                    7 %
14:31:17.457470 /TM/CPU/00/pctOther                    64 %
14:31:17.457475 /TM/CPU/01/cNsExecuting          2650600107458 ns
14:31:17.457480 /TM/CPU/01/cNsHalted             14022607128237 ns
14:31:17.457485 /TM/CPU/01/cNsOther              35604027413516 ns
14:31:17.457490 /TM/CPU/01/cNsTotal              52277234649211 ns
14:31:17.457495 /TM/CPU/01/cPeriodsExecuting     52356316 count
14:31:17.457500 /TM/CPU/01/cPeriodsHalted         1466934 count
14:31:17.457505 /TM/CPU/01/pctExecuting                52 %
14:31:17.457509 /TM/CPU/01/pctHalted                    9 %
14:31:17.457514 /TM/CPU/01/pctOther                    38 %
14:31:17.457519 /TM/CPU/02/cNsExecuting          2593708197668 ns
14:31:17.457524 /TM/CPU/02/cNsHalted             17645526198359 ns
14:31:17.457529 /TM/CPU/02/cNsOther              32038000253187 ns
14:31:17.457534 /TM/CPU/02/cNsTotal              52277234649214 ns
14:31:17.457539 /TM/CPU/02/cPeriodsExecuting     49595715 count
14:31:17.457543 /TM/CPU/02/cPeriodsHalted         1358910 count
14:31:17.457548 /TM/CPU/02/pctExecuting                31 %
14:31:17.457553 /TM/CPU/02/pctHalted                   16 %
14:31:17.457558 /TM/CPU/02/pctOther                    51 %
14:31:17.457562 /TM/CPU/03/cNsExecuting          2561949604157 ns
14:31:17.457567 /TM/CPU/03/cNsHalted             34916239679601 ns
14:31:17.457572 /TM/CPU/03/cNsOther              14799045359825 ns
14:31:17.457577 /TM/CPU/03/cNsTotal              52277234643583 ns
14:31:17.457582 /TM/CPU/03/cPeriodsExecuting     56534470 count
14:31:17.457587 /TM/CPU/03/cPeriodsHalted         5593131 count
14:31:17.457592 /TM/CPU/03/pctExecuting                10 %
14:31:17.457596 /TM/CPU/03/pctHalted                   80 %
14:31:17.457601 /TM/CPU/03/pctOther                     8 %
14:31:17.457606 /TM/CPU/pctExecuting                   30 %
14:31:17.457611 /TM/CPU/pctHalted                      28 %
14:31:17.457615 /TM/CPU/pctOther                       40 %
14:31:17.457620 /TM/MaxHzHint                           0 Hz
14:31:17.457626 /TM/PIT/Handler                         0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
14:31:17.457631 /TM/PIT/Irq                             0 times
14:31:17.457636 /TM/R0/1nsSteps                   1196795 times
14:31:17.457641 /TM/R3/1nsSteps                   2847448 times
14:31:17.457646 /TM/TSC/offCPU0                  396749453861750 ticks
14:31:17.457651 /TM/TSC/offCPU1                  396749453861750 ticks
14:31:17.457657 /TM/TSC/offCPU2                  396749453861750 ticks
14:31:17.457662 /TM/TSC/offCPU3                  396749453861750 ticks
14:31:17.457667 /TM/VirtualSync/CurrentOffset    770503707349 ns
14:31:17.457672 /TM/VirtualSync/GivenUp          720192131750 ns
14:31:17.457677 ********************* End of statistics **********************
14:31:17.460314 NAT: Zone(nm:mbuf_cluster, used:0)
14:31:17.460951 NAT: Zone(nm:mbuf_packet, used:0)
14:31:17.460964 NAT: Zone(nm:mbuf, used:1)
14:31:17.461006 NAT: Zone(nm:mbuf_jumbo_pagesize, used:0)
14:31:17.461160 NAT: Zone(nm:mbuf_jumbo_9k, used:0)
14:31:17.461290 NAT: Zone(nm:mbuf_jumbo_16k, used:0)
14:31:17.461391 NAT: Zone(nm:mbuf_ext_refcnt, used:0)
14:31:17.461733 fastpipe: deviceDestruct g_bGuestPowerOff=1
14:31:17.466116 GIM: KVM: Resetting MSRs
14:31:17.471867 Changing the VM state from 'DESTROYING' to 'TERMINATED'
14:31:17.473960 Console: Machine state changed to 'PoweredOff'
14:31:17.474128 VBoxHeadless: processEventQueue: VERR_INTERRUPTED, termination requested
14:31:17.671672 VBoxHeadless: exiting
