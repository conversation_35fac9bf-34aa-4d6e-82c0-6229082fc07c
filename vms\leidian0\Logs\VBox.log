00:00:00.027453 VirtualBox VM 4.1.34 r2751252224 win.amd64 (Jul 26 2022 16:16:15) release log
00:00:00.027456 Log opened 2025-08-04T15:11:52.511352900Z
00:00:00.027457 Build Type: release
00:00:00.027462 OS Product: Windows 10
00:00:00.027463 OS Release: 10.0.19045
00:00:00.027464 OS Service Pack: 
00:00:00.044642 DMI Product Name: 10NBCTO1WW
00:00:00.050509 DMI Product Version: ThinkCentre M710t-N000
00:00:00.050528 Firmware type: UEFI
00:00:00.051048 Secure Boot: VERR_PRIVILEGE_NOT_HELD
00:00:00.051083 Host RAM: 32675MB (31.9GB) total, 19514MB (19.0GB) available
00:00:00.051087 Executable: C:\Program Files\ldplayer9box\Ld9BoxHeadless.exe
00:00:00.051088 Process ID: 21180
00:00:00.051088 Package type: WINDOWS_64BITS_GENERIC (OSE)
00:00:00.052998 Installed Extension Packs:
00:00:00.053023   None installed!
00:00:00.053639 Console: Machine state changed to 'Starting'
00:00:00.065668 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.065700 SUP: seg #1: R X 0x00001000 LB 0x00109000
00:00:00.065712 SUP: seg #2: R   0x0010a000 LB 0x0004a000
00:00:00.065719 SUP: seg #3: RW  0x00154000 LB 0x00013000
00:00:00.065727 SUP: seg #4: R   0x00167000 LB 0x0000e000
00:00:00.065734 SUP: seg #5: RW  0x00175000 LB 0x00003000
00:00:00.065744 SUP: seg #6: R   0x00178000 LB 0x0000b000
00:00:00.065754 SUP: seg #7: RWX 0x00183000 LB 0x00002000
00:00:00.065761 SUP: seg #8: R   0x00185000 LB 0x00007000
00:00:00.067373 SUP: Loaded Ld9VMMR0.r0 (C:\Program Files\ldplayer9box\Ld9VMMR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.067429 SUP: VMMR0EntryEx located at XXXXXXXXXXXXXXXX and VMMR0EntryFast at XXXXXXXXXXXXXXXX
00:00:00.067438 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9VMMR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.069821 Guest OS type: 'Linux26_64'
00:00:00.070825 fHMForced=true - No raw-mode support in this build!
00:00:00.073560 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\Snapshots' (snapshots) is unknown
00:00:00.073574 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk' is ntfs
00:00:00.074225 File system of 'D:\Program Files\LDPlayer9\system.vmdk' is ntfs
00:00:00.075109 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk' is ntfs
00:00:00.081442 Shared Clipboard: Service loaded
00:00:00.081462 Shared Clipboard: Mode: Off
00:00:00.081516 Shared Clipboard: Service running in headless mode
00:00:00.082487 Drag and drop service loaded
00:00:00.082507 Drag and drop mode: Off
00:00:00.084258 Extradata overrides:
00:00:00.084282   VBoxInternal/Devices/fastpipe/0/PCIBusNo="0"
00:00:00.084330   VBoxInternal/Devices/fastpipe/0/PCIDeviceNo="18"
00:00:00.084381   VBoxInternal/Devices/fastpipe/0/PCIFunctionNo="0"
00:00:00.084420   VBoxInternal/Devices/fastpipe/0/Trusted="1"
00:00:00.084460   VBoxInternal/PDM/Devices/fastpipe/Path="fastpipe.dll"
00:00:00.084705 ************************* CFGM dump *************************
00:00:00.084706 [/] (level 0)
00:00:00.084709   CpuExecutionCap   <integer> = 0x0000000000000064 (100)
00:00:00.084711   EnablePAE         <integer> = 0x0000000000000000 (0)
00:00:00.084711   HMEnabled         <integer> = 0x0000000000000001 (1)
00:00:00.084712   MemBalloonSize    <integer> = 0x0000000000000000 (0)
00:00:00.084712   Name              <string>  = "leidian0" (cb=9)
00:00:00.084713   NumCPUs           <integer> = 0x0000000000000004 (4)
00:00:00.084714   PageFusionAllowed <integer> = 0x0000000000000000 (0)
00:00:00.084714   RamHoleSize       <integer> = 0x0000000020000000 (536 870 912, 512 MB)
00:00:00.084716   RamSize           <integer> = 0x0000000180000000 (6 442 450 944, 6 144 MB, 6.0 GB)
00:00:00.084717   TimerMillies      <integer> = 0x000000000000000a (10)
00:00:00.084718   UUID              <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.084732 
00:00:00.084732 [/CPUM/] (level 1)
00:00:00.084733   GuestCpuName       <string>  = "host" (cb=5)
00:00:00.084734   NestedHWVirt       <integer> = 0x0000000000000000 (0)
00:00:00.084735   PortableCpuIdLevel <integer> = 0x0000000000000000 (0)
00:00:00.084735   SpecCtrl           <integer> = 0x0000000000000001 (1)
00:00:00.084736 
00:00:00.084736 [/CPUM/IsaExts/] (level 2)
00:00:00.084736 
00:00:00.084737 [/DBGC/] (level 1)
00:00:00.084737   GlobalInitScript <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-init" (cb=39)
00:00:00.084738   HistoryFile      <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-history" (cb=42)
00:00:00.084739   LocalInitScript  <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/dbgc-init" (cb=50)
00:00:00.084739 
00:00:00.084739 [/DBGF/] (level 1)
00:00:00.084740   Path <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/debug/;D:\Program Files\LDPlayer9\vms\leidian0/;cache*D:\Program Files\LDPlayer9\vms\leidian0/dbgcache/;C:\Users\<USER>\" (cb=159)
00:00:00.084741 
00:00:00.084741 [/Devices/] (level 1)
00:00:00.084741 
00:00:00.084741 [/Devices/8237A/] (level 2)
00:00:00.084742 
00:00:00.084742 [/Devices/8237A/0/] (level 3)
00:00:00.084743   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084743 
00:00:00.084744 [/Devices/GIMDev/] (level 2)
00:00:00.084744 
00:00:00.084744 [/Devices/GIMDev/0/] (level 3)
00:00:00.084745   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084745 
00:00:00.084745 [/Devices/VMMDev/] (level 2)
00:00:00.084746 
00:00:00.084746 [/Devices/VMMDev/0/] (level 3)
00:00:00.084751   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084752   PCIDeviceNo   <integer> = 0x0000000000000004 (4)
00:00:00.084752   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084753   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084753 
00:00:00.084753 [/Devices/VMMDev/0/Config/] (level 4)
00:00:00.084754   GuestCoreDumpDir <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\Snapshots" (cb=50)
00:00:00.084755 
00:00:00.084755 [/Devices/VMMDev/0/LUN#0/] (level 4)
00:00:00.084756   Driver <string>  = "HGCM" (cb=5)
00:00:00.084756 
00:00:00.084757 [/Devices/VMMDev/0/LUN#0/Config/] (level 5)
00:00:00.084758   Object <integer> = 0x0000000002b1eb20 (45 214 496)
00:00:00.084758 
00:00:00.084759 [/Devices/VMMDev/0/LUN#999/] (level 4)
00:00:00.084759   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084760 
00:00:00.084760 [/Devices/VMMDev/0/LUN#999/Config/] (level 5)
00:00:00.084761   First   <integer> = 0x0000000000000000 (0)
00:00:00.084761   Last    <integer> = 0x0000000000000000 (0)
00:00:00.084762   papLeds <integer> = 0x0000000002b14338 (45 171 512)
00:00:00.084762 
00:00:00.084762 [/Devices/acpi/] (level 2)
00:00:00.084763 
00:00:00.084763 [/Devices/acpi/0/] (level 3)
00:00:00.084764   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084764   PCIDeviceNo   <integer> = 0x0000000000000007 (7)
00:00:00.084765   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084765   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084766 
00:00:00.084766 [/Devices/acpi/0/Config/] (level 4)
00:00:00.084767   CpuHotPlug          <integer> = 0x0000000000000000 (0)
00:00:00.084768   FdcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.084768   HostBusPciAddress   <integer> = 0x0000000000000000 (0)
00:00:00.084769   HpetEnabled         <integer> = 0x0000000000000000 (0)
00:00:00.084769   IOAPIC              <integer> = 0x0000000000000001 (1)
00:00:00.084770   IocPciAddress       <integer> = 0x0000000000010000 (65 536)
00:00:00.084770   NumCPUs             <integer> = 0x0000000000000004 (4)
00:00:00.084771   Parallel0IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.084771   Parallel0Irq        <integer> = 0x0000000000000000 (0)
00:00:00.084772   Parallel1IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.084772   Parallel1Irq        <integer> = 0x0000000000000000 (0)
00:00:00.084773   Serial0IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.084773   Serial0Irq          <integer> = 0x0000000000000000 (0)
00:00:00.084774   Serial1IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.084774   Serial1Irq          <integer> = 0x0000000000000000 (0)
00:00:00.084775   ShowCpu             <integer> = 0x0000000000000001 (1)
00:00:00.084775   ShowRtc             <integer> = 0x0000000000000000 (0)
00:00:00.084776   SmcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.084776 
00:00:00.084776 [/Devices/acpi/0/LUN#0/] (level 4)
00:00:00.084777   Driver <string>  = "ACPIHost" (cb=9)
00:00:00.084777 
00:00:00.084778 [/Devices/acpi/0/LUN#0/Config/] (level 5)
00:00:00.084778 
00:00:00.084778 [/Devices/acpi/0/LUN#1/] (level 4)
00:00:00.084779   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084779 
00:00:00.084779 [/Devices/acpi/0/LUN#1/Config/] (level 5)
00:00:00.084780 
00:00:00.084780 [/Devices/acpi/0/LUN#2/] (level 4)
00:00:00.084781   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084781 
00:00:00.084782 [/Devices/acpi/0/LUN#2/Config/] (level 5)
00:00:00.084782 
00:00:00.084782 [/Devices/acpi/0/LUN#3/] (level 4)
00:00:00.084783   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.084783 
00:00:00.084784 [/Devices/acpi/0/LUN#3/Config/] (level 5)
00:00:00.084784 
00:00:00.084784 [/Devices/apic/] (level 2)
00:00:00.084785 
00:00:00.084785 [/Devices/apic/0/] (level 3)
00:00:00.084786   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084786 
00:00:00.084786 [/Devices/apic/0/Config/] (level 4)
00:00:00.084787   IOAPIC  <integer> = 0x0000000000000001 (1)
00:00:00.084788   Mode    <integer> = 0x0000000000000003 (3)
00:00:00.084788   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.084789 
00:00:00.084789 [/Devices/e1000/] (level 2)
00:00:00.084789 
00:00:00.084789 [/Devices/fastpipe/] (level 2)
00:00:00.084790 
00:00:00.084790 [/Devices/fastpipe/0/] (level 3)
00:00:00.084791   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084791   PCIDeviceNo   <integer> = 0x0000000000000012 (18)
00:00:00.084792   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084792   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084793 
00:00:00.084793 [/Devices/i8254/] (level 2)
00:00:00.084793 
00:00:00.084793 [/Devices/i8254/0/] (level 3)
00:00:00.084794 
00:00:00.084794 [/Devices/i8254/0/Config/] (level 4)
00:00:00.084795 
00:00:00.084795 [/Devices/i8259/] (level 2)
00:00:00.084796 
00:00:00.084796 [/Devices/i8259/0/] (level 3)
00:00:00.084796   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084797 
00:00:00.084797 [/Devices/i8259/0/Config/] (level 4)
00:00:00.084798 
00:00:00.084798 [/Devices/ioapic/] (level 2)
00:00:00.084798 
00:00:00.084798 [/Devices/ioapic/0/] (level 3)
00:00:00.084799   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084800 
00:00:00.084800 [/Devices/ioapic/0/Config/] (level 4)
00:00:00.084801   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.084801 
00:00:00.084801 [/Devices/mc146818/] (level 2)
00:00:00.084802 
00:00:00.084802 [/Devices/mc146818/0/] (level 3)
00:00:00.084802 
00:00:00.084802 [/Devices/mc146818/0/Config/] (level 4)
00:00:00.084803   UseUTC <integer> = 0x0000000000000001 (1)
00:00:00.084804 
00:00:00.084804 [/Devices/parallel/] (level 2)
00:00:00.084804 
00:00:00.084804 [/Devices/pcarch/] (level 2)
00:00:00.084805 
00:00:00.084805 [/Devices/pcarch/0/] (level 3)
00:00:00.084805   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084806 
00:00:00.084806 [/Devices/pcarch/0/Config/] (level 4)
00:00:00.084807 
00:00:00.084807 [/Devices/pcbios/] (level 2)
00:00:00.084807 
00:00:00.084807 [/Devices/pcbios/0/] (level 3)
00:00:00.084808   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084808 
00:00:00.084808 [/Devices/pcbios/0/Config/] (level 4)
00:00:00.084809   APIC           <integer> = 0x0000000000000001 (1)
00:00:00.084810   BootDevice0    <string>  = "IDE" (cb=4)
00:00:00.084810   BootDevice1    <string>  = "NONE" (cb=5)
00:00:00.084811   BootDevice2    <string>  = "NONE" (cb=5)
00:00:00.084812   BootDevice3    <string>  = "NONE" (cb=5)
00:00:00.084812   FloppyDevice   <string>  = "i82078" (cb=7)
00:00:00.084813   HardDiskDevice <string>  = "piix3ide" (cb=9)
00:00:00.084813   IOAPIC         <integer> = 0x0000000000000001 (1)
00:00:00.084814   McfgBase       <integer> = 0x0000000000000000 (0)
00:00:00.084814   McfgLength     <integer> = 0x0000000000000000 (0)
00:00:00.084815   NumCPUs        <integer> = 0x0000000000000004 (4)
00:00:00.084815   PXEDebug       <integer> = 0x0000000000000000 (0)
00:00:00.084816   UUID           <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.084817   UuidLe         <integer> = 0x0000000000000000 (0)
00:00:00.084817 
00:00:00.084817 [/Devices/pcbios/0/Config/NetBoot/] (level 5)
00:00:00.084818 
00:00:00.084818 [/Devices/pcbios/0/Config/NetBoot/0/] (level 6)
00:00:00.084819   NIC           <integer> = 0x0000000000000000 (0)
00:00:00.084820   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084820   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.084821   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084821 
00:00:00.084821 [/Devices/pci/] (level 2)
00:00:00.084822 
00:00:00.084822 [/Devices/pci/0/] (level 3)
00:00:00.084822   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084823 
00:00:00.084823 [/Devices/pci/0/Config/] (level 4)
00:00:00.084823   IOAPIC <integer> = 0x0000000000000001 (1)
00:00:00.084824 
00:00:00.084824 [/Devices/pcibridge/] (level 2)
00:00:00.084825 
00:00:00.084825 [/Devices/pckbd/] (level 2)
00:00:00.084825 
00:00:00.084825 [/Devices/pckbd/0/] (level 3)
00:00:00.084826   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.084826 
00:00:00.084827 [/Devices/pckbd/0/Config/] (level 4)
00:00:00.084827 
00:00:00.084827 [/Devices/pckbd/0/LUN#0/] (level 4)
00:00:00.084828   Driver <string>  = "KeyboardQueue" (cb=14)
00:00:00.084829 
00:00:00.084829 [/Devices/pckbd/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.084829   Driver <string>  = "MainKeyboard" (cb=13)
00:00:00.084830 
00:00:00.084830 [/Devices/pckbd/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.084831   Object <integer> = 0x0000000002b17a50 (45 185 616)
00:00:00.084832 
00:00:00.084832 [/Devices/pckbd/0/LUN#0/Config/] (level 5)
00:00:00.084832   QueueSize <integer> = 0x0000000000000040 (64)
00:00:00.084833 
00:00:00.084833 [/Devices/pckbd/0/LUN#1/] (level 4)
00:00:00.084834   Driver <string>  = "MouseQueue" (cb=11)
00:00:00.084834 
00:00:00.084834 [/Devices/pckbd/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.084835   Driver <string>  = "MainMouse" (cb=10)
00:00:00.084835 
00:00:00.084836 [/Devices/pckbd/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.084836   Object <integer> = 0x0000000002b18110 (45 187 344)
00:00:00.084837 
00:00:00.084837 [/Devices/pckbd/0/LUN#1/Config/] (level 5)
00:00:00.084838   QueueSize <integer> = 0x0000000000000080 (128)
00:00:00.084838 
00:00:00.084838 [/Devices/pcnet/] (level 2)
00:00:00.084839 
00:00:00.084839 [/Devices/serial/] (level 2)
00:00:00.084840 
00:00:00.084840 [/Devices/virtio-net/] (level 2)
00:00:00.084841 
00:00:00.084841 [/Devices/virtio-net/0/] (level 3)
00:00:00.084841   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084842   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.084842   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084843   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084843 
00:00:00.084843 [/Devices/virtio-net/0/Config/] (level 4)
00:00:00.084844   CableConnected <integer> = 0x0000000000000001 (1)
00:00:00.084845   LineSpeed      <integer> = 0x0000000000000000 (0)
00:00:00.084845   MAC            <bytes>   = "00 db 30 ef a7 99" (cb=6)
00:00:00.084846 
00:00:00.084846 [/Devices/virtio-net/0/LUN#0/] (level 4)
00:00:00.084847   Driver <string>  = "NAT" (cb=4)
00:00:00.084847 
00:00:00.084847 [/Devices/virtio-net/0/LUN#0/Config/] (level 5)
00:00:00.084848   AliasMode       <integer> = 0x0000000000000000 (0)
00:00:00.084848   BootFile        <string>  = "leidian0.pxe" (cb=13)
00:00:00.084849   DNSProxy        <integer> = 0x0000000000000000 (0)
00:00:00.084849   Network         <string>  = "**********/24" (cb=14)
00:00:00.084850   PassDomain      <integer> = 0x0000000000000001 (1)
00:00:00.084850   SockRcv         <integer> = 0x0000000000020000 (131 072)
00:00:00.084851   SockSnd         <integer> = 0x0000000000020000 (131 072)
00:00:00.084852   TFTPPrefix      <string>  = "C:\Users\<USER>\.Ld9VirtualBox\TFTP" (cb=34)
00:00:00.084852   UseHostResolver <integer> = 0x0000000000000000 (0)
00:00:00.084853 
00:00:00.084853 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/] (level 6)
00:00:00.084854 
00:00:00.084854 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/0/] (level 7)
00:00:00.084855   GuestPort <integer> = 0x00000000000008ae (2 222)
00:00:00.084855   HostPort  <integer> = 0x00000000000008ae (2 222)
00:00:00.084856   Name      <string>  = "tcp_2222_2222" (cb=14)
00:00:00.084856   Protocol  <string>  = "TCP" (cb=4)
00:00:00.084857 
00:00:00.084857 [/Devices/virtio-net/0/LUN#0/Config/PortForwarding/1/] (level 7)
00:00:00.084858   GuestPort <integer> = 0x00000000000015b3 (5 555)
00:00:00.084858   HostPort  <integer> = 0x00000000000015b3 (5 555)
00:00:00.084859   Name      <string>  = "tcp_5555_5555" (cb=14)
00:00:00.084859   Protocol  <string>  = "TCP" (cb=4)
00:00:00.084860 
00:00:00.084860 [/Devices/virtio-net/0/LUN#999/] (level 4)
00:00:00.084860   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084861 
00:00:00.084861 [/Devices/virtio-net/0/LUN#999/Config/] (level 5)
00:00:00.084862   First   <integer> = 0x0000000000000000 (0)
00:00:00.084862   Last    <integer> = 0x0000000000000000 (0)
00:00:00.084862   papLeds <integer> = 0x0000000002b14218 (45 171 224)
00:00:00.084863 
00:00:00.084863 [/Devices/virtio-scsi/] (level 2)
00:00:00.084864 
00:00:00.084864 [/Devices/virtio-scsi/0/] (level 3)
00:00:00.084865   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.084865   PCIDeviceNo   <integer> = 0x000000000000000f (15)
00:00:00.084866   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.084866   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.084866 
00:00:00.084866 [/Devices/virtio-scsi/0/Config/] (level 4)
00:00:00.084867   Bootable   <integer> = 0x0000000000000001 (1)
00:00:00.084868   NumTargets <integer> = 0x0000000000000003 (3)
00:00:00.084868 
00:00:00.084868 [/Devices/virtio-scsi/0/LUN#0/] (level 4)
00:00:00.084869   Driver <string>  = "SCSI" (cb=5)
00:00:00.084869 
00:00:00.084869 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.084870   Driver <string>  = "VD" (cb=3)
00:00:00.084870 
00:00:00.084871 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.084871   Format    <string>  = "VMDK" (cb=5)
00:00:00.084872   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084872   Path      <string>  = "D:\Program Files\LDPlayer9\system.vmdk" (cb=39)
00:00:00.084873   ReadOnly  <integer> = 0x0000000000000001 (1)
00:00:00.084873   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084874 
00:00:00.084874 [/Devices/virtio-scsi/0/LUN#1/] (level 4)
00:00:00.084874   Driver <string>  = "SCSI" (cb=5)
00:00:00.084875 
00:00:00.084875 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.084876   Driver <string>  = "VD" (cb=3)
00:00:00.084876 
00:00:00.084876 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.084877   Format    <string>  = "VMDK" (cb=5)
00:00:00.084877   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084878   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk" (cb=50)
00:00:00.084878   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084878 
00:00:00.084879 [/Devices/virtio-scsi/0/LUN#2/] (level 4)
00:00:00.084879   Driver <string>  = "SCSI" (cb=5)
00:00:00.084880 
00:00:00.084880 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/] (level 5)
00:00:00.084880   Driver <string>  = "VD" (cb=3)
00:00:00.084881 
00:00:00.084881 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/Config/] (level 6)
00:00:00.084882   Format    <string>  = "VMDK" (cb=5)
00:00:00.084882   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.084882   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk" (cb=52)
00:00:00.084883   Type      <string>  = "HardDisk" (cb=9)
00:00:00.084883 
00:00:00.084883 [/Devices/virtio-scsi/0/LUN#999/] (level 4)
00:00:00.084884   Driver <string>  = "MainStatus" (cb=11)
00:00:00.084884 
00:00:00.084885 [/Devices/virtio-scsi/0/LUN#999/Config/] (level 5)
00:00:00.084885   DeviceInstance        <string>  = "virtio-scsi/0" (cb=14)
00:00:00.084886   First                 <integer> = 0x0000000000000000 (0)
00:00:00.084886   Last                  <integer> = 0x0000000000000002 (2)
00:00:00.084887   pConsole              <integer> = 0x0000000002b12f40 (45 166 400)
00:00:00.084888   papLeds               <integer> = 0x0000000002b13a20 (45 169 184)
00:00:00.084888   pmapMediumAttachments <integer> = 0x0000000002b14358 (45 171 544)
00:00:00.084889 
00:00:00.084889 [/EM/] (level 1)
00:00:00.084890   TripleFaultReset <integer> = 0x0000000000000000 (0)
00:00:00.084890 
00:00:00.084890 [/GIM/] (level 1)
00:00:00.084891   Provider <string>  = "KVM" (cb=4)
00:00:00.084891 
00:00:00.084891 [/HM/] (level 1)
00:00:00.084892   64bitEnabled       <integer> = 0x0000000000000001 (1)
00:00:00.084893   EnableLargePages   <integer> = 0x0000000000000001 (1)
00:00:00.084893   EnableNestedPaging <integer> = 0x0000000000000001 (1)
00:00:00.084894   EnableUX           <integer> = 0x0000000000000001 (1)
00:00:00.084894   EnableVPID         <integer> = 0x0000000000000001 (1)
00:00:00.084894   Exclusive          <integer> = 0x0000000000000000 (0)
00:00:00.084895   HMForced           <integer> = 0x0000000000000001 (1)
00:00:00.084895   IBPBOnVMEntry      <integer> = 0x0000000000000000 (0)
00:00:00.084896   IBPBOnVMExit       <integer> = 0x0000000000000000 (0)
00:00:00.084896   L1DFlushOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.084897   L1DFlushOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.084897   MDSClearOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.084898   MDSClearOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.084898   SpecCtrlByHost     <integer> = 0x0000000000000000 (0)
00:00:00.084898   UseNEMInstead      <integer> = 0x0000000000000000 (0)
00:00:00.084899 
00:00:00.084899 [/MM/] (level 1)
00:00:00.084900   CanUseLargerHeap <integer> = 0x0000000000000000 (0)
00:00:00.084900 
00:00:00.084900 [/NEM/] (level 1)
00:00:00.084901   Allow64BitGuests <integer> = 0x0000000000000001 (1)
00:00:00.084901 
00:00:00.084901 [/PDM/] (level 1)
00:00:00.084901 
00:00:00.084902 [/PDM/AsyncCompletion/] (level 2)
00:00:00.084902 
00:00:00.084902 [/PDM/AsyncCompletion/File/] (level 3)
00:00:00.084903 
00:00:00.084903 [/PDM/AsyncCompletion/File/BwGroups/] (level 4)
00:00:00.084904 
00:00:00.084904 [/PDM/BlkCache/] (level 2)
00:00:00.084904   CacheSize <integer> = 0x0000000000500000 (5 242 880, 5 MB)
00:00:00.084905 
00:00:00.084906 [/PDM/Devices/] (level 2)
00:00:00.084906 
00:00:00.084906 [/PDM/Devices/fastpipe/] (level 3)
00:00:00.084907   Path <string>  = "fastpipe.dll" (cb=13)
00:00:00.084908 
00:00:00.084908 [/PDM/Drivers/] (level 2)
00:00:00.084908 
00:00:00.084908 [/PDM/Drivers/VBoxC/] (level 3)
00:00:00.084909   Path <string>  = "VBoxC" (cb=6)
00:00:00.084909 
00:00:00.084909 [/PDM/NetworkShaper/] (level 2)
00:00:00.084910 
00:00:00.084910 [/PDM/NetworkShaper/BwGroups/] (level 3)
00:00:00.084911 
00:00:00.084911 [/TM/] (level 1)
00:00:00.084911   UTCOffset <integer> = 0x0000000000000000 (0)
00:00:00.084912 
00:00:00.084912 ********************* End of CFGM dump **********************
00:00:00.085081 HM: HMR3Init: VT-x w/ nested paging and unrestricted guest execution hw support
00:00:00.085223 MM: cbHyperHeap=0x840000 (8650752)
00:00:00.096725 CPUM: fXStateHostMask=0x7; initial: 0x7; host XCR0=0x1f
00:00:00.098658 CPUM: Matched host CPU INTEL 0x6/0x9e/0x9 Intel_Core7_KabyLake with CPU DB entry 'Intel Core i7-6700K' (INTEL 0x6/0x5e/0x3 Intel_Core7_Skylake)
00:00:00.098776 CPUM: MXCSR_MASK=0xffff (host: 0xffff)
00:00:00.098803 CPUM: Microcode revision 0x000000B4
00:00:00.098821 CPUM: Changing leaf 13[0]: EBX=0x440 -> 0x340, ECX=0x440 -> 0x340
00:00:00.098844 CPUM: MSR/CPUID reconciliation insert: 0x0000010b IA32_FLUSH_CMD
00:00:00.098896 CPUM: SetGuestCpuIdFeature: Enabled Speculation Control.
00:00:00.099155 PGM: Host paging mode: AMD64+NX
00:00:00.099171 PGM: PGMPool: cMaxPages=3328 (u64MaxPages=3110)
00:00:00.099181 PGM: pgmR3PoolInit: cMaxPages=0xd00 cMaxUsers=0x1a00 cMaxPhysExts=0x1a00 fCacheEnable=true 
00:00:00.134337 TM: GIP - u32Mode=3 (Invariant) u32UpdateHz=93 u32UpdateIntervalNS=10734900 enmUseTscDelta=2 (Practically Zero) fGetGipCpu=0x1b cCpus=8
00:00:00.134405 TM: GIP - u64CpuHz=3 600 001 324 (0xd693a92c)  SUPGetCpuHzFromGip => 3 600 001 324
00:00:00.134414 TM: GIP - CPU: iCpuSet=0x0 idCpu=0x0 idApic=0x0 iGipCpu=0x6 i64TSCDelta=0 enmState=3 u64CpuHz=3600001113(*) cErrors=0
00:00:00.134420 TM: GIP - CPU: iCpuSet=0x1 idCpu=0x1 idApic=0x1 iGipCpu=0x7 i64TSCDelta=0 enmState=3 u64CpuHz=3600001264(*) cErrors=0
00:00:00.134427 TM: GIP - CPU: iCpuSet=0x2 idCpu=0x2 idApic=0x2 iGipCpu=0x3 i64TSCDelta=0 enmState=3 u64CpuHz=3599955467(*) cErrors=0
00:00:00.134432 TM: GIP - CPU: iCpuSet=0x3 idCpu=0x3 idApic=0x3 iGipCpu=0x4 i64TSCDelta=0 enmState=3 u64CpuHz=3600000949(*) cErrors=0
00:00:00.134437 TM: GIP - CPU: iCpuSet=0x4 idCpu=0x4 idApic=0x4 iGipCpu=0x5 i64TSCDelta=0 enmState=3 u64CpuHz=3600001155(*) cErrors=0
00:00:00.134443 TM: GIP - CPU: iCpuSet=0x5 idCpu=0x5 idApic=0x5 iGipCpu=0x0 i64TSCDelta=0 enmState=3 u64CpuHz=3600001324(*) cErrors=0
00:00:00.134449 TM: GIP - CPU: iCpuSet=0x6 idCpu=0x6 idApic=0x6 iGipCpu=0x2 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.134454 TM: GIP - CPU: iCpuSet=0x7 idCpu=0x7 idApic=0x7 iGipCpu=0x1 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.134519 TM: cTSCTicksPerSecond=3 600 001 324 (0xd693a92c) enmTSCMode=1 (VirtTscEmulated)
00:00:00.134522 TM: TSCTiedToExecution=false TSCNotTiedToHalt=false
00:00:00.135330 EMR3Init: fIemExecutesAll=false fGuruOnTripleFault=true 
00:00:00.136030 IEM: TargetCpu=CURRENT, Microarch=Intel_Core7_KabyLake
00:00:00.136295 GIM: Using provider 'KVM' (Implementation version: 0)
00:00:00.136310 CPUM: SetGuestCpuIdFeature: Enabled Hypervisor Present bit
00:00:00.136405 AIOMgr: Default manager type is 'Async'
00:00:00.136413 AIOMgr: Default file backend is 'NonBuffered'
00:00:00.136636 BlkCache: Cache successfully initialized. Cache size is 5242880 bytes
00:00:00.136656 BlkCache: Cache commit interval is 10000 ms
00:00:00.136663 BlkCache: Cache commit threshold is 2621440 bytes
00:00:00.138721 fastpipe::VBoxDevicesRegister: u32Version=0x60001 pCallbacks->u32Version=0xffe30010
00:00:00.138895 PcBios: [SMP] BIOS with 4 CPUs
00:00:00.138920 PcBios: Using the 386+ BIOS image.
00:00:00.138997 PcBios: MPS table at 000e1300
00:00:00.141247 PcBios: fCheckShutdownStatusForSoftReset=true   fClearShutdownStatusOnHardReset=true 
00:00:00.144919 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.144935 SUP: seg #1: R X 0x00001000 LB 0x0001f000
00:00:00.144941 SUP: seg #2: R   0x00020000 LB 0x0000c000
00:00:00.144946 SUP: seg #3: RW  0x0002c000 LB 0x00001000
00:00:00.144951 SUP: seg #4: R   0x0002d000 LB 0x00002000
00:00:00.144956 SUP: seg #5: RW  0x0002f000 LB 0x00001000
00:00:00.144961 SUP: seg #6: R   0x00030000 LB 0x00001000
00:00:00.144966 SUP: seg #7: RWX 0x00031000 LB 0x00001000
00:00:00.144971 SUP: seg #8: R   0x00032000 LB 0x00002000
00:00:00.145023 SUP: Loaded Ld9BoxDDR0.r0 (C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.145032 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.145318 CPUM: SetGuestCpuIdFeature: Enabled xAPIC
00:00:00.145329 CPUM: SetGuestCpuIdFeature: Enabled x2APIC
00:00:00.145679 IOAPIC: Using implementation 2.0! Chipset type ICH9
00:00:00.145770 PIT: mode=3 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.146021 VMMDev: cbDefaultBudget: 535 351 424 (1fe8d080)
00:00:00.152692 Shared Folders service loaded
00:00:00.153261 Guest Control service loaded
00:00:00.154032 VIRTIOSCSI0: Targets=3 Bootable=true  (unimplemented) R0Enabled=true  RCEnabled=false
00:00:00.154704 DrvVD: Flushes will be ignored
00:00:00.154729 DrvVD: Async flushes will be passed to the disk
00:00:00.156241 VD: VDInit finished with VINF_SUCCESS
00:00:00.156470 VD: Opening the disk took 634074 ns
00:00:00.156609 DrvVD: Flushes will be ignored
00:00:00.156620 DrvVD: Async flushes will be passed to the disk
00:00:00.171138 VD: Opening the disk took 13478944 ns
00:00:00.171312 DrvVD: Flushes will be ignored
00:00:00.171320 DrvVD: Async flushes will be passed to the disk
00:00:00.182969 VD: Opening the disk took 10817644 ns
00:00:00.190734 NAT: Guest address guess set to *********** by initialization
00:00:00.196878 NAT: DNS#0: *******
00:00:00.196938 NAT: DNS#1: *******
00:00:00.196957 NAT: ("SOCKET_RCVBUF":131072) has been ignored, because out of range (8, 1024)
00:00:00.196965 NAT: ("SOCKET_SNDBUF":131072) has been ignored, because out of range (8, 1024)
00:00:00.197971 NAT: Set redirect TCP 0.0.0.0:2222 -> 0.0.0.0:2222
00:00:00.198067 NAT: Set redirect TCP 0.0.0.0:5555 -> 0.0.0.0:5555
00:00:00.205419 fastpipe: load host successs mod=0000000006227850, path=C:\Program Files\ldplayer9box\libOpenglRender.dll
00:00:00.205440 fastpipe: GetFunctionAddr success mod=0000000006227850, lpszFuncName=OnLoad
00:00:00.205838 fastpipe: load host successs mod=0000000006227a90, path=C:\Program Files\ldplayer9box\host_manager.dll
00:00:00.205851 fastpipe: GetFunctionAddr success mod=0000000006227a90, lpszFuncName=OnLoad
00:00:00.207695 PGM: The CPU physical address width is 39 bits
00:00:00.207714 PGM: PGMR3InitFinalize: 4 MB PSE mask 0000007fffffffff -> VINF_SUCCESS
00:00:00.207901 TM: TMR3InitFinalize: fTSCModeSwitchAllowed=true 
00:00:00.208173 VMM: Thread-context hooks unavailable
00:00:00.208189 VMM: RTThreadPreemptIsPending() can be trusted
00:00:00.208197 VMM: Kernel preemption is possible
00:00:00.211013 HM: fWorldSwitcher=0x0 (fIbpbOnVmExit=false fIbpbOnVmEntry=false fL1dFlushOnVmEntry=false); fL1dFlushOnSched=false fMdsClearOnVmEntry=false
00:00:00.211031 HM: Using VT-x implementation 3.0
00:00:00.211036 HM: Max resume loops                  = 8192
00:00:00.211036 HM: Host CR4                          = 0x370678
00:00:00.211037 HM: Host EFER                         = 0xd01
00:00:00.211038 HM: MSR_IA32_SMM_MONITOR_CTL          = 0x0
00:00:00.211038 HM: MSR_IA32_FEATURE_CONTROL          = 0x5
00:00:00.211039 HM:   LOCK
00:00:00.211039 HM:   VMXON
00:00:00.211039 HM: MSR_IA32_VMX_BASIC                = 0xda040000000004
00:00:00.211040 HM:   VMCS id                           = 0x4
00:00:00.211042 HM:   VMCS size                         = 1024 bytes
00:00:00.211042 HM:   VMCS physical address limit       = None
00:00:00.211043 HM:   VMCS memory type                  = Write Back (WB)
00:00:00.211043 HM:   Dual-monitor treatment support    = true 
00:00:00.211044 HM:   OUTS & INS instruction-info       = true 
00:00:00.211044 HM:   Supports true-capability MSRs     = true 
00:00:00.211044 HM:   VM-entry Xcpt error-code optional = false
00:00:00.211046 HM: MSR_IA32_VMX_PINBASED_CTLS        = 0x7f00000016
00:00:00.211047 HM:   EXT_INT_EXIT
00:00:00.211047 HM:   NMI_EXIT
00:00:00.211047 HM:   VIRTUAL_NMI
00:00:00.211047 HM:   PREEMPT_TIMER
00:00:00.211048 HM:   POSTED_INT (must be cleared)
00:00:00.211048 HM: MSR_IA32_VMX_PROCBASED_CTLS       = 0xfff9fffe0401e172
00:00:00.211049 HM:   INT_WINDOW_EXIT
00:00:00.211049 HM:   USE_TSC_OFFSETTING
00:00:00.211049 HM:   HLT_EXIT
00:00:00.211050 HM:   INVLPG_EXIT
00:00:00.211050 HM:   MWAIT_EXIT
00:00:00.211050 HM:   RDPMC_EXIT
00:00:00.211051 HM:   RDTSC_EXIT
00:00:00.211051 HM:   CR3_LOAD_EXIT (must be set)
00:00:00.211051 HM:   CR3_STORE_EXIT (must be set)
00:00:00.211051 HM:   CR8_LOAD_EXIT
00:00:00.211052 HM:   CR8_STORE_EXIT
00:00:00.211052 HM:   USE_TPR_SHADOW
00:00:00.211052 HM:   NMI_WINDOW_EXIT
00:00:00.211052 HM:   MOV_DR_EXIT
00:00:00.211053 HM:   UNCOND_IO_EXIT
00:00:00.211053 HM:   USE_IO_BITMAPS
00:00:00.211053 HM:   MONITOR_TRAP_FLAG
00:00:00.211053 HM:   USE_MSR_BITMAPS
00:00:00.211054 HM:   MONITOR_EXIT
00:00:00.211054 HM:   PAUSE_EXIT
00:00:00.211054 HM:   USE_SECONDARY_CTLS
00:00:00.211056 HM: MSR_IA32_VMX_PROCBASED_CTLS2      = 0x5ffcff00000000
00:00:00.211057 HM:   VIRT_APIC_ACCESS
00:00:00.211057 HM:   EPT
00:00:00.211057 HM:   DESC_TABLE_EXIT
00:00:00.211058 HM:   RDTSCP
00:00:00.211058 HM:   VIRT_X2APIC_MODE
00:00:00.211058 HM:   VPID
00:00:00.211058 HM:   WBINVD_EXIT
00:00:00.211058 HM:   UNRESTRICTED_GUEST
00:00:00.211059 HM:   APIC_REG_VIRT (must be cleared)
00:00:00.211059 HM:   VIRT_INT_DELIVERY (must be cleared)
00:00:00.211059 HM:   PAUSE_LOOP_EXIT
00:00:00.211060 HM:   RDRAND_EXIT
00:00:00.211060 HM:   INVPCID
00:00:00.211060 HM:   VMFUNC
00:00:00.211062 HM:   VMCS_SHADOWING
00:00:00.211062 HM:   ENCLS_EXIT
00:00:00.211062 HM:   RDSEED_EXIT
00:00:00.211062 HM:   PML
00:00:00.211063 HM:   EPT_VE
00:00:00.211063 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211063 HM:   XSAVES_XRSTORS
00:00:00.211063 HM:   MODE_BASED_EPT_PERM
00:00:00.211064 HM:   SPPTP_EPT (must be cleared)
00:00:00.211064 HM:   PT_EPT (must be cleared)
00:00:00.211064 HM:   TSC_SCALING (must be cleared)
00:00:00.211064 HM:   USER_WAIT_PAUSE (must be cleared)
00:00:00.211065 HM:   ENCLV_EXIT (must be cleared)
00:00:00.211065 HM: MSR_IA32_VMX_ENTRY_CTLS           = 0x3ffff000011ff
00:00:00.211065 HM:   LOAD_DEBUG (must be set)
00:00:00.211066 HM:   IA32E_MODE_GUEST
00:00:00.211066 HM:   ENTRY_TO_SMM
00:00:00.211066 HM:   DEACTIVATE_DUAL_MON
00:00:00.211066 HM:   LOAD_PERF_MSR
00:00:00.211067 HM:   LOAD_PAT_MSR
00:00:00.211067 HM:   LOAD_EFER_MSR
00:00:00.211067 HM:   LOAD_BNDCFGS_MSR
00:00:00.211067 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211067 HM:   LOAD_RTIT_CTL_MSR (must be cleared)
00:00:00.211068 HM: MSR_IA32_VMX_EXIT_CTLS            = 0x1ffffff00036dff
00:00:00.211068 HM:   SAVE_DEBUG (must be set)
00:00:00.211069 HM:   HOST_ADDR_SPACE_SIZE
00:00:00.211069 HM:   LOAD_PERF_MSR
00:00:00.211069 HM:   ACK_EXT_INT
00:00:00.211069 HM:   SAVE_PAT_MSR
00:00:00.211069 HM:   LOAD_PAT_MSR
00:00:00.211070 HM:   SAVE_EFER_MSR
00:00:00.211070 HM:   LOAD_EFER_MSR
00:00:00.211070 HM:   SAVE_PREEMPT_TIMER
00:00:00.211070 HM:   CLEAR_BNDCFGS_MSR
00:00:00.211071 HM:   CONCEAL_VMX_FROM_PT
00:00:00.211071 HM:   CLEAR_RTIT_CTL_MSR (must be cleared)
00:00:00.211071 HM: MSR_IA32_VMX_TRUE_PINBASED_CTLS   = 0x7f00000016
00:00:00.211072 HM: MSR_IA32_VMX_TRUE_PROCBASED_CTLS  = 0xfff9fffe04006172
00:00:00.211072 HM: MSR_IA32_VMX_TRUE_ENTRY_CTLS      = 0x3ffff000011fb
00:00:00.211073 HM: MSR_IA32_VMX_TRUE_EXIT_CTLS       = 0x1ffffff00036dfb
00:00:00.211075 HM: MSR_IA32_VMX_MISC                 = 0x7004c1e7
00:00:00.211075 HM:   PREEMPT_TIMER_TSC                 = 0x7
00:00:00.211076 HM:   EXIT_SAVE_EFER_LMA                = true 
00:00:00.211076 HM:   ACTIVITY_STATES                   = 0x7 ( HLT SHUTDOWN SIPI_WAIT )
00:00:00.211077 HM:   INTEL_PT                          = true 
00:00:00.211077 HM:   SMM_READ_SMBASE_MSR               = true 
00:00:00.211077 HM:   CR3_TARGET                        = 0x4
00:00:00.211078 HM:   MAX_MSR                           = 0x0 ( 512 )
00:00:00.211078 HM:   VMXOFF_BLOCK_SMI                  = true 
00:00:00.211078 HM:   VMWRITE_ALL                       = true 
00:00:00.211079 HM:   ENTRY_INJECT_SOFT_INT             = 0x1
00:00:00.211079 HM:   MSEG_ID                           = 0x0
00:00:00.211079 HM: MSR_IA32_VMX_VMCS_ENUM            = 0x2e
00:00:00.211080 HM:   HIGHEST_IDX                       = 0x17
00:00:00.211080 HM: MSR_IA32_VMX_EPT_VPID_CAP         = 0xf0106734141
00:00:00.211080 HM:   RWX_X_ONLY
00:00:00.211081 HM:   PAGE_WALK_LENGTH_4
00:00:00.211081 HM:   EMT_UC
00:00:00.211081 HM:   EMT_WB
00:00:00.211081 HM:   PDE_2M
00:00:00.211081 HM:   PDPTE_1G
00:00:00.211082 HM:   INVEPT
00:00:00.211082 HM:   EPT_ACCESS_DIRTY
00:00:00.211082 HM:   ADVEXITINFO_EPT
00:00:00.211082 HM:   INVEPT_SINGLE_CONTEXT
00:00:00.211083 HM:   INVEPT_ALL_CONTEXTS
00:00:00.211083 HM:   INVVPID
00:00:00.211083 HM:   INVVPID_INDIV_ADDR
00:00:00.211083 HM:   INVVPID_SINGLE_CONTEXT
00:00:00.211083 HM:   INVVPID_ALL_CONTEXTS
00:00:00.211084 HM:   INVVPID_SINGLE_CONTEXT_RETAIN_GLOBALS
00:00:00.211084 HM: MSR_IA32_VMX_VMFUNC               = 0x1
00:00:00.211084 HM:   EPTP_SWITCHING
00:00:00.211085 HM: MSR_IA32_VMX_CR0_FIXED0           = 0x80000021
00:00:00.211085 HM: MSR_IA32_VMX_CR0_FIXED1           = 0xffffffff
00:00:00.211085 HM: MSR_IA32_VMX_CR4_FIXED0           = 0x2000
00:00:00.211086 HM: MSR_IA32_VMX_CR4_FIXED1           = 0x3767ff
00:00:00.211086 HM: APIC-access page physaddr         = 0x000000054be98000
00:00:00.211087 HM: VCPU  0: MSR bitmap physaddr      = 0x00000007c159c000
00:00:00.211088 HM: VCPU  0: VMCS physaddr            = 0x00000007f1899000
00:00:00.211089 HM: VCPU  1: MSR bitmap physaddr      = 0x000000075ada0000
00:00:00.211089 HM: VCPU  1: VMCS physaddr            = 0x000000013d49d000
00:00:00.211090 HM: VCPU  2: MSR bitmap physaddr      = 0x000000012e3a4000
00:00:00.211090 HM: VCPU  2: VMCS physaddr            = 0x00000001861a1000
00:00:00.211091 HM: VCPU  3: MSR bitmap physaddr      = 0x00000003d5da8000
00:00:00.211091 HM: VCPU  3: VMCS physaddr            = 0x00000007fdda5000
00:00:00.211092 HM: Guest support: 32-bit and 64-bit
00:00:00.211101 HM: Supports VMCS EFER fields         = true 
00:00:00.211102 HM: Enabled VMX
00:00:00.211105 CPUM: SetGuestCpuIdFeature: Enabled SYSENTER/EXIT
00:00:00.211105 CPUM: SetGuestCpuIdFeature: Enabled PAE
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled LONG MODE
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled SYSCALL/RET
00:00:00.211106 CPUM: SetGuestCpuIdFeature: Enabled LAHF/SAHF
00:00:00.211107 CPUM: SetGuestCpuIdFeature: Enabled NX
00:00:00.211107 HM: Enabled nested paging
00:00:00.211107 HM:   EPT flush type                  = Single context
00:00:00.211108 HM: Enabled unrestricted guest execution
00:00:00.211108 HM: Enabled large page support
00:00:00.211108 HM: Enabled VPID
00:00:00.211108 HM:   VPID flush type                 = Single context
00:00:00.211109 HM: Enabled VMX-preemption timer (cPreemptTimerShift=7)
00:00:00.211109 HM: VT-x/AMD-V init method: Local
00:00:00.211111 EM: Exit history optimizations: enabled=true  enabled-r0=true  enabled-r0-no-preemption=false
00:00:00.211138 APIC: fPostedIntrsEnabled=false fVirtApicRegsEnabled=false fSupportsTscDeadline=false
00:00:00.211147 TMR3UtcNow: nsNow=1 754 320 312 694 585 100 nsPrev=0 -> cNsDelta=1 754 320 312 694 585 100 (offLag=0 offVirtualSync=0 offVirtualSyncGivenUp=0, NowAgain=1 754 320 312 694 585 100)
00:00:00.211160 VMM: fUsePeriodicPreemptionTimers=false
00:00:00.211211 CPUM: Logical host processors: 8 present, 8 max, 8 online, online mask: 00000000000000ff
00:00:00.211212 CPUM: Physical host cores: 4
00:00:00.211212 ************************* CPUID dump ************************
00:00:00.211219          Raw Standard CPUID Leaves
00:00:00.211219      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211221 Gst: 00000000/0000  00000016 756e6547 6c65746e 49656e69
00:00:00.211223 Hst:                00000016 756e6547 6c65746e 49656e69
00:00:00.211223 Gst: 00000001/0000  000906e9 00040800 d6fa2203 178bfbff
00:00:00.211224 Hst:                000906e9 03100800 7ffafbff bfebfbff
00:00:00.211225 Gst: 00000002/0000  76036301 00f0b5ff 00000000 00c30000
00:00:00.211226 Hst:                76036301 00f0b5ff 00000000 00c30000
00:00:00.211227 Gst: 00000003/0000  00000000 00000000 00000000 00000000
00:00:00.211227 Hst:                00000000 00000000 00000000 00000000
00:00:00.211228 Gst: 00000004/0000  0c000121 01c0003f 0000003f 00000000
00:00:00.211228 Hst:                1c004121 01c0003f 0000003f 00000000
00:00:00.211229 Gst: 00000004/0001  0c000122 01c0003f 0000003f 00000000
00:00:00.211230 Hst:                1c004122 01c0003f 0000003f 00000000
00:00:00.211230 Gst: 00000004/0002  0c000143 00c0003f 000003ff 00000000
00:00:00.211231 Hst:                1c004143 00c0003f 000003ff 00000000
00:00:00.211231 Gst: 00000004/0003  0c000163 03c0003f 00001fff 00000006
00:00:00.211232 Hst:                1c03c163 03c0003f 00001fff 00000006
00:00:00.211233 Gst: 00000004/0004  0c000000 00000000 00000000 00000000
00:00:00.211233 Hst:                00000000 00000000 00000000 00000000
00:00:00.211234 Gst: 00000005/0000  00000000 00000000 00000000 00000000
00:00:00.211234 Hst:                00000040 00000040 00000003 00142120
00:00:00.211235 Gst: 00000006/0000  00000000 00000000 00000000 00000000
00:00:00.211235 Hst:                000027f7 00000002 00000009 00000000
00:00:00.211236 Gst: 00000007/0000  00000000 00842421 00000000 1c000400
00:00:00.211237 Hst:                00000000 029c6fbf 00000000 9c002400
00:00:00.211237 Gst: 00000007/0001  00000000 00000000 00000000 00000000
00:00:00.211238 Hst:                00000000 00000000 00000000 00000000
00:00:00.211238 Gst: 00000008/0000  00000000 00000000 00000000 00000000
00:00:00.211238 Hst:                00000000 00000000 00000000 00000000
00:00:00.211239 Gst: 00000009/0000  00000000 00000000 00000000 00000000
00:00:00.211239 Hst:                00000000 00000000 00000000 00000000
00:00:00.211240 Gst: 0000000a/0000  00000000 00000000 00000000 00000000
00:00:00.211240 Hst:                07300404 00000000 00000000 00000603
00:00:00.211241 Gst: 0000000b/0000  00000000 00000001 00000100 00000000
00:00:00.211241 Hst:                00000001 00000002 00000100 00000003
00:00:00.211242 Gst: 0000000b/0001  00000002 00000004 00000201 00000000
00:00:00.211242 Hst:                00000004 00000008 00000201 00000003
00:00:00.211243 Gst: 0000000b/0002  00000000 00000000 00000002 00000000
00:00:00.211243 Hst:                00000000 00000000 00000002 00000003
00:00:00.211244 Gst: 0000000c/0000  00000000 00000000 00000000 00000000
00:00:00.211244 Hst:                00000000 00000000 00000000 00000000
00:00:00.211244 Gst: 0000000d/0000  00000007 00000340 00000340 00000000
00:00:00.211245 Hst:                0000001f 00000440 00000440 00000000
00:00:00.211245 Gst: 0000000d/0001  00000000 00000440 00000000 00000000
00:00:00.211246 Hst:                0000000f 00000440 00000100 00000000
00:00:00.211246 Gst: 0000000d/0002  00000100 00000240 00000000 00000000
00:00:00.211247 Hst:                00000100 00000240 00000000 00000000
00:00:00.211247 Gst: 0000000d/0003  00000000 00000000 00000000 00000000
00:00:00.211248 Hst:                00000040 000003c0 00000000 00000000
00:00:00.211248 Gst: 0000000d/0004  00000000 00000000 00000000 00000000
00:00:00.211249 Hst:                00000040 00000400 00000000 00000000
00:00:00.211249 Gst: 0000000d/0005  00000000 00000000 00000000 00000000
00:00:00.211250 Hst:                00000000 00000000 00000000 00000000
00:00:00.211250 Gst: 0000000d/0006  00000000 00000000 00000000 00000000
00:00:00.211250 Hst:                00000000 00000000 00000000 00000000
00:00:00.211251 Gst: 0000000d/0007  00000000 00000000 00000000 00000000
00:00:00.211251 Hst:                00000000 00000000 00000000 00000000
00:00:00.211252 Gst: 0000000d/0008  00000000 00000000 00000000 00000000
00:00:00.211252 Hst:                00000080 00000000 00000001 00000000
00:00:00.211253 Gst: 0000000d/0009  00000000 00000000 00000000 00000000
00:00:00.211253 Hst:                00000000 00000000 00000000 00000000
00:00:00.211268 Gst: 0000000e/0000  00000000 00000000 00000000 00000000
00:00:00.211268 Hst:                00000000 00000000 00000000 00000000
00:00:00.211269 Gst: 0000000f/0000  00000000 00000000 00000000 00000000
00:00:00.211269 Hst:                00000000 00000000 00000000 00000000
00:00:00.211269 Gst: 00000010/0000  00000000 00000000 00000000 00000000
00:00:00.211270 Hst:                00000000 00000000 00000000 00000000
00:00:00.211270 Gst: 00000011/0000  00000000 00000000 00000000 00000000
00:00:00.211271 Hst:                00000000 00000000 00000000 00000000
00:00:00.211271 Gst: 00000012/0000  00000000 00000000 00000000 00000000
00:00:00.211272 Hst:                00000000 00000000 00000000 00000000
00:00:00.211272 Gst: 00000013/0000  00000000 00000000 00000000 00000000
00:00:00.211272 Hst:                00000000 00000000 00000000 00000000
00:00:00.211273 Gst: 00000014/0000  00000000 00000000 00000000 00000000
00:00:00.211273 Hst:                00000001 0000000f 00000007 00000000
00:00:00.211274 Hst: 00000015/0000  00000002 0000012c 00000000 00000000
00:00:00.211275 Hst: 00000016/0000  00000e10 00001068 00000064 00000000
00:00:00.211275                                Name: GenuineIntel
00:00:00.211276                            Supports: 0x00000000-0x00000016
00:00:00.211279                              Family:  6 	Extended: 0 	Effective: 6
00:00:00.211280                               Model: 14 	Extended: 9 	Effective: 158
00:00:00.211280                            Stepping: 9
00:00:00.211281                                Type: 0 (primary)
00:00:00.211281                             APIC ID: 0x00
00:00:00.211282                        Logical CPUs: 4
00:00:00.211282                        CLFLUSH Size: 8
00:00:00.211283                            Brand ID: 0x00
00:00:00.211283 Features
00:00:00.211284   Mnemonic - Description                                  = guest (host)
00:00:00.211286   FPU - x87 FPU on Chip                                   = 1 (1)
00:00:00.211287   VME - Virtual 8086 Mode Enhancements                    = 1 (1)
00:00:00.211287   DE - Debugging extensions                               = 1 (1)
00:00:00.211288   PSE - Page Size Extension                               = 1 (1)
00:00:00.211289   TSC - Time Stamp Counter                                = 1 (1)
00:00:00.211290   MSR - Model Specific Registers                          = 1 (1)
00:00:00.211290   PAE - Physical Address Extension                        = 1 (1)
00:00:00.211291   MCE - Machine Check Exception                           = 1 (1)
00:00:00.211291   CX8 - CMPXCHG8B instruction                             = 1 (1)
00:00:00.211292   APIC - APIC On-Chip                                     = 1 (1)
00:00:00.211293   SEP - SYSENTER and SYSEXIT Present                      = 1 (1)
00:00:00.211293   MTRR - Memory Type Range Registers                      = 1 (1)
00:00:00.211294   PGE - PTE Global Bit                                    = 1 (1)
00:00:00.211294   MCA - Machine Check Architecture                        = 1 (1)
00:00:00.211295   CMOV - Conditional Move instructions                    = 1 (1)
00:00:00.211296   PAT - Page Attribute Table                              = 1 (1)
00:00:00.211296   PSE-36 - 36-bit Page Size Extension                     = 1 (1)
00:00:00.211297   PSN - Processor Serial Number                           = 0 (0)
00:00:00.211297   CLFSH - CLFLUSH instruction                             = 1 (1)
00:00:00.211298   DS - Debug Store                                        = 0 (1)
00:00:00.211299   ACPI - Thermal Mon. & Soft. Clock Ctrl.                 = 0 (1)
00:00:00.211299   MMX - Intel MMX Technology                              = 1 (1)
00:00:00.211300   FXSR - FXSAVE and FXRSTOR instructions                  = 1 (1)
00:00:00.211301   SSE - SSE support                                       = 1 (1)
00:00:00.211301   SSE2 - SSE2 support                                     = 1 (1)
00:00:00.211302   SS - Self Snoop                                         = 0 (1)
00:00:00.211303   HTT - Hyper-Threading Technology                        = 1 (1)
00:00:00.211303   TM - Therm. Monitor                                     = 0 (1)
00:00:00.211304   PBE - Pending Break Enabled                             = 0 (1)
00:00:00.211304   SSE3 - SSE3 support                                     = 1 (1)
00:00:00.211305   PCLMUL - PCLMULQDQ support (for AES-GCM)                = 1 (1)
00:00:00.211306   DTES64 - DS Area 64-bit Layout                          = 0 (1)
00:00:00.211306   MONITOR - MONITOR/MWAIT instructions                    = 0 (1)
00:00:00.211307   CPL-DS - CPL Qualified Debug Store                      = 0 (1)
00:00:00.211307   VMX - Virtual Machine Extensions                        = 0 (1)
00:00:00.211308   SMX - Safer Mode Extensions                             = 0 (1)
00:00:00.211309   EST - Enhanced SpeedStep Technology                     = 0 (1)
00:00:00.211309   TM2 - Terminal Monitor 2                                = 0 (1)
00:00:00.211310   SSSE3 - Supplemental Streaming SIMD Extensions 3        = 1 (1)
00:00:00.211310   CNTX-ID - L1 Context ID                                 = 0 (0)
00:00:00.211311   SDBG - Silicon Debug interface                          = 0 (1)
00:00:00.211311   FMA - Fused Multiply Add extensions                     = 0 (1)
00:00:00.211312   CX16 - CMPXCHG16B instruction                           = 1 (1)
00:00:00.211313   TPRUPDATE - xTPR Update Control                         = 0 (1)
00:00:00.211313   PDCM - Perf/Debug Capability MSR                        = 0 (1)
00:00:00.211314   PCID - Process Context Identifiers                      = 1 (1)
00:00:00.211314   DCA - Direct Cache Access                               = 0 (0)
00:00:00.211315   SSE4_1 - SSE4_1 support                                 = 1 (1)
00:00:00.211316   SSE4_2 - SSE4_2 support                                 = 1 (1)
00:00:00.211316   X2APIC - x2APIC support                                 = 1 (1)
00:00:00.211317   MOVBE - MOVBE instruction                               = 1 (1)
00:00:00.211318   POPCNT - POPCNT instruction                             = 1 (1)
00:00:00.211318   TSCDEADL - Time Stamp Counter Deadline                  = 0 (1)
00:00:00.211319   AES - AES instructions                                  = 1 (1)
00:00:00.211319   XSAVE - XSAVE instruction                               = 1 (1)
00:00:00.211320   OSXSAVE - OSXSAVE instruction                           = 0 (1)
00:00:00.211321   AVX - AVX support                                       = 1 (1)
00:00:00.211321   F16C - 16-bit floating point conversion instructions    = 0 (1)
00:00:00.211322   RDRAND - RDRAND instruction                             = 1 (1)
00:00:00.211322   HVP - Hypervisor Present (we're a guest)                = 1 (0)
00:00:00.211323 Structured Extended Feature Flags Enumeration (leaf 7):
00:00:00.211324   Mnemonic - Description                                  = guest (host)
00:00:00.211324   FSGSBASE - RDFSBASE/RDGSBASE/WRFSBASE/WRGSBASE instr.   = 1 (1)
00:00:00.211324   TSCADJUST - Supports MSR_IA32_TSC_ADJUST                = 0 (1)
00:00:00.211325   SGX - Supports Software Guard Extensions                = 0 (1)
00:00:00.211325   BMI1 - Advanced Bit Manipulation extension 1            = 0 (1)
00:00:00.211326   HLE - Hardware Lock Elision                             = 0 (1)
00:00:00.211327   AVX2 - Advanced Vector Extensions 2                     = 1 (1)
00:00:00.211327   FDP_EXCPTN_ONLY - FPU DP only updated on exceptions     = 0 (0)
00:00:00.211328   SMEP - Supervisor Mode Execution Prevention             = 0 (1)
00:00:00.211328   BMI2 - Advanced Bit Manipulation extension 2            = 0 (1)
00:00:00.211329   ERMS - Enhanced REP MOVSB/STOSB instructions            = 0 (1)
00:00:00.211329   INVPCID - INVPCID instruction                           = 1 (1)
00:00:00.211330   RTM - Restricted Transactional Memory                   = 0 (1)
00:00:00.211330   PQM - Platform Quality of Service Monitoring            = 0 (0)
00:00:00.211331   DEPFPU_CS_DS - Deprecates FPU CS, FPU DS values if set  = 1 (1)
00:00:00.211331   MPE - Intel Memory Protection Extensions                = 0 (1)
00:00:00.211332   PQE - Platform Quality of Service Enforcement           = 0 (0)
00:00:00.211332   AVX512F - AVX512 Foundation instructions                = 0 (0)
00:00:00.211333   RDSEED - RDSEED instruction                             = 1 (1)
00:00:00.211334   ADX - ADCX/ADOX instructions                            = 0 (1)
00:00:00.211334   SMAP - Supervisor Mode Access Prevention                = 0 (1)
00:00:00.211335   CLFLUSHOPT - CLFLUSHOPT (Cache Line Flush) instruction  = 1 (1)
00:00:00.211335   INTEL_PT - Intel Processor Trace                        = 0 (1)
00:00:00.211336   AVX512PF - AVX512 Prefetch instructions                 = 0 (0)
00:00:00.211336   AVX512ER - AVX512 Exponential & Reciprocal instructions = 0 (0)
00:00:00.211337   AVX512CD - AVX512 Conflict Detection instructions       = 0 (0)
00:00:00.211337   SHA - Secure Hash Algorithm extensions                  = 0 (0)
00:00:00.211338   PREFETCHWT1 - PREFETCHWT1 instruction                   = 0 (0)
00:00:00.211339   UMIP - User mode insturction prevention                 = 0 (0)
00:00:00.211339   PKU - Protection Key for Usermode pages                 = 0 (0)
00:00:00.211340   OSPKE - CR4.PKU mirror                                  = 0 (0)
00:00:00.211341   MAWAU - Value used by BNDLDX & BNDSTX                   = 0x0 (0x0)
00:00:00.211341   RDPID - Read processor ID support                       = 0 (0)
00:00:00.211342   SGX_LC - Supports SGX Launch Configuration              = 0 (0)
00:00:00.211342   MD_CLEAR - Supports MDS related buffer clearing         = 1 (1)
00:00:00.211343   13 - Reserved                                           = 0 (1)
00:00:00.211344   IBRS_IBPB - IA32_SPEC_CTRL.IBRS and IA32_PRED_CMD.IBPB  = 1 (1)
00:00:00.211344   STIBP - Supports IA32_SPEC_CTRL.STIBP                   = 1 (1)
00:00:00.211345   FLUSH_CMD - Supports IA32_FLUSH_CMD                     = 1 (1)
00:00:00.211345   ARCHCAP - Supports IA32_ARCH_CAP                        = 0 (0)
00:00:00.211346   CORECAP - Supports IA32_CORE_CAP                        = 0 (0)
00:00:00.211347   SSBD - Supports IA32_SPEC_CTRL.SSBD                     = 0 (1)
00:00:00.211347 Processor Extended State Enumeration (leaf 0xd):
00:00:00.211348    XSAVE area cur/max size by XCR0, guest: 0x340/0x340
00:00:00.211348     XSAVE area cur/max size by XCR0, host: 0x440/0x440
00:00:00.211349                    Valid XCR0 bits, guest: 0x00000000`00000007 ( x87 SSE YMM_Hi128 )
00:00:00.211350                     Valid XCR0 bits, host: 0x00000000`0000001f ( x87 SSE YMM_Hi128 BNDREGS BNDCSR )
00:00:00.211352                     XSAVE features, guest:
00:00:00.211352                      XSAVE features, host: XSAVEOPT XSAVEC XGETBC1 XSAVES
00:00:00.211355       XSAVE area cur size XCR0|XSS, guest: 0x440
00:00:00.211355        XSAVE area cur size XCR0|XSS, host: 0x440
00:00:00.211356                Valid IA32_XSS bits, guest: 0x00000000`00000000
00:00:00.211356                 Valid IA32_XSS bits, host: 0x00000100`00000000 ( 40 )
00:00:00.211358   State #2, guest: off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.211359   State #2, host:  off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.211360   State #3, host:  off=0x03c0, cb=0x0040 IA32_XSS-bit -- BNDREGS
00:00:00.211361   State #4, host:  off=0x0400, cb=0x0040 IA32_XSS-bit -- BNDCSR
00:00:00.211362   State #8, host:  off=0x0000, cb=0x0080 XCR0-bit -- 8
00:00:00.211369          Unknown CPUID Leaves
00:00:00.211369      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211370 Gst: 00000014/0001  00000000 00000000 00000000 00000000
00:00:00.211370 Hst:                02490002 003f3fff 00000000 00000000
00:00:00.211371 Gst: 00000014/0002  00000000 00000000 00000000 00000000
00:00:00.211372 Hst:                00000000 00000000 00000000 00000000
00:00:00.211372 Gst: 00000015/0000  00000000 00000000 00000000 00000000
00:00:00.211373 Hst:                00000002 0000012c 00000000 00000000
00:00:00.211373 Gst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:00.211374 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211375          Raw Hypervisor CPUID Leaves
00:00:00.211375      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211376 Gst: 40000000/0000  40000001 4b4d564b 564b4d56 0000004d
00:00:00.211376 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211377 Gst: 40000001/0000  01000089 00000000 00000000 00000000
00:00:00.211378 Hst:                00000e10 00001068 00000064 00000000
00:00:00.211378          Raw Extended CPUID Leaves
00:00:00.211379      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.211379 Gst: 80000000/0000  80000008 00000000 00000000 00000000
00:00:00.211380 Hst:                80000008 00000000 00000000 00000000
00:00:00.211380 Gst: 80000001/0000  00000000 00000000 00000121 28100800
00:00:00.211381 Hst:                00000000 00000000 00000121 2c100800
00:00:00.211382 Gst: 80000002/0000  65746e49 2952286c 726f4320 4d542865
00:00:00.211382 Hst:                65746e49 2952286c 726f4320 4d542865
00:00:00.211384 Gst: 80000003/0000  37692029 3037372d 50432030 20402055
00:00:00.211384 Hst:                37692029 3037372d 50432030 20402055
00:00:00.211385 Gst: 80000004/0000  30362e33 007a4847 00000000 00000000
00:00:00.211386 Hst:                30362e33 007a4847 00000000 00000000
00:00:00.211387 Gst: 80000005/0000  00000000 00000000 00000000 00000000
00:00:00.211387 Hst:                00000000 00000000 00000000 00000000
00:00:00.211387 Gst: 80000006/0000  00000000 00000000 01006040 00000000
00:00:00.211388 Hst:                00000000 00000000 01006040 00000000
00:00:00.211389 Gst: 80000007/0000  00000000 00000000 00000000 00000100
00:00:00.211389 Hst:                00000000 00000000 00000000 00000100
00:00:00.211390 Gst: 80000008/0000  00003027 00000000 00000000 00000000
00:00:00.211390 Hst:                00003027 00000000 00000000 00000000
00:00:00.211391 Ext Name:                        
00:00:00.211391 Ext Supports:                    0x80000000-0x80000008
00:00:00.211392 Family:                          0  	Extended: 0 	Effective: 0
00:00:00.211392 Model:                           0  	Extended: 0 	Effective: 0
00:00:00.211393 Stepping:                        0
00:00:00.211393 Brand ID:                        0x000
00:00:00.211393 Ext Features
00:00:00.211393   Mnemonic - Description                                  = guest (host)
00:00:00.211394   FPU - x87 FPU on Chip                                   = 0 (0)
00:00:00.211395   VME - Virtual 8086 Mode Enhancements                    = 0 (0)
00:00:00.211395   DE - Debugging extensions                               = 0 (0)
00:00:00.211396   PSE - Page Size Extension                               = 0 (0)
00:00:00.211396   TSC - Time Stamp Counter                                = 0 (0)
00:00:00.211397   MSR - K86 Model Specific Registers                      = 0 (0)
00:00:00.211398   PAE - Physical Address Extension                        = 0 (0)
00:00:00.211398   MCE - Machine Check Exception                           = 0 (0)
00:00:00.211399   CX8 - CMPXCHG8B instruction                             = 0 (0)
00:00:00.211399   APIC - APIC On-Chip                                     = 0 (0)
00:00:00.211400   SEP - SYSCALL/SYSRET                                    = 1 (1)
00:00:00.211401   MTRR - Memory Type Range Registers                      = 0 (0)
00:00:00.211401   PGE - PTE Global Bit                                    = 0 (0)
00:00:00.211402   MCA - Machine Check Architecture                        = 0 (0)
00:00:00.211402   CMOV - Conditional Move instructions                    = 0 (0)
00:00:00.211403   PAT - Page Attribute Table                              = 0 (0)
00:00:00.211404   PSE-36 - 36-bit Page Size Extension                     = 0 (0)
00:00:00.211404   NX - No-Execute/Execute-Disable                         = 1 (1)
00:00:00.211405   AXMMX - AMD Extensions to MMX instructions              = 0 (0)
00:00:00.211405   MMX - Intel MMX Technology                              = 0 (0)
00:00:00.211406   FXSR - FXSAVE and FXRSTOR Instructions                  = 0 (0)
00:00:00.211406   FFXSR - AMD fast FXSAVE and FXRSTOR instructions        = 0 (0)
00:00:00.211407   Page1GB - 1 GB large page                               = 0 (1)
00:00:00.211408   RDTSCP - RDTSCP instruction                             = 1 (1)
00:00:00.211408   LM - AMD64 Long Mode                                    = 1 (1)
00:00:00.211409   3DNOWEXT - AMD Extensions to 3DNow                      = 0 (0)
00:00:00.211409   3DNOW - AMD 3DNow                                       = 0 (0)
00:00:00.211410   LahfSahf - LAHF/SAHF support in 64-bit mode             = 1 (1)
00:00:00.211411   CmpLegacy - Core multi-processing legacy mode           = 0 (0)
00:00:00.211411   SVM - AMD Secure Virtual Machine extensions             = 0 (0)
00:00:00.211412   EXTAPIC - AMD Extended APIC registers                   = 0 (0)
00:00:00.211412   CR8L - AMD LOCK MOV CR0 means MOV CR8                   = 0 (0)
00:00:00.211413   ABM - AMD Advanced Bit Manipulation                     = 1 (1)
00:00:00.211413   SSE4A - SSE4A instructions                              = 0 (0)
00:00:00.211414   MISALIGNSSE - AMD Misaligned SSE mode                   = 0 (0)
00:00:00.211415   3DNOWPRF - AMD PREFETCH and PREFETCHW instructions      = 1 (1)
00:00:00.211415   OSVW - AMD OS Visible Workaround                        = 0 (0)
00:00:00.211416   IBS - Instruct Based Sampling                           = 0 (0)
00:00:00.211416   XOP - Extended Operation support                        = 0 (0)
00:00:00.211417   SKINIT - SKINIT, STGI, and DEV support                  = 0 (0)
00:00:00.211417   WDT - AMD Watchdog Timer support                        = 0 (0)
00:00:00.211418   LWP - Lightweight Profiling support                     = 0 (0)
00:00:00.211419   FMA4 - Four operand FMA instruction support             = 0 (0)
00:00:00.211419   NodeId - NodeId in MSR C001_100C                        = 0 (0)
00:00:00.211421   TBM - Trailing Bit Manipulation instructions            = 0 (0)
00:00:00.211421   TOPOEXT - Topology Extensions                           = 0 (0)
00:00:00.211422   PRFEXTCORE - Performance Counter Extensions support     = 0 (0)
00:00:00.211423   PRFEXTNB - NB Performance Counter Extensions support    = 0 (0)
00:00:00.211423   DATABPEXT - Data-access Breakpoint Extension            = 0 (0)
00:00:00.211424   PERFTSC - Performance Time Stamp Counter                = 0 (0)
00:00:00.211424   PCX_L2I - L2I/L3 Performance Counter Extensions         = 0 (0)
00:00:00.211425   MWAITX - MWAITX and MONITORX instructions               = 0 (0)
00:00:00.211426 Full Name:                       "Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz"
00:00:00.211426 TLB 2/4M Instr/Uni:              res0     0 entries
00:00:00.211428 TLB 2/4M Data:                   res0     0 entries
00:00:00.211428 TLB 4K Instr/Uni:                res0     0 entries
00:00:00.211429 TLB 4K Data:                     res0     0 entries
00:00:00.211429 L1 Instr Cache Line Size:        0 bytes
00:00:00.211429 L1 Instr Cache Lines Per Tag:    0
00:00:00.211430 L1 Instr Cache Associativity:    res0  
00:00:00.211430 L1 Instr Cache Size:             0 KB
00:00:00.211430 L1 Data Cache Line Size:         0 bytes
00:00:00.211430 L1 Data Cache Lines Per Tag:     0
00:00:00.211431 L1 Data Cache Associativity:     res0  
00:00:00.211431 L1 Data Cache Size:              0 KB
00:00:00.211431 L2 TLB 2/4M Instr/Uni:           off       0 entries
00:00:00.211432 L2 TLB 2/4M Data:                off       0 entries
00:00:00.211432 L2 TLB 4K Instr/Uni:             off       0 entries
00:00:00.211432 L2 TLB 4K Data:                  off       0 entries
00:00:00.211433 L2 Cache Line Size:              0 bytes
00:00:00.211433 L2 Cache Lines Per Tag:          0
00:00:00.211433 L2 Cache Associativity:          off   
00:00:00.211433 L2 Cache Size:                   0 KB
00:00:00.211434   TS - Temperature Sensor                                 = 0 (0)
00:00:00.211434   FID - Frequency ID control                              = 0 (0)
00:00:00.211435   VID - Voltage ID control                                = 0 (0)
00:00:00.211436   TscInvariant - Invariant Time Stamp Counter             = 1 (1)
00:00:00.211436   CBP - Core Performance Boost                            = 0 (0)
00:00:00.211437   EffFreqRO - Read-only Effective Frequency Interface     = 0 (0)
00:00:00.211438   ProcFdbkIf - Processor Feedback Interface               = 0 (0)
00:00:00.211438   ProcPwrRep - Core power reporting interface support     = 0 (0)
00:00:00.211439 Physical Address Width:          39 bits
00:00:00.211439 Virtual Address Width:           48 bits
00:00:00.211439 Guest Physical Address Width:    0 bits
00:00:00.211441 Physical Core Count:             1
00:00:00.211442 
00:00:00.211442 ******************** End of CPUID dump **********************
00:00:00.211442 *********************** VT-x features ***********************
00:00:00.211443 Nested hardware virtualization - VMX features
00:00:00.211444   Mnemonic - Description                                  = guest (host)
00:00:00.211444   VMX - Virtual-Machine Extensions                        = 0 (1)
00:00:00.211444   InsOutInfo - INS/OUTS instruction info.                 = 0 (1)
00:00:00.211445   ExtIntExit - External interrupt exiting                 = 0 (1)
00:00:00.211445   NmiExit - NMI exiting                                   = 0 (1)
00:00:00.211445   VirtNmi - Virtual NMIs                                  = 0 (1)
00:00:00.211446   PreemptTimer - VMX preemption timer                     = 0 (1)
00:00:00.211446   PostedInt - Posted interrupts                           = 0 (0)
00:00:00.211446   IntWindowExit - Interrupt-window exiting                = 0 (1)
00:00:00.211447   TscOffsetting - TSC offsetting                          = 0 (1)
00:00:00.211447   HltExit - HLT exiting                                   = 0 (1)
00:00:00.211447   InvlpgExit - INVLPG exiting                             = 0 (1)
00:00:00.211448   MwaitExit - MWAIT exiting                               = 0 (1)
00:00:00.211450   RdpmcExit - RDPMC exiting                               = 0 (1)
00:00:00.211450   RdtscExit - RDTSC exiting                               = 0 (1)
00:00:00.211451   Cr3LoadExit - CR3-load exiting                          = 0 (1)
00:00:00.211451   Cr3StoreExit - CR3-store exiting                        = 0 (1)
00:00:00.211451   Cr8LoadExit  - CR8-load exiting                         = 0 (1)
00:00:00.211452   Cr8StoreExit - CR8-store exiting                        = 0 (1)
00:00:00.211452   UseTprShadow - Use TPR shadow                           = 0 (1)
00:00:00.211452   NmiWindowExit - NMI-window exiting                      = 0 (1)
00:00:00.211453   MovDRxExit - Mov-DR exiting                             = 0 (1)
00:00:00.211453   UncondIoExit - Unconditional I/O exiting                = 0 (1)
00:00:00.211453   UseIoBitmaps - Use I/O bitmaps                          = 0 (1)
00:00:00.211456   MonitorTrapFlag - Monitor Trap Flag                     = 0 (1)
00:00:00.211456   UseMsrBitmaps - MSR bitmaps                             = 0 (1)
00:00:00.211456   MonitorExit - MONITOR exiting                           = 0 (1)
00:00:00.211457   PauseExit - PAUSE exiting                               = 0 (1)
00:00:00.211457   SecondaryExecCtl - Activate secondary controls          = 0 (1)
00:00:00.211457   VirtApic - Virtualize-APIC accesses                     = 0 (1)
00:00:00.211458   Ept - Extended Page Tables                              = 0 (1)
00:00:00.211458   DescTableExit - Descriptor-table exiting                = 0 (1)
00:00:00.211458   Rdtscp - Enable RDTSCP                                  = 0 (1)
00:00:00.211459   VirtX2ApicMode - Virtualize-x2APIC mode                 = 0 (1)
00:00:00.211459   Vpid - Enable VPID                                      = 0 (1)
00:00:00.211459   WbinvdExit - WBINVD exiting                             = 0 (1)
00:00:00.211460   UnrestrictedGuest - Unrestricted guest                  = 0 (1)
00:00:00.211460   ApicRegVirt - APIC-register virtualization              = 0 (0)
00:00:00.211460   VirtIntDelivery - Virtual-interrupt delivery            = 0 (0)
00:00:00.211461   PauseLoopExit - PAUSE-loop exiting                      = 0 (1)
00:00:00.211461   RdrandExit - RDRAND exiting                             = 0 (1)
00:00:00.211461   Invpcid - Enable INVPCID                                = 0 (1)
00:00:00.211462   VmFuncs - Enable VM Functions                           = 0 (1)
00:00:00.211462   VmcsShadowing - VMCS shadowing                          = 0 (1)
00:00:00.211462   RdseedExiting - RDSEED exiting                          = 0 (1)
00:00:00.211463   PML - Page-Modification Log (PML)                       = 0 (1)
00:00:00.211463   EptVe - EPT violations can cause #VE                    = 0 (1)
00:00:00.211463   XsavesXRstors - Enable XSAVES/XRSTORS                   = 0 (1)
00:00:00.211464   EntryLoadDebugCtls - Load debug controls on VM-entry    = 0 (1)
00:00:00.211464   Ia32eModeGuest - IA-32e mode guest                      = 0 (1)
00:00:00.211464   EntryLoadEferMsr - Load IA32_EFER MSR on VM-entry       = 0 (1)
00:00:00.211464   EntryLoadPatMsr - Load IA32_PAT MSR on VM-entry         = 0 (1)
00:00:00.211465   ExitSaveDebugCtls - Save debug controls on VM-exit      = 0 (1)
00:00:00.211465   HostAddrSpaceSize - Host address-space size             = 0 (1)
00:00:00.211465   ExitAckExtInt - Acknowledge interrupt on VM-exit        = 0 (1)
00:00:00.211467   ExitSavePatMsr - Save IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.211467   ExitLoadPatMsr - Load IA32_PAT MSR on VM-exit           = 0 (1)
00:00:00.211468   ExitSaveEferMsr - Save IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.211468   ExitLoadEferMsr - Load IA32_EFER MSR on VM-exit         = 0 (1)
00:00:00.211468   SavePreemptTimer - Save VMX-preemption timer            = 0 (1)
00:00:00.211469   ExitSaveEferLma - Save IA32_EFER.LMA on VM-exit         = 0 (1)
00:00:00.211469   IntelPt - Intel PT (Processor Trace) in VMX operation   = 0 (1)
00:00:00.211469   VmwriteAll - VMWRITE to any supported VMCS field        = 0 (1)
00:00:00.211470   EntryInjectSoftInt - Inject softint. with 0-len instr.  = 0 (1)
00:00:00.211470 
00:00:00.211470 ******************* End of VT-x features ********************
00:00:00.211493 VMEmt: Halt method global1 (5)
00:00:00.211597 VMEmt: HaltedGlobal1 config: cNsSpinBlockThresholdCfg=50000
00:00:00.211645 Changing the VM state from 'CREATING' to 'CREATED'
00:00:00.213499 SharedFolders host service: Adding host mapping
00:00:00.213518     Host path 'C:/Users/<USER>/Documents/leidian9/Applications', map name 'Applications', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.213775 SharedFolders host service: Adding host mapping
00:00:00.213785     Host path 'C:\Users\<USER>\AppData\Roaming\leidian9\android_bug', map name 'Bug', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214013 SharedFolders host service: Adding host mapping
00:00:00.214022     Host path 'C:/Users/<USER>/Documents/leidian9/Misc', map name 'Misc', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214266 SharedFolders host service: Adding host mapping
00:00:00.214276     Host path 'C:/Users/<USER>/Documents/leidian9/Pictures', map name 'Pictures', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.214421 Changing the VM state from 'CREATED' to 'POWERING_ON'
00:00:00.214490 virtioCoreVirtqAvailBufCount: Driver not ready or queue controlq not enabled
00:00:00.214513 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<0> not enabled
00:00:00.214530 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<1> not enabled
00:00:00.214549 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<2> not enabled
00:00:00.214569 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<3> not enabled
00:00:00.214637 Changing the VM state from 'POWERING_ON' to 'RUNNING'
00:00:00.214656 Console: Machine state changed to 'Running'
00:00:00.218391 VMMDev: Guest Log: BIOS: VirtualBox 6.1.34
00:00:00.218493 PCI: Setting up resources and interrupts
00:00:00.218623 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.243105 VMMDev: Guest Log: CPUID EDX: 0x178bfbff
00:00:00.243920 VMMDev: Guest Log: BIOS: No PCI IDE controller, not probing IDE
00:00:00.246659 VMMDev: Guest Log: BIOS: SCSI 0-ID#0: LCHS=326/255/63 0x0000000000503f2a sectors
00:00:00.247180 VMMDev: Guest Log: BIOS: SCSI 1-ID#1: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.247720 VMMDev: Guest Log: BIOS: SCSI 2-ID#2: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.249329 PIT: mode=2 count=0x48d3 (18643) - 64.00 Hz (ch=0)
00:00:00.249386 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.249628 VMMDev: Guest Log: BIOS: Boot : bseqnr=1, bootseq=0002
00:00:00.250163 VMMDev: Guest Log: BIOS: Booting from Hard Disk...
00:00:00.252960 VMMDev: Guest Log: int13_harddisk_ext: function 41, unmapped device for ELDL=83
00:00:00.253480 VMMDev: Guest Log: int13_harddisk: function 08, unmapped device for ELDL=83
00:00:00.462455 VMMDev: Guest Log: BIOS: KBD: unsupported int 16h function 03
00:00:00.462858 VMMDev: Guest Log: BIOS: AX=0305 BX=0000 CX=0000 DX=0000 
00:00:00.555769 VBoxHeadless: starting event loop
00:00:00.679505 GIM: KVM: VCPU  0: Enabled system-time struct. at 0x000000019ff7c000 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x63aee321 uVirtNanoTS=0x1bb093c1 TscKHz=3600000
00:00:00.679577 TM: Switching TSC mode from 'VirtTscEmulated' to 'RealTscOffset'
00:00:00.737625 GIM: KVM: Enabled wall-clock struct. at 0x00000000010c32a8 - u32Sec=1754320313 u32Nano=220869559 uVersion=2
00:00:00.761371 PIT: mode=2 count=0xf89 (3977) - 300.02 Hz (ch=0)
00:00:00.771203 APIC0: Switched mode to x2APIC
00:00:00.877885 PIT: mode=0 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.909123 APIC1: Switched mode to x2APIC
00:00:00.909139 GIM: KVM: VCPU  1: Enabled system-time struct. at 0x000000019ff7c040 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x94f54a0e uVirtNanoTS=0x29609392 TscKHz=3600000
00:00:00.923549 APIC2: Switched mode to x2APIC
00:00:00.923551 GIM: KVM: VCPU  2: Enabled system-time struct. at 0x000000019ff7c080 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x980d618d uVirtNanoTS=0x2a3c9a14 TscKHz=3600000
00:00:00.938158 APIC3: Switched mode to x2APIC
00:00:00.938176 GIM: KVM: VCPU  3: Enabled system-time struct. at 0x000000019ff7c0c0 - u32TscScale=0x8e38e020 i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x9b301fe0 uVirtNanoTS=0x2b1b9609 TscKHz=3600000
00:00:02.293041 VMMDev: Guest Additions information report: Version 6.1.36 r152435 '6.1.36'
00:00:02.293123 VMMDev: Guest Additions information report: Interface = 0x00010004 osType = 0x00053100 (Linux >= 2.6, 64-bit)
00:00:02.293326 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.293646 VMMDev: vmmDevReqHandler_HeartbeatConfigure: No change (fHeartbeatActive=false)
00:00:02.293682 VMMDev: Heartbeat flatline timer set to trigger after 4 000 000 000 ns
00:00:02.293735 VMMDev: Guest Log: vgdrvHeartbeatInit: Setting up heartbeat to trigger every 2000 milliseconds
00:00:02.294735 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:02.294903 VMMDev: Guest Log: vboxguest: Successfully loaded version 6.1.36 r152435
00:00:02.295198 VMMDev: Guest Log: vboxguest: misc device minor 53, IRQ 20, I/O port d020, MMIO at 00000000f0000000 (size 0x400000)
00:00:02.297715 VMMDev: Guest Log: vboxsf: g_fHostFeatures=0x8000000f g_fSfFeatures=0x1 g_uSfLastFunction=29
00:00:02.298648 VMMDev: Guest Log: vboxsf: Successfully loaded version 6.1.36 r152435 on 4.4.146 SMP preempt mod_unload modversions  (LINUX_VERSION_CODE=0x40492)
00:00:04.799493 NAT: IPv6 not supported
00:00:12.983596 denglibo ld_get_start_addr return 15
00:00:12.983629 NAT: DHCP offered IP address ***********
00:00:13.089608 denglibo ld_get_start_addr return 15
00:00:13.089636 NAT: DHCP offered IP address ***********
00:00:13.681732 NAT: Old socket recv size: 64KB
00:00:13.681781 NAT: Old socket send size: 64KB
