#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版三实查询人员信息采集脚本
- 智能房屋点击（避免图片预览）
- 防重复防遗漏
- 检测"没有更多数据了"
"""

import uiautomator2 as u2
import pandas as pd
import os
import time
import re
import traceback
from datetime import datetime

# 配置
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "people_data.csv")
PROGRESS_FILE = os.path.join(RESULT_DIR, "progress.txt")
ERROR_LOG = os.path.join(RESULT_DIR, "error.log")

# 初始化
d = u2.connect()

def init_files():
    """初始化文件和目录"""
    if not os.path.exists(RESULT_DIR):
        os.makedirs(RESULT_DIR)
    
    if not os.path.exists(RESULT_CSV):
        df = pd.DataFrame(columns=[
            "姓名", "身份证号", "联系电话", "更新时间", 
            "房屋地址", "记录时间", "房主", "处理状态"
        ])
        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        print(f"[INFO] 创建CSV文件: {RESULT_CSV}")

def log_error(msg):
    """记录错误日志"""
    with open(ERROR_LOG, 'a', encoding='utf-8') as f:
        f.write(f"[{datetime.now()}] {msg}\n")

def get_page_text():
    """获取页面所有文本内容"""
    try:
        xml = d.dump_hierarchy()
        texts = re.findall(r'text="([^"]*)"', xml)
        return [text for text in texts if text.strip()]
    except Exception as e:
        print(f"[ERROR] 获取页面文本失败: {e}")
        return []

def get_house_list():
    """获取当前页面的房屋列表信息"""
    try:
        texts = get_page_text()
        houses = []
        
        # 分析房屋列表结构
        current_house = {}
        
        for i, text in enumerate(texts):
            # 查找房主姓名（2-4个中文字符，排除关键词）
            if (len(text) >= 2 and len(text) <= 4 and 
                all('\u4e00' <= c <= '\u9fff' for c in text) and
                text not in ["自住房", "出租房", "全部", "地址", "人员", "单位", "经纬度"]):
                current_house = {"房主": text, "地址": "", "时间": "", "index": len(houses)}
            
            # 查找地址
            elif (text.startswith("云南省") or "小组" in text) and len(text) > 15:
                if "房主" in current_house:
                    current_house["地址"] = text
            
            # 查找时间并完成一个房屋记录
            elif re.match(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', text):
                if "房主" in current_house and "地址" in current_house:
                    current_house["时间"] = text
                    houses.append(current_house.copy())
                    current_house = {}
        
        print(f"[INFO] 当前页面发现 {len(houses)} 个房屋")
        return houses
    except Exception as e:
        print(f"[ERROR] 获取房屋列表失败: {e}")
        return []

def click_house_safely(house_info):
    """安全点击房屋（避免点击图片预览）"""
    try:
        house_name = house_info.get("房主", "")
        if not house_name:
            return False
        
        # 查找包含房主姓名的文本元素
        name_elements = d.xpath(f'//*[@text="{house_name}"]').all()
        
        if name_elements:
            name_element = name_elements[0]
            bounds = name_element.bounds
            
            # 计算安全点击坐标：姓名右侧区域
            click_x = bounds[2] + 100  # 姓名右边100像素
            click_y = (bounds[1] + bounds[3]) // 2  # 垂直居中
            
            print(f"[DEBUG] 点击房屋 {house_name}，坐标: ({click_x}, {click_y})")
            d.click(click_x, click_y)
            time.sleep(3)
            return True
        
        return False
    except Exception as e:
        print(f"[ERROR] 点击房屋失败: {e}")
        return False

def extract_person_info():
    """提取人员信息"""
    try:
        texts = get_page_text()
        people = []
        
        # 合并所有文本进行分析
        all_text = " ".join(texts)
        
        # 查找姓名模式
        name_matches = re.finditer(r'名\s*[：:]\s*([^\s\d，。！？；：""''（）【】]+)', all_text)
        
        for name_match in name_matches:
            person = {
                "姓名": name_match.group(1).strip(),
                "身份证号": "",
                "联系电话": "",
                "更新时间": ""
            }
            
            # 查找身份证号
            id_matches = re.findall(r'(\d{18})', all_text)
            if id_matches:
                person["身份证号"] = id_matches[0]
            
            # 查找联系电话
            phone_matches = re.findall(r'(1[3-9]\d{9})', all_text)
            if phone_matches:
                person["联系电话"] = phone_matches[0]
            
            # 查找更新时间
            time_matches = re.findall(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', all_text)
            if time_matches:
                person["更新时间"] = time_matches[0]
            
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 提取人员: {person['姓名']} - {person['身份证号']}")
        
        # 如果没找到，尝试逐行分析
        if not people:
            person = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}
            
            for text in texts:
                if "名：" in text or "名:" in text:
                    name_part = text.split("名：")[-1] if "名：" in text else text.split("名:")[-1]
                    if name_part.strip():
                        person["姓名"] = name_part.strip()
                
                if re.search(r'\d{18}', text):
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person["身份证号"] = id_match.group(1)
                
                if re.search(r'1[3-9]\d{9}', text):
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person["联系电话"] = phone_match.group(1)
                
                if re.search(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', text):
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person["更新时间"] = time_match.group(1)
            
            if person["姓名"] and person["身份证号"]:
                people.append(person)
                print(f"[INFO] 逐行提取人员: {person['姓名']} - {person['身份证号']}")
        
        return people
    except Exception as e:
        print(f"[ERROR] 提取人员信息失败: {e}")
        return []

def process_house_people(house_info):
    """处理房屋内的人员信息"""
    house_address = house_info.get("地址", "未知地址")
    house_name = house_info.get("房主", "未知")
    
    print(f"[INFO] 开始采集房屋人员: {house_name}")
    
    total_saved = 0
    scroll_count = 0
    max_scrolls = 10
    processed_people = set()
    
    while scroll_count < max_scrolls:
        # 提取当前页面的人员信息
        people = extract_person_info()
        
        if people:
            # 过滤重复人员
            new_people = []
            for person in people:
                person_key = f"{person['姓名']}_{person['身份证号']}"
                if person_key not in processed_people:
                    new_people.append(person)
                    processed_people.add(person_key)
            
            if new_people:
                saved = save_people_data(new_people, house_info)
                total_saved += saved
                print(f"[INFO] 本轮保存 {saved} 个新人员")
        
        # 检查是否显示"没有更多数据了"
        texts = get_page_text()
        if any("没有更多数据" in text for text in texts):
            print("[INFO] 检测到'没有更多数据了'，停止滚动")
            break
        
        # 向下滚动查看更多
        try:
            if d(scrollable=True).exists:
                print(f"[INFO] 向下滚动 ({scroll_count + 1}/{max_scrolls})")
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_count += 1
            else:
                break
        except:
            break
    
    print(f"[INFO] 房屋 {house_name} 采集完成，共保存 {total_saved} 个人员")
    return total_saved

def save_people_data(people, house_info):
    """保存人员数据"""
    if not people:
        return 0

    try:
        df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
        saved_count = 0

        for person in people:
            # 检查是否已存在
            existing = df[(df['姓名'] == person['姓名']) &
                         (df['身份证号'] == person['身份证号'])]

            if existing.empty:
                new_record = {
                    "姓名": person['姓名'],
                    "身份证号": person['身份证号'],
                    "联系电话": person['联系电话'],
                    "更新时间": person['更新时间'],
                    "房屋地址": house_info.get("地址", ""),
                    "记录时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "房主": house_info.get("房主", ""),
                    "处理状态": "已采集"
                }

                df.loc[len(df)] = new_record
                saved_count += 1
                print(f"[SAVE] {person['姓名']} - {person['身份证号']}")

        df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
        return saved_count
    except Exception as e:
        print(f"[ERROR] 保存数据失败: {e}")
        return 0

def process_single_house(house_info):
    """处理单个房屋"""
    try:
        house_name = house_info.get("房主", "未知")
        print(f"\n[INFO] === 处理房屋: {house_name} ===")

        # 安全点击房屋（避免图片预览）
        if not click_house_safely(house_info):
            print("[WARNING] 点击房屋失败")
            return False

        # 检查是否进入房屋详情页
        if not d(text="实有人口").exists:
            print("[WARNING] 未进入房屋详情页，可能点击了图片")
            d.press("back")  # 如果是图片预览，返回
            time.sleep(1)
            return False

        # 点击"实有人口"
        d(text="实有人口").click()
        time.sleep(3)

        # 检查是否进入人员列表页
        if not (d(text="暂无图片").exists or d(text="共有").exists):
            print("[WARNING] 未进入人员列表页")
            d.press("back")
            time.sleep(1)
            return False

        # 处理房屋内的人员信息
        saved_count = process_house_people(house_info)

        # 返回房屋列表
        print("[INFO] 返回房屋列表")
        d.press("back")  # 从人员列表返回房屋详情
        time.sleep(2)

        # 如果还在房屋详情页，再返回一次
        if d(text="实有房屋").exists or d(text="实有人口").exists:
            d.press("back")  # 从房屋详情返回房屋列表
            time.sleep(2)

        return True

    except Exception as e:
        error_msg = f"处理房屋 {house_name} 失败: {e}"
        print(f"[ERROR] {error_msg}")
        log_error(error_msg)

        # 尝试返回房屋列表
        for _ in range(3):
            d.press("back")
            time.sleep(1)
        return False

def check_if_house_list_page():
    """检查是否在房屋列表页"""
    texts = get_page_text()
    return any("共有" in text and "条数据" in text for text in texts)

def main():
    """主函数"""
    print("[INFO] 启动三实查询人员信息采集...")

    # 初始化
    init_files()

    # 启动应用
    print("[INFO] 启动应用...")
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    # 点击三实查询
    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(10)

    time.sleep(5)

    # 点击搜索显示房屋列表
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)

    processed_houses = set()  # 记录已处理的房屋
    total_processed = 0

    while True:
        # 检查是否在房屋列表页
        if not check_if_house_list_page():
            print("[WARNING] 不在房屋列表页，可能已处理完所有房屋")
            break

        # 获取当前页面的房屋列表
        houses = get_house_list()
        if not houses:
            print("[INFO] 当前页面没有房屋，尝试滚动")
            try:
                d(scrollable=True).scroll.vert.forward(steps=5)
                time.sleep(3)
                continue
            except:
                print("[INFO] 无法滚动，采集结束")
                break

        # 处理每个房屋
        new_houses_found = False
        for house in houses:
            house_key = f"{house['房主']}_{house['地址']}"

            if house_key not in processed_houses:
                processed_houses.add(house_key)
                new_houses_found = True

                success = process_single_house(house)
                total_processed += 1

                if not success:
                    print(f"[WARNING] 处理房屋 {house['房主']} 失败")

                time.sleep(2)  # 间隔时间

        # 如果当前页面没有新房屋，尝试滚动到下一页
        if not new_houses_found:
            print("[INFO] 当前页面房屋已全部处理，滚动到下一页")
            try:
                d(scrollable=True).scroll.vert.forward(steps=10)
                time.sleep(3)
            except:
                print("[INFO] 无法继续滚动，采集完成")
                break

    print(f"\n[INFO] 采集完成！共处理 {total_processed} 个房屋")
    print(f"[INFO] 数据保存在: {RESULT_CSV}")

if __name__ == "__main__":
    main()
