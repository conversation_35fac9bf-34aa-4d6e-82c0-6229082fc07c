00:00:00.026186 VirtualBox VM 4.1.34 r2690565888 win.amd64 (Jul 26 2022 16:16:15) release log
00:00:00.026188 Log opened 2025-08-01T07:23:51.198741400Z
00:00:00.026193 Build Type: release
00:00:00.026196 OS Product: Windows 10
00:00:00.026197 OS Release: 10.0.19045
00:00:00.026198 OS Service Pack: 
00:00:00.042247 DMI Product Name: 10NBCTO1WW
00:00:00.046387 DMI Product Version: ThinkCentre M710t-N000
00:00:00.046400 Firmware type: UEFI
00:00:00.046798 Secure Boot: VERR_PRIVILEGE_NOT_HELD
00:00:00.046814 Host RAM: 32675MB (31.9GB) total, 9657MB (9.4GB) available
00:00:00.046817 Executable: C:\Program Files\ldplayer9box\Ld9BoxHeadless.exe
00:00:00.046817 Process ID: 28292
00:00:00.046817 Package type: WINDOWS_64BITS_GENERIC (OSE)
00:00:00.048033 Installed Extension Packs:
00:00:00.048051   None installed!
00:00:00.048705 Console: Machine state changed to 'Starting'
00:00:00.071906 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.071924 SUP: seg #1: R X 0x00001000 LB 0x00109000
00:00:00.071931 SUP: seg #2: R   0x0010a000 LB 0x0004a000
00:00:00.071936 SUP: seg #3: RW  0x00154000 LB 0x00013000
00:00:00.071942 SUP: seg #4: R   0x00167000 LB 0x0000e000
00:00:00.071947 SUP: seg #5: RW  0x00175000 LB 0x00003000
00:00:00.071952 SUP: seg #6: R   0x00178000 LB 0x0000b000
00:00:00.071957 SUP: seg #7: RWX 0x00183000 LB 0x00002000
00:00:00.071962 SUP: seg #8: R   0x00185000 LB 0x00007000
00:00:00.072512 SUP: Loaded Ld9VMMR0.r0 (C:\Program Files\ldplayer9box\Ld9VMMR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.072530 SUP: VMMR0EntryEx located at XXXXXXXXXXXXXXXX and VMMR0EntryFast at XXXXXXXXXXXXXXXX
00:00:00.072536 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9VMMR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.073906 Guest OS type: 'Linux26_64'
00:00:00.074820 fHMForced=true - No raw-mode support in this build!
00:00:00.077242 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\Snapshots' (snapshots) is unknown
00:00:00.077252 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk' is ntfs
00:00:00.077920 File system of 'D:\Program Files\LDPlayer9\system.vmdk' is ntfs
00:00:00.078651 File system of 'D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk' is ntfs
00:00:00.111506 Shared Clipboard: Service loaded
00:00:00.111523 Shared Clipboard: Mode: Off
00:00:00.111755 Shared Clipboard: Service running in headless mode
00:00:00.112725 Drag and drop service loaded
00:00:00.112737 Drag and drop mode: Off
00:00:00.114194 Extradata overrides:
00:00:00.114206   VBoxInternal/Devices/fastpipe/0/PCIBusNo="0"
00:00:00.114261   VBoxInternal/Devices/fastpipe/0/PCIDeviceNo="18"
00:00:00.114304   VBoxInternal/Devices/fastpipe/0/PCIFunctionNo="0"
00:00:00.114343   VBoxInternal/Devices/fastpipe/0/Trusted="1"
00:00:00.114382   VBoxInternal/PDM/Devices/fastpipe/Path="fastpipe.dll"
00:00:00.114563 ************************* CFGM dump *************************
00:00:00.114564 [/] (level 0)
00:00:00.114566   CpuExecutionCap   <integer> = 0x0000000000000064 (100)
00:00:00.114567   EnablePAE         <integer> = 0x0000000000000000 (0)
00:00:00.114568   HMEnabled         <integer> = 0x0000000000000001 (1)
00:00:00.114568   MemBalloonSize    <integer> = 0x0000000000000000 (0)
00:00:00.114569   Name              <string>  = "leidian0" (cb=9)
00:00:00.114569   NumCPUs           <integer> = 0x0000000000000004 (4)
00:00:00.114570   PageFusionAllowed <integer> = 0x0000000000000000 (0)
00:00:00.114570   RamHoleSize       <integer> = 0x0000000020000000 (536 870 912, 512 MB)
00:00:00.114572   RamSize           <integer> = 0x0000000180000000 (6 442 450 944, 6 144 MB, 6.0 GB)
00:00:00.114573   TimerMillies      <integer> = 0x000000000000000a (10)
00:00:00.114574   UUID              <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.114579 
00:00:00.114579 [/CPUM/] (level 1)
00:00:00.114580   GuestCpuName       <string>  = "host" (cb=5)
00:00:00.114581   NestedHWVirt       <integer> = 0x0000000000000000 (0)
00:00:00.114581   PortableCpuIdLevel <integer> = 0x0000000000000000 (0)
00:00:00.114582   SpecCtrl           <integer> = 0x0000000000000001 (1)
00:00:00.114582 
00:00:00.114583 [/CPUM/IsaExts/] (level 2)
00:00:00.114583 
00:00:00.114583 [/DBGC/] (level 1)
00:00:00.114584   GlobalInitScript <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-init" (cb=39)
00:00:00.114584   HistoryFile      <string>  = "C:\Users\<USER>\.Ld9VirtualBox/dbgc-history" (cb=42)
00:00:00.114585   LocalInitScript  <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/dbgc-init" (cb=50)
00:00:00.114585 
00:00:00.114586 [/DBGF/] (level 1)
00:00:00.114586   Path <string>  = "D:\Program Files\LDPlayer9\vms\leidian0/debug/;D:\Program Files\LDPlayer9\vms\leidian0/;cache*D:\Program Files\LDPlayer9\vms\leidian0/dbgcache/;C:\Users\<USER>\" (cb=159)
00:00:00.114587 
00:00:00.114587 [/Devices/] (level 1)
00:00:00.114587 
00:00:00.114588 [/Devices/8237A/] (level 2)
00:00:00.114588 
00:00:00.114588 [/Devices/8237A/0/] (level 3)
00:00:00.114589   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114589 
00:00:00.114590 [/Devices/GIMDev/] (level 2)
00:00:00.114590 
00:00:00.114590 [/Devices/GIMDev/0/] (level 3)
00:00:00.114591   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114591 
00:00:00.114591 [/Devices/VMMDev/] (level 2)
00:00:00.114592 
00:00:00.114592 [/Devices/VMMDev/0/] (level 3)
00:00:00.114593   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114593   PCIDeviceNo   <integer> = 0x0000000000000004 (4)
00:00:00.114594   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114594   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.114595 
00:00:00.114595 [/Devices/VMMDev/0/Config/] (level 4)
00:00:00.114595   GuestCoreDumpDir <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\Snapshots" (cb=50)
00:00:00.114596 
00:00:00.114596 [/Devices/VMMDev/0/LUN#0/] (level 4)
00:00:00.114597   Driver <string>  = "HGCM" (cb=5)
00:00:00.114597 
00:00:00.114597 [/Devices/VMMDev/0/LUN#0/Config/] (level 5)
00:00:00.114598   Object <integer> = 0x000000000328dce0 (53 009 632)
00:00:00.114599 
00:00:00.114599 [/Devices/VMMDev/0/LUN#999/] (level 4)
00:00:00.114600   Driver <string>  = "MainStatus" (cb=11)
00:00:00.114600 
00:00:00.114600 [/Devices/VMMDev/0/LUN#999/Config/] (level 5)
00:00:00.114601   First   <integer> = 0x0000000000000000 (0)
00:00:00.114601   Last    <integer> = 0x0000000000000000 (0)
00:00:00.114602   papLeds <integer> = 0x0000000003284338 (52 970 296)
00:00:00.114602 
00:00:00.114603 [/Devices/acpi/] (level 2)
00:00:00.114603 
00:00:00.114603 [/Devices/acpi/0/] (level 3)
00:00:00.114604   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114604   PCIDeviceNo   <integer> = 0x0000000000000007 (7)
00:00:00.114605   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114605   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.114605 
00:00:00.114606 [/Devices/acpi/0/Config/] (level 4)
00:00:00.114606   CpuHotPlug          <integer> = 0x0000000000000000 (0)
00:00:00.114607   FdcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.114607   HostBusPciAddress   <integer> = 0x0000000000000000 (0)
00:00:00.114608   HpetEnabled         <integer> = 0x0000000000000000 (0)
00:00:00.114608   IOAPIC              <integer> = 0x0000000000000001 (1)
00:00:00.114609   IocPciAddress       <integer> = 0x0000000000010000 (65 536)
00:00:00.114609   NumCPUs             <integer> = 0x0000000000000004 (4)
00:00:00.114610   Parallel0IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.114610   Parallel0Irq        <integer> = 0x0000000000000000 (0)
00:00:00.114611   Parallel1IoPortBase <integer> = 0x0000000000000000 (0)
00:00:00.114611   Parallel1Irq        <integer> = 0x0000000000000000 (0)
00:00:00.114612   Serial0IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.114612   Serial0Irq          <integer> = 0x0000000000000000 (0)
00:00:00.114613   Serial1IoPortBase   <integer> = 0x0000000000000000 (0)
00:00:00.114613   Serial1Irq          <integer> = 0x0000000000000000 (0)
00:00:00.114613   ShowCpu             <integer> = 0x0000000000000001 (1)
00:00:00.114614   ShowRtc             <integer> = 0x0000000000000000 (0)
00:00:00.114614   SmcEnabled          <integer> = 0x0000000000000000 (0)
00:00:00.114615 
00:00:00.114615 [/Devices/acpi/0/LUN#0/] (level 4)
00:00:00.114615   Driver <string>  = "ACPIHost" (cb=9)
00:00:00.114616 
00:00:00.114616 [/Devices/acpi/0/LUN#0/Config/] (level 5)
00:00:00.114617 
00:00:00.114617 [/Devices/acpi/0/LUN#1/] (level 4)
00:00:00.114617   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.114618 
00:00:00.114618 [/Devices/acpi/0/LUN#1/Config/] (level 5)
00:00:00.114619 
00:00:00.114619 [/Devices/acpi/0/LUN#2/] (level 4)
00:00:00.114619   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.114620 
00:00:00.114620 [/Devices/acpi/0/LUN#2/Config/] (level 5)
00:00:00.114620 
00:00:00.114621 [/Devices/acpi/0/LUN#3/] (level 4)
00:00:00.114621   Driver <string>  = "ACPICpu" (cb=8)
00:00:00.114622 
00:00:00.114622 [/Devices/acpi/0/LUN#3/Config/] (level 5)
00:00:00.114622 
00:00:00.114622 [/Devices/apic/] (level 2)
00:00:00.114623 
00:00:00.114623 [/Devices/apic/0/] (level 3)
00:00:00.114624   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114624 
00:00:00.114624 [/Devices/apic/0/Config/] (level 4)
00:00:00.114625   IOAPIC  <integer> = 0x0000000000000001 (1)
00:00:00.114625   Mode    <integer> = 0x0000000000000003 (3)
00:00:00.114626   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.114626 
00:00:00.114627 [/Devices/e1000/] (level 2)
00:00:00.114627 
00:00:00.114627 [/Devices/fastpipe/] (level 2)
00:00:00.114628 
00:00:00.114628 [/Devices/fastpipe/0/] (level 3)
00:00:00.114629   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114629   PCIDeviceNo   <integer> = 0x0000000000000012 (18)
00:00:00.114629   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114630   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.114630 
00:00:00.114630 [/Devices/i8254/] (level 2)
00:00:00.114631 
00:00:00.114631 [/Devices/i8254/0/] (level 3)
00:00:00.114632 
00:00:00.114632 [/Devices/i8254/0/Config/] (level 4)
00:00:00.114632 
00:00:00.114633 [/Devices/i8259/] (level 2)
00:00:00.114633 
00:00:00.114633 [/Devices/i8259/0/] (level 3)
00:00:00.114634   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114634 
00:00:00.114634 [/Devices/i8259/0/Config/] (level 4)
00:00:00.114635 
00:00:00.114635 [/Devices/ioapic/] (level 2)
00:00:00.114636 
00:00:00.114636 [/Devices/ioapic/0/] (level 3)
00:00:00.114636   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114637 
00:00:00.114637 [/Devices/ioapic/0/Config/] (level 4)
00:00:00.114637   NumCPUs <integer> = 0x0000000000000004 (4)
00:00:00.114638 
00:00:00.114638 [/Devices/mc146818/] (level 2)
00:00:00.114638 
00:00:00.114639 [/Devices/mc146818/0/] (level 3)
00:00:00.114639 
00:00:00.114639 [/Devices/mc146818/0/Config/] (level 4)
00:00:00.114640   UseUTC <integer> = 0x0000000000000001 (1)
00:00:00.114640 
00:00:00.114640 [/Devices/parallel/] (level 2)
00:00:00.114641 
00:00:00.114641 [/Devices/pcarch/] (level 2)
00:00:00.114642 
00:00:00.114642 [/Devices/pcarch/0/] (level 3)
00:00:00.114642   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114643 
00:00:00.114643 [/Devices/pcarch/0/Config/] (level 4)
00:00:00.114643 
00:00:00.114643 [/Devices/pcbios/] (level 2)
00:00:00.114644 
00:00:00.114644 [/Devices/pcbios/0/] (level 3)
00:00:00.114645   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114645 
00:00:00.114645 [/Devices/pcbios/0/Config/] (level 4)
00:00:00.114646   APIC           <integer> = 0x0000000000000001 (1)
00:00:00.114646   BootDevice0    <string>  = "IDE" (cb=4)
00:00:00.114647   BootDevice1    <string>  = "NONE" (cb=5)
00:00:00.114647   BootDevice2    <string>  = "NONE" (cb=5)
00:00:00.114648   BootDevice3    <string>  = "NONE" (cb=5)
00:00:00.114648   FloppyDevice   <string>  = "i82078" (cb=7)
00:00:00.114649   HardDiskDevice <string>  = "piix3ide" (cb=9)
00:00:00.114649   IOAPIC         <integer> = 0x0000000000000001 (1)
00:00:00.114649   McfgBase       <integer> = 0x0000000000000000 (0)
00:00:00.114650   McfgLength     <integer> = 0x0000000000000000 (0)
00:00:00.114650   NumCPUs        <integer> = 0x0000000000000004 (4)
00:00:00.114651   PXEDebug       <integer> = 0x0000000000000000 (0)
00:00:00.114651   UUID           <bytes>   = "02 03 16 20 aa aa aa aa 0c 9f 00 00 00 00 00 00" (cb=16)
00:00:00.114652   UuidLe         <integer> = 0x0000000000000000 (0)
00:00:00.114653 
00:00:00.114653 [/Devices/pcbios/0/Config/NetBoot/] (level 5)
00:00:00.114654 
00:00:00.114654 [/Devices/pcbios/0/Config/NetBoot/0/] (level 6)
00:00:00.114655   NIC           <integer> = 0x0000000000000000 (0)
00:00:00.114655   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114656   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.114656   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114656 
00:00:00.114657 [/Devices/pci/] (level 2)
00:00:00.114657 
00:00:00.114657 [/Devices/pci/0/] (level 3)
00:00:00.114658   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114658 
00:00:00.114658 [/Devices/pci/0/Config/] (level 4)
00:00:00.114659   IOAPIC <integer> = 0x0000000000000001 (1)
00:00:00.114659 
00:00:00.114659 [/Devices/pcibridge/] (level 2)
00:00:00.114660 
00:00:00.114660 [/Devices/pckbd/] (level 2)
00:00:00.114661 
00:00:00.114661 [/Devices/pckbd/0/] (level 3)
00:00:00.114661   Trusted <integer> = 0x0000000000000001 (1)
00:00:00.114662 
00:00:00.114662 [/Devices/pckbd/0/Config/] (level 4)
00:00:00.114662 
00:00:00.114663 [/Devices/pckbd/0/LUN#0/] (level 4)
00:00:00.114663   Driver <string>  = "KeyboardQueue" (cb=14)
00:00:00.114664 
00:00:00.114664 [/Devices/pckbd/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.114664   Driver <string>  = "MainKeyboard" (cb=13)
00:00:00.114665 
00:00:00.114665 [/Devices/pckbd/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.114666   Object <integer> = 0x0000000003287a50 (52 984 400)
00:00:00.114666 
00:00:00.114667 [/Devices/pckbd/0/LUN#0/Config/] (level 5)
00:00:00.114667   QueueSize <integer> = 0x0000000000000040 (64)
00:00:00.114668 
00:00:00.114668 [/Devices/pckbd/0/LUN#1/] (level 4)
00:00:00.114669   Driver <string>  = "MouseQueue" (cb=11)
00:00:00.114669 
00:00:00.114669 [/Devices/pckbd/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.114670   Driver <string>  = "MainMouse" (cb=10)
00:00:00.114670 
00:00:00.114670 [/Devices/pckbd/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.114671   Object <integer> = 0x0000000003288110 (52 986 128)
00:00:00.114672 
00:00:00.114672 [/Devices/pckbd/0/LUN#1/Config/] (level 5)
00:00:00.114672   QueueSize <integer> = 0x0000000000000080 (128)
00:00:00.114673 
00:00:00.114673 [/Devices/pcnet/] (level 2)
00:00:00.114674 
00:00:00.114674 [/Devices/serial/] (level 2)
00:00:00.114674 
00:00:00.114674 [/Devices/virtio-net/] (level 2)
00:00:00.114675 
00:00:00.114675 [/Devices/virtio-net/0/] (level 3)
00:00:00.114676   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114676   PCIDeviceNo   <integer> = 0x0000000000000003 (3)
00:00:00.114677   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114677   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.114677 
00:00:00.114678 [/Devices/virtio-net/0/Config/] (level 4)
00:00:00.114678   CableConnected <integer> = 0x0000000000000001 (1)
00:00:00.114679   LineSpeed      <integer> = 0x0000000000000000 (0)
00:00:00.114679   MAC            <bytes>   = "00 db 30 ef a7 99" (cb=6)
00:00:00.114680 
00:00:00.114680 [/Devices/virtio-net/0/LUN#0/] (level 4)
00:00:00.114681   Driver <string>  = "IntNet" (cb=7)
00:00:00.114681 
00:00:00.114681 [/Devices/virtio-net/0/LUN#0/Config/] (level 5)
00:00:00.114682   IfPolicyPromisc      <string>  = "deny" (cb=5)
00:00:00.114682   IgnoreConnectFailure <integer> = 0x0000000000000000 (0)
00:00:00.114683   Network              <string>  = "HostInterfaceNetworking-Realtek PCIe GBE Family Controller" (cb=59)
00:00:00.114683   Trunk                <string>  = "\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}" (cb=47)
00:00:00.114684   TrunkType            <integer> = 0x0000000000000003 (3)
00:00:00.114684 
00:00:00.114684 [/Devices/virtio-net/0/LUN#999/] (level 4)
00:00:00.114685   Driver <string>  = "MainStatus" (cb=11)
00:00:00.114685 
00:00:00.114686 [/Devices/virtio-net/0/LUN#999/Config/] (level 5)
00:00:00.114686   First   <integer> = 0x0000000000000000 (0)
00:00:00.114687   Last    <integer> = 0x0000000000000000 (0)
00:00:00.114687   papLeds <integer> = 0x0000000003284218 (52 970 008)
00:00:00.114688 
00:00:00.114688 [/Devices/virtio-scsi/] (level 2)
00:00:00.114688 
00:00:00.114688 [/Devices/virtio-scsi/0/] (level 3)
00:00:00.114689   PCIBusNo      <integer> = 0x0000000000000000 (0)
00:00:00.114689   PCIDeviceNo   <integer> = 0x000000000000000f (15)
00:00:00.114690   PCIFunctionNo <integer> = 0x0000000000000000 (0)
00:00:00.114690   Trusted       <integer> = 0x0000000000000001 (1)
00:00:00.114691 
00:00:00.114691 [/Devices/virtio-scsi/0/Config/] (level 4)
00:00:00.114692   Bootable   <integer> = 0x0000000000000001 (1)
00:00:00.114692   NumTargets <integer> = 0x0000000000000003 (3)
00:00:00.114692 
00:00:00.114693 [/Devices/virtio-scsi/0/LUN#0/] (level 4)
00:00:00.114693   Driver <string>  = "SCSI" (cb=5)
00:00:00.114693 
00:00:00.114694 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/] (level 5)
00:00:00.114694   Driver <string>  = "VD" (cb=3)
00:00:00.114695 
00:00:00.114695 [/Devices/virtio-scsi/0/LUN#0/AttachedDriver/Config/] (level 6)
00:00:00.114696   Format    <string>  = "VMDK" (cb=5)
00:00:00.114697   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.114697   Path      <string>  = "D:\Program Files\LDPlayer9\system.vmdk" (cb=39)
00:00:00.114697   ReadOnly  <integer> = 0x0000000000000001 (1)
00:00:00.114698   Type      <string>  = "HardDisk" (cb=9)
00:00:00.114698 
00:00:00.114698 [/Devices/virtio-scsi/0/LUN#1/] (level 4)
00:00:00.114699   Driver <string>  = "SCSI" (cb=5)
00:00:00.114699 
00:00:00.114700 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/] (level 5)
00:00:00.114700   Driver <string>  = "VD" (cb=3)
00:00:00.114701 
00:00:00.114701 [/Devices/virtio-scsi/0/LUN#1/AttachedDriver/Config/] (level 6)
00:00:00.114702   Format    <string>  = "VMDK" (cb=5)
00:00:00.114702   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.114703   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\data.vmdk" (cb=50)
00:00:00.114703   Type      <string>  = "HardDisk" (cb=9)
00:00:00.114703 
00:00:00.114704 [/Devices/virtio-scsi/0/LUN#2/] (level 4)
00:00:00.114704   Driver <string>  = "SCSI" (cb=5)
00:00:00.114704 
00:00:00.114705 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/] (level 5)
00:00:00.114705   Driver <string>  = "VD" (cb=3)
00:00:00.114706 
00:00:00.114706 [/Devices/virtio-scsi/0/LUN#2/AttachedDriver/Config/] (level 6)
00:00:00.114706   Format    <string>  = "VMDK" (cb=5)
00:00:00.114707   Mountable <integer> = 0x0000000000000000 (0)
00:00:00.114707   Path      <string>  = "D:\Program Files\LDPlayer9\vms\leidian0\sdcard.vmdk" (cb=52)
00:00:00.114708   Type      <string>  = "HardDisk" (cb=9)
00:00:00.114708 
00:00:00.114708 [/Devices/virtio-scsi/0/LUN#999/] (level 4)
00:00:00.114709   Driver <string>  = "MainStatus" (cb=11)
00:00:00.114709 
00:00:00.114709 [/Devices/virtio-scsi/0/LUN#999/Config/] (level 5)
00:00:00.114710   DeviceInstance        <string>  = "virtio-scsi/0" (cb=14)
00:00:00.114711   First                 <integer> = 0x0000000000000000 (0)
00:00:00.114711   Last                  <integer> = 0x0000000000000002 (2)
00:00:00.114711   pConsole              <integer> = 0x0000000003282f40 (52 965 184)
00:00:00.114712   papLeds               <integer> = 0x0000000003283a20 (52 967 968)
00:00:00.114713   pmapMediumAttachments <integer> = 0x0000000003284358 (52 970 328)
00:00:00.114714 
00:00:00.114714 [/EM/] (level 1)
00:00:00.114714   TripleFaultReset <integer> = 0x0000000000000000 (0)
00:00:00.114715 
00:00:00.114715 [/GIM/] (level 1)
00:00:00.114715   Provider <string>  = "KVM" (cb=4)
00:00:00.114716 
00:00:00.114716 [/HM/] (level 1)
00:00:00.114716   64bitEnabled       <integer> = 0x0000000000000001 (1)
00:00:00.114717   EnableLargePages   <integer> = 0x0000000000000001 (1)
00:00:00.114717   EnableNestedPaging <integer> = 0x0000000000000001 (1)
00:00:00.114718   EnableUX           <integer> = 0x0000000000000001 (1)
00:00:00.114718   EnableVPID         <integer> = 0x0000000000000001 (1)
00:00:00.114719   Exclusive          <integer> = 0x0000000000000000 (0)
00:00:00.114719   HMForced           <integer> = 0x0000000000000001 (1)
00:00:00.114719   IBPBOnVMEntry      <integer> = 0x0000000000000000 (0)
00:00:00.114720   IBPBOnVMExit       <integer> = 0x0000000000000000 (0)
00:00:00.114720   L1DFlushOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.114721   L1DFlushOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.114721   MDSClearOnSched    <integer> = 0x0000000000000000 (0)
00:00:00.114722   MDSClearOnVMEntry  <integer> = 0x0000000000000000 (0)
00:00:00.114722   SpecCtrlByHost     <integer> = 0x0000000000000000 (0)
00:00:00.114722   UseNEMInstead      <integer> = 0x0000000000000000 (0)
00:00:00.114723 
00:00:00.114723 [/MM/] (level 1)
00:00:00.114723   CanUseLargerHeap <integer> = 0x0000000000000000 (0)
00:00:00.114724 
00:00:00.114724 [/NEM/] (level 1)
00:00:00.114724   Allow64BitGuests <integer> = 0x0000000000000001 (1)
00:00:00.114725 
00:00:00.114725 [/PDM/] (level 1)
00:00:00.114725 
00:00:00.114726 [/PDM/AsyncCompletion/] (level 2)
00:00:00.114726 
00:00:00.114726 [/PDM/AsyncCompletion/File/] (level 3)
00:00:00.114727 
00:00:00.114727 [/PDM/AsyncCompletion/File/BwGroups/] (level 4)
00:00:00.114728 
00:00:00.114728 [/PDM/BlkCache/] (level 2)
00:00:00.114728   CacheSize <integer> = 0x0000000000500000 (5 242 880, 5 MB)
00:00:00.114729 
00:00:00.114729 [/PDM/Devices/] (level 2)
00:00:00.114730 
00:00:00.114730 [/PDM/Devices/fastpipe/] (level 3)
00:00:00.114730   Path <string>  = "fastpipe.dll" (cb=13)
00:00:00.114731 
00:00:00.114731 [/PDM/Drivers/] (level 2)
00:00:00.114731 
00:00:00.114732 [/PDM/Drivers/VBoxC/] (level 3)
00:00:00.114732   Path <string>  = "VBoxC" (cb=6)
00:00:00.114732 
00:00:00.114733 [/PDM/NetworkShaper/] (level 2)
00:00:00.114733 
00:00:00.114733 [/PDM/NetworkShaper/BwGroups/] (level 3)
00:00:00.114734 
00:00:00.114734 [/TM/] (level 1)
00:00:00.114734   UTCOffset <integer> = 0x0000000000000000 (0)
00:00:00.114735 
00:00:00.114735 ********************* End of CFGM dump **********************
00:00:00.115035 HM: HMR3Init: Attempting fall back to NEM: VT-x is not available
00:00:00.116035 NEM:  info: Found optional import WinHvPlatform.dll!WHvQueryGpaRangeDirtyBitmap.
00:00:00.116130 NEM: WHvCapabilityCodeHypervisorPresent is TRUE, so this might work...
00:00:00.116138 NEM: WHvCapabilityCodeExtendedVmExits      = 0x00000000000003ff
00:00:00.116158 NEM:                       fExtendedMsrExit: 1
00:00:00.116164 NEM:                     fExtendedCpuIdExit: 1
00:00:00.116169 NEM:                      fExtendedXcptExit: 1
00:00:00.116174 NEM: Warning! Unknown VM exit definitions: 0x3ff
00:00:00.116180 NEM: Warning! Unknown feature definitions: 0x7f
00:00:00.116185 NEM: Supported exception exit bitmap: 0xf7dfb
00:00:00.116193 NEM: WHvCapabilityCodeProcessorVendor      = 1 - Intel
00:00:00.116203 NEM: WHvCapabilityCodeProcessorFeatures    = 0x100178ffe7f7859f
00:00:00.116209 NEM:                            Sse3Support: 1
00:00:00.116227 NEM:                        LahfSahfSupport: 1
00:00:00.116232 NEM:                           Ssse3Support: 1
00:00:00.116238 NEM:                          Sse4_1Support: 1
00:00:00.116243 NEM:                          Sse4_2Support: 1
00:00:00.116248 NEM:                           Sse4aSupport: 0
00:00:00.116254 NEM:                             XopSupport: 0
00:00:00.116259 NEM:                          PopCntSupport: 1
00:00:00.116264 NEM:                      Cmpxchg16bSupport: 1
00:00:00.116269 NEM:                       Altmovcr8Support: 0
00:00:00.116274 NEM:                           LzcntSupport: 1
00:00:00.116281 NEM:                     MisAlignSseSupport: 0
00:00:00.116287 NEM:                          MmxExtSupport: 0
00:00:00.116292 NEM:                        Amd3DNowSupport: 0
00:00:00.116297 NEM:                ExtendedAmd3DNowSupport: 0
00:00:00.116302 NEM:                         Page1GbSupport: 1
00:00:00.116307 NEM:                             AesSupport: 1
00:00:00.116312 NEM:                       PclmulqdqSupport: 1
00:00:00.116318 NEM:                            PcidSupport: 1
00:00:00.116323 NEM:                            Fma4Support: 0
00:00:00.116328 NEM:                            F16CSupport: 1
00:00:00.116333 NEM:                          RdRandSupport: 1
00:00:00.116338 NEM:                        RdWrFsGsSupport: 1
00:00:00.116343 NEM:                            SmepSupport: 1
00:00:00.116348 NEM:              EnhancedFastStringSupport: 1
00:00:00.116353 NEM:                            Bmi1Support: 1
00:00:00.116358 NEM:                            Bmi2Support: 1
00:00:00.116363 NEM:                           MovbeSupport: 1
00:00:00.116368 NEM:                          Npiep1Support: 1
00:00:00.116373 NEM:                   DepX87FPUSaveSupport: 1
00:00:00.116378 NEM:                          RdSeedSupport: 1
00:00:00.116383 NEM:                             AdxSupport: 1
00:00:00.116388 NEM:                   IntelPrefetchSupport: 1
00:00:00.116393 NEM:                            SmapSupport: 1
00:00:00.116398 NEM:                             HleSupport: 1
00:00:00.116404 NEM:                             RtmSupport: 1
00:00:00.116409 NEM:                          RdtscpSupport: 1
00:00:00.116414 NEM:                      ClflushoptSupport: 1
00:00:00.116419 NEM:                            ClwbSupport: 0
00:00:00.116424 NEM:                             ShaSupport: 0
00:00:00.116429 NEM:                X87PointersSavedSupport: 0
00:00:00.116434 NEM: Warning! Unknown CPU features: 0x100178ffe7f7859f
00:00:00.116442 NEM: WHvCapabilityCodeProcessorClFlushSize = 2^8
00:00:00.116448 NEM: Warning! Unknown capability 0x4 returning: 3f 00 00 00 00 00 00 00
00:00:00.117011 NEM: Warning! Unknown capability 0x1003 returning: 3f 38 00 00 00 00 00 00
00:00:00.117051 NEM: Warning! Unknown capability 0x1004 returning: ea a2 93 d6 00 00 00 00
00:00:00.117058 NEM: Warning! Unknown capability 0x1005 returning: 00 c2 eb 0b 00 00 00 00
00:00:00.117705 NEM: Created partition 00000000010e9cd0.
00:00:00.117716 NEM: Adjusting APIC configuration from X2APIC to APIC max mode.  X2APIC is not supported by the WinHvPlatform API!
00:00:00.117721 NEM: Disable Hyper-V if you need X2APIC for your guests!
00:00:00.117977 NEM: NEMR3Init: Active.
00:00:00.117989 MM: cbHyperHeap=0x840000 (8650752)
00:00:00.127324 CPUM: No hardware-virtualization capability detected
00:00:00.128351 CPUM: fXStateHostMask=0x7; initial: 0x7; host XCR0=0x1f
00:00:00.130645 CPUM: Matched host CPU INTEL 0x6/0x9e/0x9 Intel_Core7_KabyLake with CPU DB entry 'Intel Core i7-6700K' (INTEL 0x6/0x5e/0x3 Intel_Core7_Skylake)
00:00:00.130714 CPUM: MXCSR_MASK=0xffff (host: 0xffff)
00:00:00.130731 CPUM: Microcode revision 0x000000B4
00:00:00.130748 CPUM: MSR/CPUID reconciliation insert: 0x0000010b IA32_FLUSH_CMD
00:00:00.130756 CPUM: MSR/CPUID reconciliation insert: 0x0000010a IA32_ARCH_CAPABILITIES
00:00:00.130764 CPUM: MSR fudge: 0x00000122 IA32_TSX_CTRL
00:00:00.130790 CPUM: SetGuestCpuIdFeature: Enabled Speculation Control.
00:00:00.130799 CPUM: SetGuestCpuIdFeature: Enabled SYSENTER/EXIT
00:00:00.130804 CPUM: SetGuestCpuIdFeature: Enabled SYSCALL/RET
00:00:00.130809 CPUM: SetGuestCpuIdFeature: Enabled PAE
00:00:00.130814 CPUM: SetGuestCpuIdFeature: Enabled LONG MODE
00:00:00.130819 CPUM: SetGuestCpuIdFeature: Enabled LAHF/SAHF
00:00:00.130824 CPUM: SetGuestCpuIdFeature: Enabled NX
00:00:00.132651 NEM: HvPartitionPropertyProcessorVendor=0x1 (1)
00:00:00.132695 NEM: Successfully set up partition (device handle 00000000000006b0, partition ID 0x6)
00:00:00.132899 PGM: Host paging mode: AMD64+NX
00:00:00.132927 PGM: PGMPool: cMaxPages=3328 (u64MaxPages=3110)
00:00:00.132937 PGM: pgmR3PoolInit: cMaxPages=0xd00 cMaxUsers=0x1a00 cMaxPhysExts=0x1a00 fCacheEnable=true 
00:00:00.336422 TM: GIP - u32Mode=3 (Invariant) u32UpdateHz=93 u32UpdateIntervalNS=10741500 enmUseTscDelta=2 (Practically Zero) fGetGipCpu=0x1b cCpus=8
00:00:00.336508 TM: GIP - u64CpuHz=3 599 999 612 (0xd693a27c)  SUPGetCpuHzFromGip => 3 599 999 612
00:00:00.336539 TM: GIP - CPU: iCpuSet=0x0 idCpu=0x0 idApic=0x0 iGipCpu=0x6 i64TSCDelta=0 enmState=3 u64CpuHz=3599999481(*) cErrors=0
00:00:00.336575 TM: GIP - CPU: iCpuSet=0x1 idCpu=0x1 idApic=0x1 iGipCpu=0x2 i64TSCDelta=0 enmState=3 u64CpuHz=3599993958(*) cErrors=0
00:00:00.336622 TM: GIP - CPU: iCpuSet=0x2 idCpu=0x2 idApic=0x2 iGipCpu=0x3 i64TSCDelta=0 enmState=3 u64CpuHz=3599998819(*) cErrors=0
00:00:00.336668 TM: GIP - CPU: iCpuSet=0x3 idCpu=0x3 idApic=0x3 iGipCpu=0x5 i64TSCDelta=0 enmState=3 u64CpuHz=3599999399(*) cErrors=0
00:00:00.336681 TM: GIP - CPU: iCpuSet=0x4 idCpu=0x4 idApic=0x4 iGipCpu=0x0 i64TSCDelta=0 enmState=3 u64CpuHz=3599999612(*) cErrors=0
00:00:00.336693 TM: GIP - CPU: iCpuSet=0x5 idCpu=0x5 idApic=0x5 iGipCpu=0x7 i64TSCDelta=0 enmState=3 u64CpuHz=3599999515(*) cErrors=0
00:00:00.336709 TM: GIP - CPU: iCpuSet=0x6 idCpu=0x6 idApic=0x6 iGipCpu=0x4 i64TSCDelta=0 enmState=3 u64CpuHz=3599999270(*) cErrors=0
00:00:00.336739 TM: GIP - CPU: iCpuSet=0x7 idCpu=0x7 idApic=0x7 iGipCpu=0x1 i64TSCDelta=0 enmState=3 u64CpuHz=**********(*) cErrors=0
00:00:00.336977 TM: NEM overrides the /TM/TSCModeSwitchAllowed setting.
00:00:00.337001 TM: cTSCTicksPerSecond=3 599 999 612 (0xd693a27c) enmTSCMode=4 (NativeApi)
00:00:00.337016 TM: TSCTiedToExecution=false TSCNotTiedToHalt=false
00:00:00.338014 EMR3Init: fIemExecutesAll=false fGuruOnTripleFault=true 
00:00:00.339262 IEM: TargetCpu=CURRENT, Microarch=Intel_Core7_KabyLake
00:00:00.339664 GIM: Using provider 'KVM' (Implementation version: 0)
00:00:00.339686 CPUM: SetGuestCpuIdFeature: Enabled Hypervisor Present bit
00:00:00.339824 AIOMgr: Default manager type is 'Async'
00:00:00.339836 AIOMgr: Default file backend is 'NonBuffered'
00:00:00.340127 BlkCache: Cache successfully initialized. Cache size is 5242880 bytes
00:00:00.340145 BlkCache: Cache commit interval is 10000 ms
00:00:00.340158 BlkCache: Cache commit threshold is 2621440 bytes
00:00:00.342204 fastpipe::VBoxDevicesRegister: u32Version=0x60001 pCallbacks->u32Version=0xffe30010
00:00:00.342431 PcBios: [SMP] BIOS with 4 CPUs
00:00:00.342462 PcBios: Using the 386+ BIOS image.
00:00:00.342593 PcBios: MPS table at 000e1300
00:00:00.343061 PcBios: fCheckShutdownStatusForSoftReset=true   fClearShutdownStatusOnHardReset=true 
00:00:00.343349 CPUM: SetGuestCpuIdFeature: Enabled xAPIC
00:00:00.343735 IOAPIC: Using implementation 2.0! Chipset type ICH9
00:00:00.343865 PIT: mode=3 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.344182 VMMDev: cbDefaultBudget: 535 351 424 (1fe8d080)
00:00:00.344384 SUPLib: MEM_LARGE_PAGES works!
00:00:00.345906 Shared Folders service loaded
00:00:00.346829 Guest Control service loaded
00:00:00.348498 VIRTIOSCSI0: Targets=3 Bootable=true  (unimplemented) R0Enabled=false RCEnabled=false
00:00:00.349595 DrvVD: Flushes will be ignored
00:00:00.349641 DrvVD: Async flushes will be passed to the disk
00:00:00.351572 VD: VDInit finished with VINF_SUCCESS
00:00:00.351951 VD: Opening the disk took 940238 ns
00:00:00.352202 DrvVD: Flushes will be ignored
00:00:00.352224 DrvVD: Async flushes will be passed to the disk
00:00:00.366006 VD: Opening the disk took 12567833 ns
00:00:00.366172 DrvVD: Flushes will be ignored
00:00:00.366185 DrvVD: Async flushes will be passed to the disk
00:00:00.377684 VD: Opening the disk took 9947954 ns
00:00:00.377988 IntNet#0: szNetwork={HostInterfaceNetworking-Realtek PCIe GBE Family Controller} enmTrunkType=3 szTrunk={\DEVICE\{29D241F2-5681-42E5-BBC4-65E21D0CE72E}} fFlags=0x8000 cbRecv=325632 cbSend=196608 fIgnoreConnectFailure=false
00:00:00.385580 SUP: seg #0: R   0x00000000 LB 0x00001000
00:00:00.385603 SUP: seg #1: R X 0x00001000 LB 0x0001f000
00:00:00.385612 SUP: seg #2: R   0x00020000 LB 0x0000c000
00:00:00.385619 SUP: seg #3: RW  0x0002c000 LB 0x00001000
00:00:00.385624 SUP: seg #4: R   0x0002d000 LB 0x00002000
00:00:00.385630 SUP: seg #5: RW  0x0002f000 LB 0x00001000
00:00:00.385636 SUP: seg #6: R   0x00030000 LB 0x00001000
00:00:00.385641 SUP: seg #7: RWX 0x00031000 LB 0x00001000
00:00:00.385647 SUP: seg #8: R   0x00032000 LB 0x00002000
00:00:00.385711 SUP: Loaded Ld9BoxDDR0.r0 (C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0) at 0xXXXXXXXXXXXXXXXX - ModuleInit at XXXXXXXXXXXXXXXX and ModuleTerm at XXXXXXXXXXXXXXXX using the native ring-0 loader
00:00:00.385720 SUP: windbg> .reload /f C:\Program Files\ldplayer9box\Ld9BoxDDR0.r0=0xXXXXXXXXXXXXXXXX
00:00:00.390043 fastpipe: load host successs mod=0000000006aad480, path=C:\Program Files\ldplayer9box\libOpenglRender.dll
00:00:00.390062 fastpipe: GetFunctionAddr success mod=0000000006aad480, lpszFuncName=OnLoad
00:00:00.390582 fastpipe: load host successs mod=0000000006aad100, path=C:\Program Files\ldplayer9box\host_manager.dll
00:00:00.390598 fastpipe: GetFunctionAddr success mod=0000000006aad100, lpszFuncName=OnLoad
00:00:00.392358 PGM: The CPU physical address width is 39 bits
00:00:00.392379 PGM: PGMR3InitFinalize: 4 MB PSE mask 0000007fffffffff -> VINF_SUCCESS
00:00:00.392551 TM: TMR3InitFinalize: fTSCModeSwitchAllowed=false
00:00:00.392911 VMM: Thread-context hooks unavailable
00:00:00.392930 VMM: RTThreadPreemptIsPending() can be trusted
00:00:00.392944 VMM: Kernel preemption is possible
00:00:00.393043 EM: Exit history optimizations: enabled=true  enabled-r0=true  enabled-r0-no-preemption=false
00:00:00.393078 APIC: fPostedIntrsEnabled=false fVirtApicRegsEnabled=false fSupportsTscDeadline=false
00:00:00.393096 TMR3UtcNow: nsNow=1 754 033 031 566 597 500 nsPrev=0 -> cNsDelta=1 754 033 031 566 597 500 (offLag=0 offVirtualSync=0 offVirtualSyncGivenUp=0, NowAgain=1 754 033 031 566 597 500)
00:00:00.393114 VMM: fUsePeriodicPreemptionTimers=false
00:00:00.393160 CPUM: Logical host processors: 8 present, 8 max, 8 online, online mask: 00000000000000ff
00:00:00.393161 CPUM: Physical host cores: 4
00:00:00.393162 ************************* CPUID dump ************************
00:00:00.393170          Raw Standard CPUID Leaves
00:00:00.393170      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.393173 Gst: 00000000/0000  00000016 756e6547 6c65746e 49656e69
00:00:00.393174 Hst:                00000016 756e6547 6c65746e 49656e69
00:00:00.393175 Gst: 00000001/0000  000906e9 00040800 801a2201 178bfbff
00:00:00.393177 Hst:                000906e9 01100800 fedaf387 bfebfbff
00:00:00.393178 Gst: 00000002/0000  76036301 00f0b5ff 00000000 00c30000
00:00:00.393179 Hst:                76036301 00f0b5ff 00000000 00c30000
00:00:00.393180 Gst: 00000003/0000  00000000 00000000 00000000 00000000
00:00:00.393181 Hst:                00000000 00000000 00000000 00000000
00:00:00.393182 Gst: 00000004/0000  0c000121 01c0003f 0000003f 00000000
00:00:00.393183 Hst:                1c004121 01c0003f 0000003f 00000000
00:00:00.393184 Gst: 00000004/0001  0c000122 01c0003f 0000003f 00000000
00:00:00.393185 Hst:                1c004122 01c0003f 0000003f 00000000
00:00:00.393186 Gst: 00000004/0002  0c000143 00c0003f 000003ff 00000000
00:00:00.393187 Hst:                1c004143 00c0003f 000003ff 00000000
00:00:00.393188 Gst: 00000004/0003  0c000163 03c0003f 00001fff 00000002
00:00:00.393189 Hst:                1c03c163 03c0003f 00001fff 00000002
00:00:00.393190 Gst: 00000004/0004  0c000000 00000000 00000000 00000000
00:00:00.393191 Hst:                00000000 00000000 00000000 00000000
00:00:00.393192 Gst: 00000005/0000  00000000 00000000 00000000 00000000
00:00:00.393193 Hst:                00000000 00000000 00000000 00000000
00:00:00.393194 Gst: 00000006/0000  00000000 00000000 00000000 00000000
00:00:00.393194 Hst:                000007f3 00000002 00000009 00000000
00:00:00.393195 Gst: 00000007/0000  00000000 00002401 00000000 3c000400
00:00:00.393196 Hst:                00000000 009c6fbb 00000000 bc000400
00:00:00.393197 Gst: 00000007/0001  00000000 00000000 00000000 00000000
00:00:00.393198 Hst:                00000000 00000000 00000000 00000000
00:00:00.393199 Gst: 00000008/0000  00000000 00000000 00000000 00000000
00:00:00.393199 Hst:                00000000 00000000 00000000 00000000
00:00:00.393200 Gst: 00000009/0000  00000000 00000000 00000000 00000000
00:00:00.393201 Hst:                00000000 00000000 00000000 00000000
00:00:00.393202 Gst: 0000000a/0000  00000000 00000000 00000000 00000000
00:00:00.393202 Hst:                07300404 00000000 00000000 00000603
00:00:00.393203 Gst: 0000000b/0000  00000000 00000001 00000100 00000000
00:00:00.393204 Hst:                00000001 00000002 00000100 00000001
00:00:00.393205 Gst: 0000000b/0001  00000002 00000004 00000201 00000000
00:00:00.393206 Hst:                00000004 00000008 00000201 00000001
00:00:00.393207 Gst: 0000000b/0002  00000000 00000000 00000002 00000000
00:00:00.393207 Hst:                00000000 00000000 00000002 00000001
00:00:00.393208 Gst: 0000000c/0000  00000000 00000000 00000000 00000000
00:00:00.393209 Hst:                00000000 00000000 00000000 00000000
00:00:00.393210 Gst: 0000000d/0000  00000000 00000000 00000000 00000000
00:00:00.393210 Hst:                0000001f 00000440 00000440 00000000
00:00:00.393212 Gst: 0000000d/0001  00000000 00000000 00000000 00000000
00:00:00.393212 Hst:                0000000f 000003c0 00000000 00000000
00:00:00.393213 Gst: 0000000d/0002  00000000 00000000 00000000 00000000
00:00:00.393214 Hst:                00000100 00000240 00000000 00000000
00:00:00.393215 Gst: 0000000d/0003  00000000 00000000 00000000 00000000
00:00:00.393215 Hst:                00000040 000003c0 00000000 00000000
00:00:00.393216 Gst: 0000000d/0004  00000000 00000000 00000000 00000000
00:00:00.393217 Hst:                00000040 00000400 00000000 00000000
00:00:00.393218 Gst: 0000000d/0005  00000000 00000000 00000000 00000000
00:00:00.393218 Hst:                00000000 00000000 00000000 00000000
00:00:00.393274 Gst: 0000000e/0000  00000000 00000000 00000000 00000000
00:00:00.393275 Hst:                00000000 00000000 00000000 00000000
00:00:00.393276 Gst: 0000000f/0000  00000000 00000000 00000000 00000000
00:00:00.393276 Hst:                00000000 00000000 00000000 00000000
00:00:00.393277 Gst: 00000010/0000  00000000 00000000 00000000 00000000
00:00:00.393278 Hst:                00000000 00000000 00000000 00000000
00:00:00.393279 Gst: 00000011/0000  00000000 00000000 00000000 00000000
00:00:00.393279 Hst:                00000000 00000000 00000000 00000000
00:00:00.393295 Gst: 00000012/0000  00000000 00000000 00000000 00000000
00:00:00.393296 Hst:                00000000 00000000 00000000 00000000
00:00:00.393297 Gst: 00000013/0000  00000000 00000000 00000000 00000000
00:00:00.393297 Hst:                00000000 00000000 00000000 00000000
00:00:00.393298 Gst: 00000014/0000  00000000 00000000 00000000 00000000
00:00:00.393299 Hst:                00000001 0000000f 00000003 00000000
00:00:00.393300 Hst: 00000015/0000  00000002 0000012c 00000000 00000000
00:00:00.393301 Hst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:00.393302                                Name: GenuineIntel
00:00:00.393303                            Supports: 0x00000000-0x00000016
00:00:00.393306                              Family:  6 	Extended: 0 	Effective: 6
00:00:00.393308                               Model: 14 	Extended: 9 	Effective: 158
00:00:00.393309                            Stepping: 9
00:00:00.393310                                Type: 0 (primary)
00:00:00.393311                             APIC ID: 0x00
00:00:00.393312                        Logical CPUs: 4
00:00:00.393313                        CLFLUSH Size: 8
00:00:00.393314                            Brand ID: 0x00
00:00:00.393315 Features
00:00:00.393316   Mnemonic - Description                                  = guest (host)
00:00:00.393318   FPU - x87 FPU on Chip                                   = 1 (1)
00:00:00.393320   VME - Virtual 8086 Mode Enhancements                    = 1 (1)
00:00:00.393321   DE - Debugging extensions                               = 1 (1)
00:00:00.393322   PSE - Page Size Extension                               = 1 (1)
00:00:00.393323   TSC - Time Stamp Counter                                = 1 (1)
00:00:00.393324   MSR - Model Specific Registers                          = 1 (1)
00:00:00.393326   PAE - Physical Address Extension                        = 1 (1)
00:00:00.393327   MCE - Machine Check Exception                           = 1 (1)
00:00:00.393329   CX8 - CMPXCHG8B instruction                             = 1 (1)
00:00:00.393330   APIC - APIC On-Chip                                     = 1 (1)
00:00:00.393332   SEP - SYSENTER and SYSEXIT Present                      = 1 (1)
00:00:00.393333   MTRR - Memory Type Range Registers                      = 1 (1)
00:00:00.393334   PGE - PTE Global Bit                                    = 1 (1)
00:00:00.393336   MCA - Machine Check Architecture                        = 1 (1)
00:00:00.393337   CMOV - Conditional Move instructions                    = 1 (1)
00:00:00.393338   PAT - Page Attribute Table                              = 1 (1)
00:00:00.393340   PSE-36 - 36-bit Page Size Extension                     = 1 (1)
00:00:00.393342   PSN - Processor Serial Number                           = 0 (0)
00:00:00.393343   CLFSH - CLFLUSH instruction                             = 1 (1)
00:00:00.393344   DS - Debug Store                                        = 0 (1)
00:00:00.393346   ACPI - Thermal Mon. & Soft. Clock Ctrl.                 = 0 (1)
00:00:00.393347   MMX - Intel MMX Technology                              = 1 (1)
00:00:00.393348   FXSR - FXSAVE and FXRSTOR instructions                  = 1 (1)
00:00:00.393349   SSE - SSE support                                       = 1 (1)
00:00:00.393351   SSE2 - SSE2 support                                     = 1 (1)
00:00:00.393352   SS - Self Snoop                                         = 0 (1)
00:00:00.393353   HTT - Hyper-Threading Technology                        = 1 (1)
00:00:00.393354   TM - Therm. Monitor                                     = 0 (1)
00:00:00.393356   PBE - Pending Break Enabled                             = 0 (1)
00:00:00.393357   SSE3 - SSE3 support                                     = 1 (1)
00:00:00.393358   PCLMUL - PCLMULQDQ support (for AES-GCM)                = 0 (1)
00:00:00.393359   DTES64 - DS Area 64-bit Layout                          = 0 (1)
00:00:00.393360   MONITOR - MONITOR/MWAIT instructions                    = 0 (0)
00:00:00.393361   CPL-DS - CPL Qualified Debug Store                      = 0 (0)
00:00:00.393362   VMX - Virtual Machine Extensions                        = 0 (0)
00:00:00.393363   SMX - Safer Mode Extensions                             = 0 (0)
00:00:00.393364   EST - Enhanced SpeedStep Technology                     = 0 (1)
00:00:00.393365   TM2 - Terminal Monitor 2                                = 0 (1)
00:00:00.393366   SSSE3 - Supplemental Streaming SIMD Extensions 3        = 1 (1)
00:00:00.393367   CNTX-ID - L1 Context ID                                 = 0 (0)
00:00:00.393368   SDBG - Silicon Debug interface                          = 0 (0)
00:00:00.393369   FMA - Fused Multiply Add extensions                     = 0 (1)
00:00:00.393370   CX16 - CMPXCHG16B instruction                           = 1 (1)
00:00:00.393371   TPRUPDATE - xTPR Update Control                         = 0 (1)
00:00:00.393372   PDCM - Perf/Debug Capability MSR                        = 0 (1)
00:00:00.393373   PCID - Process Context Identifiers                      = 1 (1)
00:00:00.393374   DCA - Direct Cache Access                               = 0 (0)
00:00:00.393375   SSE4_1 - SSE4_1 support                                 = 1 (1)
00:00:00.393376   SSE4_2 - SSE4_2 support                                 = 1 (1)
00:00:00.393377   X2APIC - x2APIC support                                 = 0 (0)
00:00:00.393379   MOVBE - MOVBE instruction                               = 0 (1)
00:00:00.393380   POPCNT - POPCNT instruction                             = 0 (1)
00:00:00.393381   TSCDEADL - Time Stamp Counter Deadline                  = 0 (0)
00:00:00.393382   AES - AES instructions                                  = 0 (1)
00:00:00.393383   XSAVE - XSAVE instruction                               = 0 (1)
00:00:00.393384   OSXSAVE - OSXSAVE instruction                           = 0 (1)
00:00:00.393385   AVX - AVX support                                       = 0 (1)
00:00:00.393386   F16C - 16-bit floating point conversion instructions    = 0 (1)
00:00:00.393387   RDRAND - RDRAND instruction                             = 0 (1)
00:00:00.393388   HVP - Hypervisor Present (we're a guest)                = 1 (1)
00:00:00.393389 Structured Extended Feature Flags Enumeration (leaf 7):
00:00:00.393390   Mnemonic - Description                                  = guest (host)
00:00:00.393390   FSGSBASE - RDFSBASE/RDGSBASE/WRFSBASE/WRGSBASE instr.   = 1 (1)
00:00:00.393391   TSCADJUST - Supports MSR_IA32_TSC_ADJUST                = 0 (1)
00:00:00.393392   SGX - Supports Software Guard Extensions                = 0 (0)
00:00:00.393393   BMI1 - Advanced Bit Manipulation extension 1            = 0 (1)
00:00:00.393394   HLE - Hardware Lock Elision                             = 0 (1)
00:00:00.393395   AVX2 - Advanced Vector Extensions 2                     = 0 (1)
00:00:00.393395   FDP_EXCPTN_ONLY - FPU DP only updated on exceptions     = 0 (0)
00:00:00.393396   SMEP - Supervisor Mode Execution Prevention             = 0 (1)
00:00:00.393397   BMI2 - Advanced Bit Manipulation extension 2            = 0 (1)
00:00:00.393398   ERMS - Enhanced REP MOVSB/STOSB instructions            = 0 (1)
00:00:00.393398   INVPCID - INVPCID instruction                           = 1 (1)
00:00:00.393399   RTM - Restricted Transactional Memory                   = 0 (1)
00:00:00.393400   PQM - Platform Quality of Service Monitoring            = 0 (0)
00:00:00.393401   DEPFPU_CS_DS - Deprecates FPU CS, FPU DS values if set  = 1 (1)
00:00:00.393402   MPE - Intel Memory Protection Extensions                = 0 (1)
00:00:00.393403   PQE - Platform Quality of Service Enforcement           = 0 (0)
00:00:00.393403   AVX512F - AVX512 Foundation instructions                = 0 (0)
00:00:00.393404   RDSEED - RDSEED instruction                             = 0 (1)
00:00:00.393405   ADX - ADCX/ADOX instructions                            = 0 (1)
00:00:00.393406   SMAP - Supervisor Mode Access Prevention                = 0 (1)
00:00:00.393407   CLFLUSHOPT - CLFLUSHOPT (Cache Line Flush) instruction  = 0 (1)
00:00:00.393408   INTEL_PT - Intel Processor Trace                        = 0 (0)
00:00:00.393409   AVX512PF - AVX512 Prefetch instructions                 = 0 (0)
00:00:00.393410   AVX512ER - AVX512 Exponential & Reciprocal instructions = 0 (0)
00:00:00.393410   AVX512CD - AVX512 Conflict Detection instructions       = 0 (0)
00:00:00.393411   SHA - Secure Hash Algorithm extensions                  = 0 (0)
00:00:00.393412   PREFETCHWT1 - PREFETCHWT1 instruction                   = 0 (0)
00:00:00.393413   UMIP - User mode insturction prevention                 = 0 (0)
00:00:00.393414   PKU - Protection Key for Usermode pages                 = 0 (0)
00:00:00.393414   OSPKE - CR4.PKU mirror                                  = 0 (0)
00:00:00.393416   MAWAU - Value used by BNDLDX & BNDSTX                   = 0x0 (0x0)
00:00:00.393417   RDPID - Read processor ID support                       = 0 (0)
00:00:00.393418   SGX_LC - Supports SGX Launch Configuration              = 0 (0)
00:00:00.393418   MD_CLEAR - Supports MDS related buffer clearing         = 1 (1)
00:00:00.393419   IBRS_IBPB - IA32_SPEC_CTRL.IBRS and IA32_PRED_CMD.IBPB  = 1 (1)
00:00:00.393420   STIBP - Supports IA32_SPEC_CTRL.STIBP                   = 1 (1)
00:00:00.393421   FLUSH_CMD - Supports IA32_FLUSH_CMD                     = 1 (1)
00:00:00.393422   ARCHCAP - Supports IA32_ARCH_CAP                        = 1 (1)
00:00:00.393423   CORECAP - Supports IA32_CORE_CAP                        = 0 (0)
00:00:00.393423   SSBD - Supports IA32_SPEC_CTRL.SSBD                     = 0 (1)
00:00:00.393425 Processor Extended State Enumeration (leaf 0xd):
00:00:00.393426    XSAVE area cur/max size by XCR0, guest: 0x0/0x0
00:00:00.393426     XSAVE area cur/max size by XCR0, host: 0x440/0x440
00:00:00.393427                    Valid XCR0 bits, guest: 0x00000000`00000000
00:00:00.393428                     Valid XCR0 bits, host: 0x00000000`0000001f ( x87 SSE YMM_Hi128 BNDREGS BNDCSR )
00:00:00.393430                     XSAVE features, guest:
00:00:00.393431                      XSAVE features, host: XSAVEOPT XSAVEC XGETBC1 XSAVES
00:00:00.393436       XSAVE area cur size XCR0|XSS, guest: 0x0
00:00:00.393436        XSAVE area cur size XCR0|XSS, host: 0x3c0
00:00:00.393437                Valid IA32_XSS bits, guest: 0x00000000`00000000
00:00:00.393438                 Valid IA32_XSS bits, host: 0x00000000`00000000
00:00:00.393439   State #2, host:  off=0x0240, cb=0x0100 IA32_XSS-bit -- YMM_Hi128
00:00:00.393440   State #3, host:  off=0x03c0, cb=0x0040 IA32_XSS-bit -- BNDREGS
00:00:00.393442   State #4, host:  off=0x0400, cb=0x0040 IA32_XSS-bit -- BNDCSR
00:00:00.393491          Unknown CPUID Leaves
00:00:00.393491      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.393492 Gst: 00000014/0001  00000000 00000000 00000000 00000000
00:00:00.393493 Hst:                02490002 003f3fff 00000000 00000000
00:00:00.393494 Gst: 00000014/0002  00000000 00000000 00000000 00000000
00:00:00.393494 Hst:                00000000 00000000 00000000 00000000
00:00:00.393495 Gst: 00000015/0000  00000000 00000000 00000000 00000000
00:00:00.393496 Hst:                00000002 0000012c 00000000 00000000
00:00:00.393497 Gst: 00000016/0000  00000000 00000000 00000000 00000000
00:00:00.393497 Hst:                00000000 00000000 00000000 00000000
00:00:00.393498          Raw Hypervisor CPUID Leaves
00:00:00.393498      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.393499 Gst: 40000000/0000  40000001 4b4d564b 564b4d56 0000004d
00:00:00.393500 Hst:                4000000c 7263694d 666f736f 76482074
00:00:00.393501 Gst: 40000001/0000  01000089 00000000 00000000 00000000
00:00:00.393502 Hst:                31237648 00000000 00000000 00000000
00:00:00.393503          Raw Extended CPUID Leaves
00:00:00.393503      Leaf/sub-leaf  eax      ebx      ecx      edx
00:00:00.393504 Gst: 80000000/0000  80000008 00000000 00000000 00000000
00:00:00.393504 Hst:                80000008 00000000 00000000 00000000
00:00:00.393505 Gst: 80000001/0000  00000000 00000000 00000001 28100800
00:00:00.393506 Hst:                00000000 00000000 00000121 2c100800
00:00:00.393507 Gst: 80000002/0000  65746e49 2952286c 726f4320 4d542865
00:00:00.393508 Hst:                65746e49 2952286c 726f4320 4d542865
00:00:00.393509 Gst: 80000003/0000  37692029 3037372d 50432030 20402055
00:00:00.393510 Hst:                37692029 3037372d 50432030 20402055
00:00:00.393511 Gst: 80000004/0000  30362e33 007a4847 00000000 00000000
00:00:00.393511 Hst:                30362e33 007a4847 00000000 00000000
00:00:00.393512 Gst: 80000005/0000  00000000 00000000 00000000 00000000
00:00:00.393513 Hst:                00000000 00000000 00000000 00000000
00:00:00.393514 Gst: 80000006/0000  00000000 00000000 01006040 00000000
00:00:00.393514 Hst:                00000000 00000000 01006040 00000000
00:00:00.393515 Gst: 80000007/0000  00000000 00000000 00000000 00000100
00:00:00.393516 Hst:                00000000 00000000 00000000 00000100
00:00:00.393517 Gst: 80000008/0000  00003027 00000000 00000000 00000000
00:00:00.393518 Hst:                00003027 00000000 00000000 00000000
00:00:00.393518 Ext Name:                        
00:00:00.393519 Ext Supports:                    0x80000000-0x80000008
00:00:00.393519 Family:                          0  	Extended: 0 	Effective: 0
00:00:00.393520 Model:                           0  	Extended: 0 	Effective: 0
00:00:00.393520 Stepping:                        0
00:00:00.393521 Brand ID:                        0x000
00:00:00.393521 Ext Features
00:00:00.393522   Mnemonic - Description                                  = guest (host)
00:00:00.393522   FPU - x87 FPU on Chip                                   = 0 (0)
00:00:00.393523   VME - Virtual 8086 Mode Enhancements                    = 0 (0)
00:00:00.393524   DE - Debugging extensions                               = 0 (0)
00:00:00.393538   PSE - Page Size Extension                               = 0 (0)
00:00:00.393540   TSC - Time Stamp Counter                                = 0 (0)
00:00:00.393541   MSR - K86 Model Specific Registers                      = 0 (0)
00:00:00.393542   PAE - Physical Address Extension                        = 0 (0)
00:00:00.393543   MCE - Machine Check Exception                           = 0 (0)
00:00:00.393544   CX8 - CMPXCHG8B instruction                             = 0 (0)
00:00:00.393545   APIC - APIC On-Chip                                     = 0 (0)
00:00:00.393546   SEP - SYSCALL/SYSRET                                    = 1 (1)
00:00:00.393547   MTRR - Memory Type Range Registers                      = 0 (0)
00:00:00.393563   PGE - PTE Global Bit                                    = 0 (0)
00:00:00.393564   MCA - Machine Check Architecture                        = 0 (0)
00:00:00.393565   CMOV - Conditional Move instructions                    = 0 (0)
00:00:00.393566   PAT - Page Attribute Table                              = 0 (0)
00:00:00.393567   PSE-36 - 36-bit Page Size Extension                     = 0 (0)
00:00:00.393568   NX - No-Execute/Execute-Disable                         = 1 (1)
00:00:00.393569   AXMMX - AMD Extensions to MMX instructions              = 0 (0)
00:00:00.393570   MMX - Intel MMX Technology                              = 0 (0)
00:00:00.393571   FXSR - FXSAVE and FXRSTOR Instructions                  = 0 (0)
00:00:00.393572   FFXSR - AMD fast FXSAVE and FXRSTOR instructions        = 0 (0)
00:00:00.393572   Page1GB - 1 GB large page                               = 0 (1)
00:00:00.393573   RDTSCP - RDTSCP instruction                             = 1 (1)
00:00:00.393574   LM - AMD64 Long Mode                                    = 1 (1)
00:00:00.393576   3DNOWEXT - AMD Extensions to 3DNow                      = 0 (0)
00:00:00.393577   3DNOW - AMD 3DNow                                       = 0 (0)
00:00:00.393578   LahfSahf - LAHF/SAHF support in 64-bit mode             = 1 (1)
00:00:00.393579   CmpLegacy - Core multi-processing legacy mode           = 0 (0)
00:00:00.393579   SVM - AMD Secure Virtual Machine extensions             = 0 (0)
00:00:00.393580   EXTAPIC - AMD Extended APIC registers                   = 0 (0)
00:00:00.393581   CR8L - AMD LOCK MOV CR0 means MOV CR8                   = 0 (0)
00:00:00.393583   ABM - AMD Advanced Bit Manipulation                     = 0 (1)
00:00:00.393584   SSE4A - SSE4A instructions                              = 0 (0)
00:00:00.393585   MISALIGNSSE - AMD Misaligned SSE mode                   = 0 (0)
00:00:00.393586   3DNOWPRF - AMD PREFETCH and PREFETCHW instructions      = 0 (1)
00:00:00.393586   OSVW - AMD OS Visible Workaround                        = 0 (0)
00:00:00.393587   IBS - Instruct Based Sampling                           = 0 (0)
00:00:00.393588   XOP - Extended Operation support                        = 0 (0)
00:00:00.393589   SKINIT - SKINIT, STGI, and DEV support                  = 0 (0)
00:00:00.393590   WDT - AMD Watchdog Timer support                        = 0 (0)
00:00:00.393591   LWP - Lightweight Profiling support                     = 0 (0)
00:00:00.393592   FMA4 - Four operand FMA instruction support             = 0 (0)
00:00:00.393593   NodeId - NodeId in MSR C001_100C                        = 0 (0)
00:00:00.393594   TBM - Trailing Bit Manipulation instructions            = 0 (0)
00:00:00.393595   TOPOEXT - Topology Extensions                           = 0 (0)
00:00:00.393596   PRFEXTCORE - Performance Counter Extensions support     = 0 (0)
00:00:00.393597   PRFEXTNB - NB Performance Counter Extensions support    = 0 (0)
00:00:00.393598   DATABPEXT - Data-access Breakpoint Extension            = 0 (0)
00:00:00.393599   PERFTSC - Performance Time Stamp Counter                = 0 (0)
00:00:00.393602   PCX_L2I - L2I/L3 Performance Counter Extensions         = 0 (0)
00:00:00.393603   MWAITX - MWAITX and MONITORX instructions               = 0 (0)
00:00:00.393605 Full Name:                       "Intel(R) Core(TM) i7-7700 CPU @ 3.60GHz"
00:00:00.393605 TLB 2/4M Instr/Uni:              res0     0 entries
00:00:00.393606 TLB 2/4M Data:                   res0     0 entries
00:00:00.393607 TLB 4K Instr/Uni:                res0     0 entries
00:00:00.393607 TLB 4K Data:                     res0     0 entries
00:00:00.393608 L1 Instr Cache Line Size:        0 bytes
00:00:00.393609 L1 Instr Cache Lines Per Tag:    0
00:00:00.393609 L1 Instr Cache Associativity:    res0  
00:00:00.393610 L1 Instr Cache Size:             0 KB
00:00:00.393610 L1 Data Cache Line Size:         0 bytes
00:00:00.393611 L1 Data Cache Lines Per Tag:     0
00:00:00.393611 L1 Data Cache Associativity:     res0  
00:00:00.393612 L1 Data Cache Size:              0 KB
00:00:00.393612 L2 TLB 2/4M Instr/Uni:           off       0 entries
00:00:00.393613 L2 TLB 2/4M Data:                off       0 entries
00:00:00.393614 L2 TLB 4K Instr/Uni:             off       0 entries
00:00:00.393615 L2 TLB 4K Data:                  off       0 entries
00:00:00.393615 L2 Cache Line Size:              0 bytes
00:00:00.393616 L2 Cache Lines Per Tag:          0
00:00:00.393616 L2 Cache Associativity:          off   
00:00:00.393617 L2 Cache Size:                   0 KB
00:00:00.393619   TS - Temperature Sensor                                 = 0 (0)
00:00:00.393621   FID - Frequency ID control                              = 0 (0)
00:00:00.393624   VID - Voltage ID control                                = 0 (0)
00:00:00.393626   TscInvariant - Invariant Time Stamp Counter             = 1 (1)
00:00:00.393628   CBP - Core Performance Boost                            = 0 (0)
00:00:00.393630   EffFreqRO - Read-only Effective Frequency Interface     = 0 (0)
00:00:00.393631   ProcFdbkIf - Processor Feedback Interface               = 0 (0)
00:00:00.393633   ProcPwrRep - Core power reporting interface support     = 0 (0)
00:00:00.393636 Physical Address Width:          39 bits
00:00:00.393636 Virtual Address Width:           48 bits
00:00:00.393637 Guest Physical Address Width:    0 bits
00:00:00.393637 Physical Core Count:             1
00:00:00.393639 
00:00:00.393639 ******************** End of CPUID dump **********************
00:00:00.393662 VMEmt: Halt method global1 (5)
00:00:00.393782 VMEmt: HaltedGlobal1 config: cNsSpinBlockThresholdCfg=50000
00:00:00.393852 Changing the VM state from 'CREATING' to 'CREATED'
00:00:00.395655 SharedFolders host service: Adding host mapping
00:00:00.395683     Host path 'C:/Users/<USER>/Documents/leidian9/Applications', map name 'Applications', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.396037 SharedFolders host service: Adding host mapping
00:00:00.396055     Host path 'C:\Users\<USER>\AppData\Roaming\leidian9\android_bug', map name 'Bug', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.396325 SharedFolders host service: Adding host mapping
00:00:00.396339     Host path 'C:/Users/<USER>/Documents/leidian9/Misc', map name 'Misc', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.396605 SharedFolders host service: Adding host mapping
00:00:00.396618     Host path 'C:/Users/<USER>/Documents/leidian9/Pictures', map name 'Pictures', writable, automount=true, automntpnt=, create_symlinks=false, missing=false
00:00:00.396887 Changing the VM state from 'CREATED' to 'POWERING_ON'
00:00:00.397076 virtioCoreVirtqAvailBufCount: Driver not ready or queue controlq not enabled
00:00:00.397103 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<0> not enabled
00:00:00.397391 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<1> not enabled
00:00:00.397533 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<2> not enabled
00:00:00.397867 virtioCoreVirtqAvailBufCount: Driver not ready or queue requestq<3> not enabled
00:00:00.398052 Changing the VM state from 'POWERING_ON' to 'RUNNING'
00:00:00.398131 Console: Machine state changed to 'Running'
00:00:00.404257 VMMDev: Guest Log: BIOS: VirtualBox 6.1.34
00:00:00.404568 PCI: Setting up resources and interrupts
00:00:00.404819 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.428901 VMMDev: Guest Log: CPUID EDX: 0x178bfbff
00:00:00.429487 VMMDev: Guest Log: BIOS: No PCI IDE controller, not probing IDE
00:00:00.435939 VMMDev: Guest Log: BIOS: SCSI 0-ID#0: LCHS=326/255/63 0x0000000000503f2a sectors
00:00:00.437428 VMMDev: Guest Log: BIOS: SCSI 1-ID#1: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.438673 VMMDev: Guest Log: BIOS: SCSI 2-ID#2: LCHS=33410/255/63 0x0000000020000000 sectors
00:00:00.472118 PIT: mode=2 count=0x48d3 (18643) - 64.00 Hz (ch=0)
00:00:00.472155 PIT: mode=2 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:00.472395 VMMDev: Guest Log: BIOS: Boot : bseqnr=1, bootseq=0002
00:00:00.472928 VMMDev: Guest Log: BIOS: Booting from Hard Disk...
00:00:00.499111 VMMDev: Guest Log: int13_harddisk_ext: function 41, unmapped device for ELDL=83
00:00:00.499475 VMMDev: Guest Log: int13_harddisk: function 08, unmapped device for ELDL=83
00:00:00.553531 VBoxHeadless: starting event loop
00:00:00.698910 VMMDev: Guest Log: BIOS: KBD: unsupported int 16h function 03
00:00:00.699592 VMMDev: Guest Log: BIOS: AX=0305 BX=0000 CX=0000 DX=0000 
00:00:00.909052 GIM: KVM: VCPU  0: Enabled system-time struct. at 0x000000019ff7c000 - u32TscScale=0x8e38e48f i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x6d029e3d uVirtNanoTS=0x1e47d6d5 TscKHz=3599998
00:00:00.909083 TM: Host/VM is not suitable for using TSC mode 'RealTscOffset', request to change TSC mode ignored
00:00:01.556120 GIM: KVM: Enabled wall-clock struct. at 0x00000000010c32a8 - u32Sec=1754033032 u32Nano=728288814 uVersion=2
00:00:01.970216 PIT: mode=2 count=0xf89 (3977) - 300.02 Hz (ch=0)
00:00:02.215359 PIT: mode=0 count=0x10000 (65536) - 18.20 Hz (ch=0)
00:00:02.257903 GIM: KVM: VCPU  1: Enabled system-time struct. at 0x000000019ff7c040 - u32TscScale=0x8e38e48f i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x18e75d91f uVirtNanoTS=0x6eaef5fb TscKHz=3599998
00:00:02.257953 TM: Host/VM is not suitable for using TSC mode 'RealTscOffset', request to change TSC mode ignored
00:00:02.277000 GIM: KVM: VCPU  2: Enabled system-time struct. at 0x000000019ff7c080 - u32TscScale=0x8e38e48f i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x1928fecbb uVirtNanoTS=0x6fd2a61a TscKHz=3599998
00:00:02.277036 TM: Host/VM is not suitable for using TSC mode 'RealTscOffset', request to change TSC mode ignored
00:00:02.310221 GIM: KVM: VCPU  3: Enabled system-time struct. at 0x000000019ff7c0c0 - u32TscScale=0x8e38e48f i8TscShift=-1 uVersion=2 fFlags=0x1 uTsc=0x199b0d0fd uVirtNanoTS=0x71cd9030 TscKHz=3599998
00:00:02.310251 TM: Host/VM is not suitable for using TSC mode 'RealTscOffset', request to change TSC mode ignored
00:00:04.161709 VMMDev: Guest Additions information report: Version 6.1.36 r152435 '6.1.36'
00:00:04.161760 VMMDev: Guest Additions information report: Interface = 0x00010004 osType = 0x00053100 (Linux >= 2.6, 64-bit)
00:00:04.161900 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:04.162023 VMMDev: vmmDevReqHandler_HeartbeatConfigure: No change (fHeartbeatActive=false)
00:00:04.162053 VMMDev: Heartbeat flatline timer set to trigger after 4 000 000 000 ns
00:00:04.162100 VMMDev: Guest Log: vgdrvHeartbeatInit: Setting up heartbeat to trigger every 2000 milliseconds
00:00:04.165022 VMMDev: Guest Additions capability report: (0x0 -> 0x0) seamless: no, hostWindowMapping: no, graphics: no
00:00:04.165626 VMMDev: Guest Log: vboxguest: Successfully loaded version 6.1.36 r152435
00:00:04.167562 VMMDev: Guest Log: vboxguest: misc device minor 53, IRQ 20, I/O port d020, MMIO at 00000000f0000000 (size 0x400000)
00:00:04.178718 VMMDev: Guest Log: vboxsf: g_fHostFeatures=0x8000000f g_fSfFeatures=0x1 g_uSfLastFunction=29
00:00:04.182353 VMMDev: Guest Log: vboxsf: Successfully loaded version 6.1.36 r152435 on 4.4.146 SMP preempt mod_unload modversions  (LINUX_VERSION_CODE=0x40492)
00:01:28.386366 Console: Machine state changed to 'Stopping'
00:01:28.387255 Console::powerDown(): A request to power off the VM has been issued (mMachineState=Stopping, InUninit=0)
00:01:28.387591 Changing the VM state from 'RUNNING' to 'POWERING_OFF'
00:01:28.387608 ****************** Guest state at power off for VCpu 3 ******************
00:01:28.387617 Guest CPUM (VCPU 3) state: 
00:01:28.387622 rax=0000000000000003 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:01:28.387624 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:01:28.387626 r10=00000000ffff053b r11=0000000000000202 r12=0000000000000000 r13=0000000000000000
00:01:28.387627 r14=ffff880198970000 r15=ffff880198974000
00:01:28.387628 rip=ffffffff80239162 rsp=ffff880198973ef8 rbp=ffff880198974000 iopl=0         nv up ei pl zr na pe nc
00:01:28.387630 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:01:28.387631 ds={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387632 es={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387633 fs={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387634 gs={0000 base=ffff88019fd80000 limit=000fffff flags=00000000}
00:01:28.387635 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:01:28.387636 cr0=0000000080050033 cr2=0000000012f40000 cr3=0000000197f38000 cr4=00000000000206b0
00:01:28.387637 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:01:28.387638 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000ffff0ff0 dr7=0000000000000400
00:01:28.387639 gdtr=ffff88019fd8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:01:28.387641 ldtr={0000 base=00000000 limit=000fffff flags=00000000}
00:01:28.387642 tr  ={0040 base=ffff88019fd84840 limit=00002087 flags=0000008b}
00:01:28.387643 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:01:28.387644 xcr=0000000000000001 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000000)
00:01:28.387647 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:01:28.387648 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:01:28.387649 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387651 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387652 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387654 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387655 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387656 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387658 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387659 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387661 XMM0 =00000000'00000000'00000000'00000000  XMM1 =00000000'00000000'00000011'00000003
00:01:28.387662 XMM2 =00000000'00000000'00000000'0000004c  XMM3 =00000000'00000000'00007fff'f6a22180
00:01:28.387664 XMM4 =746f6e20'64252073'7066206d'6f747375  XMM5 =00000000'00000000'00000000'00000000
00:01:28.387666 XMM6 =00000000'00000000'00000000'00000000  XMM7 =00000000'00000000'00000000'00000000
00:01:28.387668 XMM8 =00000000'00004000'00000000'00004000  XMM9 =00000000'00000004'00000000'00000004
00:01:28.387669 XMM10=00000000'00000000'00000000'00000000  XMM11=00000000'00000000'00000000'00000000
00:01:28.387671 XMM12=00000000'00000000'00000000'00000000  XMM13=00000000'00000000'00000000'00000000
00:01:28.387673 XMM14=00000000'00000000'00000000'00000000  XMM15=00000000'00000000'00000000'00000000
00:01:28.387674 EFER         =0000000000000d01
00:01:28.387675 PAT          =0007040600070406
00:01:28.387675 STAR         =0023001000000000
00:01:28.387676 CSTAR        =ffffffff809e4e70
00:01:28.387677 LSTAR        =ffffffff809e3690
00:01:28.387677 SFMASK       =0000000000047700
00:01:28.387678 KERNELGSBASE =0000000000000000
00:01:28.387679 ***
00:01:28.387685 VCPU[3] hardware virtualization state:
00:01:28.387685 fLocalForcedActions          = 0x0
00:01:28.387686 No/inactive hwvirt state
00:01:28.387687 ***
00:01:28.387690 Guest paging mode (VCPU #3):  AMD64+NX (changed 2 times), A20 enabled (changed 0 times)
00:01:28.387691 Shadow paging mode (VCPU #3): None
00:01:28.387692 Host paging mode:             AMD64+NX
00:01:28.387693 ***
00:01:28.387693 ************** End of Guest state at power off for VCpu 3 ***************
00:01:28.387713 ****************** Guest state at power off for VCpu 2 ******************
00:01:28.387717 Guest CPUM (VCPU 2) state: 
00:01:28.387718 rax=0000000000000002 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:01:28.387720 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:01:28.387722 r10=00000000ffff0561 r11=0000000000000000 r12=0000000000000000 r13=0000000000000000
00:01:28.387723 r14=ffff880198964000 r15=ffff880198968000
00:01:28.387724 rip=ffffffff80239162 rsp=ffff880198967ef8 rbp=ffff880198968000 iopl=0         nv up ei pl zr na pe nc
00:01:28.387740 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:01:28.387741 ds={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387742 es={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387742 fs={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387743 gs={0000 base=ffff88019fd00000 limit=000fffff flags=00000000}
00:01:28.387744 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:01:28.387745 cr0=0000000080050033 cr2=0000000013040000 cr3=00000000dac55000 cr4=00000000000206b0
00:01:28.387746 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:01:28.387747 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000ffff0ff0 dr7=0000000000000400
00:01:28.387748 gdtr=ffff88019fd0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:01:28.387750 ldtr={0000 base=00000000 limit=000fffff flags=00000000}
00:01:28.387751 tr  ={0040 base=ffff88019fd04840 limit=00002087 flags=0000008b}
00:01:28.387752 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:01:28.387753 xcr=0000000000000001 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000000)
00:01:28.387754 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001fa0 MXCSR_MASK=0000ffff
00:01:28.387755 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:01:28.387756 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387758 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387759 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387761 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387762 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387763 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387765 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387766 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387767 XMM0 =00000000'07bdb717'00000000'688c6be0  XMM1 =403dffff'ffffe92a'dd800000'00000000
00:01:28.387770 XMM2 =00007fff'f7031540'00007fff'f7031580  XMM3 =00007fff'f70315c0'00007fff'f7031480
00:01:28.387772 XMM4 =00000000'00000000'00000000'00000000  XMM5 =ffffffff'ffffffff'ffffffff'ffffffff
00:01:28.387773 XMM6 =ffffffff'ffffffff'ffffffff'ffffffff  XMM7 =ffffffff'ffffffff'ffffffff'ffffffff
00:01:28.387775 XMM8 =00000000'00004000'00000000'00004000  XMM9 =00000000'00000004'00000000'00000004
00:01:28.387777 XMM10=00000000'00000000'00000000'00000000  XMM11=00000000'00000000'00000000'00000000
00:01:28.387782 XMM12=00000000'00000000'00000000'00000000  XMM13=00000000'00000000'00000000'00000000
00:01:28.387783 XMM14=00000000'00000000'00000000'00000000  XMM15=00000000'00000000'00000000'00000000
00:01:28.387785 EFER         =0000000000000d01
00:01:28.387785 PAT          =0007040600070406
00:01:28.387786 STAR         =0023001000000000
00:01:28.387787 CSTAR        =ffffffff809e4e70
00:01:28.387787 LSTAR        =ffffffff809e3690
00:01:28.387788 SFMASK       =0000000000047700
00:01:28.387788 KERNELGSBASE =0000000000000000
00:01:28.387790 ***
00:01:28.387791 VCPU[2] hardware virtualization state:
00:01:28.387807 fLocalForcedActions          = 0x0
00:01:28.387807 No/inactive hwvirt state
00:01:28.387809 ***
00:01:28.387810 Guest paging mode (VCPU #2):  AMD64+NX (changed 2 times), A20 enabled (changed 0 times)
00:01:28.387812 Shadow paging mode (VCPU #2): None
00:01:28.387812 Host paging mode:             AMD64+NX
00:01:28.387813 ***
00:01:28.387814 ************** End of Guest state at power off for VCpu 2 ***************
00:01:28.387838 ****************** Guest state at power off for VCpu 1 ******************
00:01:28.387843 Guest CPUM (VCPU 1) state: 
00:01:28.387844 rax=0000000000000001 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:01:28.387846 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:01:28.387854 r10=00000000ffff0551 r11=0000000000000000 r12=0000000000000000 r13=0000000000000000
00:01:28.387855 r14=ffff880198960000 r15=ffff880198964000
00:01:28.387857 rip=ffffffff80239162 rsp=ffff880198963ef8 rbp=ffff880198964000 iopl=0         nv up ei pl zr na pe nc
00:01:28.387859 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:01:28.387877 ds={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387879 es={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387879 fs={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.387880 gs={0000 base=ffff88019fc80000 limit=000fffff flags=00000000}
00:01:28.387881 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:01:28.387882 cr0=0000000080050033 cr2=0000000000437670 cr3=00000000d8930000 cr4=00000000000206b0
00:01:28.387884 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:01:28.387885 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000ffff0ff0 dr7=0000000000000400
00:01:28.387886 gdtr=ffff88019fc8c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:01:28.387888 ldtr={0000 base=00000000 limit=000fffff flags=00000000}
00:01:28.387889 tr  ={0040 base=ffff88019fc84840 limit=00002087 flags=0000008b}
00:01:28.387890 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:01:28.387930 xcr=0000000000000001 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000000)
00:01:28.387932 FCW=037f FSW=0000 FTW=0000 FOP=0000 MXCSR=00001f80 MXCSR_MASK=0000ffff
00:01:28.387933 FPUIP=00000000 CS=0000 Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:01:28.387934 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387937 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387938 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387940 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387941 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387943 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387944 ST(6)=FPR6={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387949 ST(7)=FPR7={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.387950 XMM0 =00000000'000bf733'00000000'00000001  XMM1 =00000000'00000000'e12d84a2'1f43a87a
00:01:28.387953 XMM2 =00000000'00000000'00000000'00000000  XMM3 =566e4675'30623378'52357769'3d323048
00:01:28.387955 XMM4 =22da4941'22da4941'00000000'22da4941  XMM5 =00000000'00000000'00000000'00000000
00:01:28.387957 XMM6 =000080fe'00002000'00000000'00000000  XMM7 =7a626664'e12d84a2'1f43a87a'00000000
00:01:28.387959 XMM8 =00000000'00004000'00000000'00004000  XMM9 =00000000'00000004'00000000'00000004
00:01:28.387960 XMM10=00000000'00000000'00000000'00000000  XMM11=00000000'00000000'00000000'00000000
00:01:28.387962 XMM12=00000000'00000000'00000000'00000000  XMM13=00000000'00000000'00000000'00000000
00:01:28.387967 XMM14=00000000'00000000'00000000'00000000  XMM15=00000000'00000000'00000000'00000000
00:01:28.387969 EFER         =0000000000000d01
00:01:28.387969 PAT          =0007040600070406
00:01:28.387970 STAR         =0023001000000000
00:01:28.387971 CSTAR        =ffffffff809e4e70
00:01:28.387971 LSTAR        =ffffffff809e3690
00:01:28.387972 SFMASK       =0000000000047700
00:01:28.387972 KERNELGSBASE =0000000000000000
00:01:28.387974 ***
00:01:28.387976 VCPU[1] hardware virtualization state:
00:01:28.387977 fLocalForcedActions          = 0x0
00:01:28.387977 No/inactive hwvirt state
00:01:28.387978 ***
00:01:28.387997 Guest paging mode (VCPU #1):  AMD64+NX (changed 2 times), A20 enabled (changed 0 times)
00:01:28.387998 Shadow paging mode (VCPU #1): None
00:01:28.387999 Host paging mode:             AMD64+NX
00:01:28.388000 ***
00:01:28.388000 ************** End of Guest state at power off for VCpu 1 ***************
00:01:28.388031 ****************** Guest state at power off for VCpu 0 ******************
00:01:28.388034 Guest CPUM (VCPU 0) state: 
00:01:28.388035 rax=0000000000000000 rbx=ffffffff80f2b900 rcx=0000000000000000 rdx=0000000000000000
00:01:28.388037 rsi=ffffffff80be5eae rdi=ffffffff80bf9610 r8 =0000000000000000 r9 =0000000000000002
00:01:28.388039 r10=00000000fffefeb6 r11=0000000000000000 r12=0000000000000000 r13=0000000000000000
00:01:28.388040 r14=ffffffff80e00000 r15=ffffffff80e04000
00:01:28.388041 rip=ffffffff80239162 rsp=ffffffff80e03f08 rbp=ffffffff80e04000 iopl=0         nv up ei pl zr na pe nc
00:01:28.388043 cs={0010 base=0000000000000000 limit=ffffffff flags=0000a09b}
00:01:28.388044 ds={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.388045 es={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.388046 fs={0000 base=0000000000000000 limit=000fffff flags=00000000}
00:01:28.388047 gs={0000 base=ffff88019fc00000 limit=000fffff flags=00000000}
00:01:28.388048 ss={0018 base=0000000000000000 limit=ffffffff flags=0000c093}
00:01:28.388049 cr0=0000000080050033 cr2=0000000013080000 cr3=0000000196b78000 cr4=00000000000206b0
00:01:28.388050 dr0=0000000000000000 dr1=0000000000000000 dr2=0000000000000000 dr3=0000000000000000
00:01:28.388051 dr4=0000000000000000 dr5=0000000000000000 dr6=00000000ffff0ff0 dr7=0000000000000400
00:01:28.388053 gdtr=ffff88019fc0c000:007f  idtr=ffffffffff4fb000:0fff  eflags=00000202
00:01:28.388054 ldtr={0000 base=00000000 limit=000fffff flags=00000000}
00:01:28.388070 tr  ={0040 base=ffff88019fc04840 limit=00002087 flags=0000008b}
00:01:28.388071 SysEnter={cs=0010 eip=ffffffff809e4dd0 esp=0000000000000000}
00:01:28.388072 xcr=0000000000000001 xcr1=0000000000000000 xss=0000000000000000 (fXStateMask=0000000000000000)
00:01:28.388073 FCW=037f FSW=0220 FTW=0000 FOP=0000 MXCSR=00001fa1 MXCSR_MASK=0000ffff
00:01:28.388075 FPUIP=f65dcd15 CS=7fff Rsrvd1=0000  FPUDP=00000000 DS=0000 Rsvrd2=0000
00:01:28.388076 ST(0)=FPR0={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388077 ST(1)=FPR1={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388079 ST(2)=FPR2={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388080 ST(3)=FPR3={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388081 ST(4)=FPR4={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388082 ST(5)=FPR5={0000'00000000'00000000} t0 +0.0000000000000000000000 * 2 ^ -16383 (*)
00:01:28.388084 ST(6)=FPR6={3fff'80000000'00000000} t0 +1.0000000000000000000000 * 2 ^ 0 (*)
00:01:28.388085 ST(7)=FPR7={3fff'c0aa5fd3'0fe10e33} t0 +1.0004659642124577345075 * 2 ^ 0 (*)
00:01:28.388087 XMM0 =00000000'00000000'00000000'00000003  XMM1 =00000000'00000000'00000010'00000004
00:01:28.388089 XMM2 =00000000'00000000'00000000'0000002c  XMM3 =00000000'00000000'00007fff'6c017c00
00:01:28.388091 XMM4 =00000000'00000000'00000000'00000000  XMM5 =00000000'00000000'00000000'00000000
00:01:28.388092 XMM6 =00000000'00000000'00000000'00000000  XMM7 =00000000'00000000'00000000'00000000
00:01:28.388094 XMM8 =00000000'00004000'00000000'00004000  XMM9 =00000000'00000004'00000000'00000004
00:01:28.388096 XMM10=00000000'00000000'00000000'ebad807a  XMM11=00000000'00000000'00000000'ebad807b
00:01:28.388097 XMM12=00000000'00000000'00000000'00000000  XMM13=00000000'00000000'00000000'00000000
00:01:28.388099 XMM14=00000000'00000000'00000000'00000000  XMM15=00000000'00000000'00000000'00000000
00:01:28.388100 EFER         =0000000000000d01
00:01:28.388101 PAT          =0007040600070406
00:01:28.388102 STAR         =0023001000000000
00:01:28.388102 CSTAR        =ffffffff809e4e70
00:01:28.388103 LSTAR        =ffffffff809e3690
00:01:28.388103 SFMASK       =0000000000047700
00:01:28.388104 KERNELGSBASE =0000000000000000
00:01:28.388105 ***
00:01:28.388106 VCPU[0] hardware virtualization state:
00:01:28.388107 fLocalForcedActions          = 0x0
00:01:28.388107 No/inactive hwvirt state
00:01:28.388108 ***
00:01:28.388110 Guest paging mode (VCPU #0):  AMD64+NX (changed 182 times), A20 enabled (changed 2 times)
00:01:28.388111 Shadow paging mode (VCPU #0): None
00:01:28.388112 Host paging mode:             AMD64+NX
00:01:28.388113 ***
00:01:28.388115 Active Timers (pVM=00000000041c0000)
00:01:28.388115 pTimerR3         offNext  offPrev  offSched Clock               Time             Expire HzHint State                     Description
00:01:28.388118 0000000004532550 00000000 00000000 00000000 Real           176699308          176699821      0 2-ACTIVE                  CPU Load Timer
00:01:28.388121 0000000004531ca0 00000000 00000000 00000000 Virt         87987201022        91769309162      0 2-ACTIVE                  Heartbeat flatlined
00:01:28.388124 000000000452d9e0 fffffd90 00000000 00000000 VrSy         87987203840        87990000000      0 2-ACTIVE                  MC146818 RTC (CMOS) - Second
00:01:28.388126 000000000452d770 ffffff80 00000270 00000000 VrSy         87987206538        87996018746     64 2-ACTIVE                  APIC Timer 3
00:01:28.388129 000000000452d6f0 ffffff00 00000080 00000000 VrSy         87987209384        88024458776     10 2-ACTIVE                  APIC Timer 2
00:01:28.388132 000000000452d5f0 00000080 00000100 00000000 VrSy         87987211915        88884220141      1 2-ACTIVE                  APIC Timer 0
00:01:28.388134 000000000452d670 00004c60 ffffff80 00000000 VrSy         87987214230        88886895622      1 2-ACTIVE                  APIC Timer 1
00:01:28.388136 00000000045322d0 00000000 ffffb3a0 00000000 VrSy         87987216669       599932015941      0 2-ACTIVE                  ACPI PM Timer
00:01:28.388140 ***
00:01:28.388141 Guest GDT (GCAddr=ffff88019fc0c000 limit=7f):
00:01:28.388147 0008 - 0000ffff 00cf9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 32-bit 
00:01:28.388148 0010 - 0000ffff 00af9b00 - base=00000000 limit=ffffffff dpl=0 CodeER Accessed Present Page 16-bit 
00:01:28.388149 0018 - 0000ffff 00cf9300 - base=00000000 limit=ffffffff dpl=0 DataRW Accessed Present Page 32-bit 
00:01:28.388150 0020 - 0000ffff 00cffb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 32-bit 
00:01:28.388151 0028 - 0000ffff 00cff300 - base=00000000 limit=ffffffff dpl=3 DataRW Accessed Present Page 32-bit 
00:01:28.388152 0030 - 0000ffff 00affb00 - base=00000000 limit=ffffffff dpl=3 CodeER Accessed Present Page 16-bit 
00:01:28.388153 0040 - 48402087 9f008bc0 - base=9fc04840 limit=00002087 dpl=0 TSS32Busy Present 16-bit 
00:01:28.388155 0078 - 00000000 0040f500 - base=00000000 limit=00000000 dpl=3 DataDownRO Accessed Present 32-bit 
00:01:28.388157 ************** End of Guest state at power off ***************
00:01:28.454236 PDMR3PowerOff: after    66 ms, 1 loops: 1 async tasks - virtio-scsi/0
00:01:28.466158 PDMR3PowerOff: 77 985 398 ns run time
00:01:28.466183 Changing the VM state from 'POWERING_OFF' to 'OFF'
00:01:28.467414 Changing the VM state from 'OFF' to 'DESTROYING'
00:01:28.467504 ************************* Statistics *************************
00:01:28.467518 /CPUM/MSR-Totals/Reads                  0 times
00:01:28.467526 /CPUM/MSR-Totals/ReadsRaisingGP         0 times
00:01:28.467532 /CPUM/MSR-Totals/ReadsUnknown           0 times
00:01:28.467543 /CPUM/MSR-Totals/Writes                 5 times
00:01:28.467553 /CPUM/MSR-Totals/WritesRaisingGP        0 times
00:01:28.467566 /CPUM/MSR-Totals/WritesToIgnoredBits        0 times
00:01:28.467579 /CPUM/MSR-Totals/WritesUnknown          0 times
00:01:28.467590 /Devices/8237A/DmaRun                   0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.467603 /Devices/VMMDev/BalloonChunks           0 count
00:01:28.467611 /Devices/VMMDev/FastIrqAckR3           50 count
00:01:28.467617 /Devices/VMMDev/FastIrqAckRZ            0 count
00:01:28.467623 /Devices/VMMDev/HGCM-Guest/BudgetAvailable 535351424 bytes
00:01:28.467629 /Devices/VMMDev/HGCM-Guest/BudgetConfig 535351424 bytes
00:01:28.467634 /Devices/VMMDev/HGCM-Guest/cTotalMessages        0 count
00:01:28.467640 /Devices/VMMDev/HGCM-Guest/cbHeapTotal        0 bytes
00:01:28.467645 /Devices/VMMDev/HGCM-Legacy/BudgetAvailable 535351424 bytes
00:01:28.467651 /Devices/VMMDev/HGCM-Legacy/BudgetConfig 535351424 bytes
00:01:28.467657 /Devices/VMMDev/HGCM-Legacy/cTotalMessages        0 count
00:01:28.467662 /Devices/VMMDev/HGCM-Legacy/cbHeapTotal        0 bytes
00:01:28.467667 /Devices/VMMDev/HGCM-OtherDrv/BudgetAvailable 535351424 bytes
00:01:28.467673 /Devices/VMMDev/HGCM-OtherDrv/BudgetConfig 535351424 bytes
00:01:28.467678 /Devices/VMMDev/HGCM-OtherDrv/cTotalMessages       51 count
00:01:28.467684 /Devices/VMMDev/HGCM-OtherDrv/cbHeapTotal   293313 bytes
00:01:28.467689 /Devices/VMMDev/HGCM-Reserved1/BudgetAvailable 535351424 bytes
00:01:28.467695 /Devices/VMMDev/HGCM-Reserved1/BudgetConfig 535351424 bytes
00:01:28.467700 /Devices/VMMDev/HGCM-Reserved1/cTotalMessages        0 count
00:01:28.467705 /Devices/VMMDev/HGCM-Reserved1/cbHeapTotal        0 bytes
00:01:28.467711 /Devices/VMMDev/HGCM-Root/BudgetAvailable 535351424 bytes
00:01:28.467716 /Devices/VMMDev/HGCM-Root/BudgetConfig 535351424 bytes
00:01:28.467721 /Devices/VMMDev/HGCM-Root/cTotalMessages        0 count
00:01:28.467727 /Devices/VMMDev/HGCM-Root/cbHeapTotal        0 bytes
00:01:28.467733 /Devices/VMMDev/HGCM-System/BudgetAvailable 535351424 bytes
00:01:28.467738 /Devices/VMMDev/HGCM-System/BudgetConfig 535351424 bytes
00:01:28.467743 /Devices/VMMDev/HGCM-System/cTotalMessages        0 count
00:01:28.467748 /Devices/VMMDev/HGCM-System/cbHeapTotal        0 bytes
00:01:28.467754 /Devices/VMMDev/HGCM-User/BudgetAvailable 535351424 bytes
00:01:28.467759 /Devices/VMMDev/HGCM-User/BudgetConfig 535351424 bytes
00:01:28.467764 /Devices/VMMDev/HGCM-User/cTotalMessages        0 count
00:01:28.467770 /Devices/VMMDev/HGCM-User/cbHeapTotal        0 bytes
00:01:28.467775 /Devices/VMMDev/HGCM-VBoxGuest/BudgetAvailable 535351424 bytes
00:01:28.467780 /Devices/VMMDev/HGCM-VBoxGuest/BudgetConfig 535351424 bytes
00:01:28.467786 /Devices/VMMDev/HGCM-VBoxGuest/cTotalMessages        0 count
00:01:28.467793 /Devices/VMMDev/HGCM-VBoxGuest/cbHeapTotal        0 bytes
00:01:28.467800 /Devices/VMMDev/LargeReqBufAllocs        0 count
00:01:28.467805 /Devices/VMMDev/SlowIrqAck              0 count
00:01:28.467811 /Devices/mc146818/Irq                   0 times
00:01:28.467816 /Devices/mc146818/TimerCB               0 times
00:01:28.467822 /Devices/virtio-net#0/Interrupts/Raised      251 times
00:01:28.467827 /Devices/virtio-net#0/Interrupts/Skipped      155 times
00:01:28.467833 /Devices/virtio-net#0/Packets/ReceiveGSO        0 count
00:01:28.467838 /Devices/virtio-net#0/Packets/Transmit      113 count
00:01:28.467843 /Devices/virtio-net#0/Packets/Transmit-Csum       95 count
00:01:28.467848 /Devices/virtio-net#0/Packets/Transmit-Gso        0 count
00:01:28.467854 /Devices/virtio-net#0/ReceiveBytes    41499 bytes
00:01:28.467862 /Devices/virtio-net#0/TransmitBytes    13446 bytes
00:01:28.467867 /Devices/virtio-scsi#0/DescChainsAllocated    11408 count
00:01:28.467873 /Devices/virtio-scsi#0/DescChainsFreed    11408 count
00:01:28.467878 /Devices/virtio-scsi#0/DescChainsSegsIn    76119 count
00:01:28.467883 /Devices/virtio-scsi#0/DescChainsSegsOut    14755 count
00:01:28.467889 /Drivers/IntNet-0/BadFrames             0 count
00:01:28.467894 /Drivers/IntNet-0/Bytes/Received    72560 bytes
00:01:28.467900 /Drivers/IntNet-0/Bytes/Sent        13446 bytes
00:01:28.467905 /Drivers/IntNet-0/Overflows/Recv        0 count
00:01:28.467910 /Drivers/IntNet-0/Overflows/Sent        0 count
00:01:28.467916 /Drivers/IntNet-0/Packets/Lost          0 count
00:01:28.467922 /Drivers/IntNet-0/Packets/Received      449 count
00:01:28.467928 /Drivers/IntNet-0/Packets/Received-Gso        0 count
00:01:28.467933 /Drivers/IntNet-0/Packets/Sent        113 count
00:01:28.467939 /Drivers/IntNet-0/Packets/Sent-Gso        0 count
00:01:28.467944 /Drivers/IntNet-0/Packets/Sent-R0        0 count
00:01:28.467949 /Drivers/IntNet-0/Recv1                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.467955 /Drivers/IntNet-0/Recv2                 0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.467961 /Drivers/IntNet-0/Reserved              0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.467967 /Drivers/IntNet-0/Send1            197764 ticks/call (    22347340 ticks,     113 times, max    954352, min   23410)
00:01:28.467974 /Drivers/IntNet-0/Send2            191550 ticks/call (    21645210 ticks,     113 times, max    806996, min   21594)
00:01:28.467980 /Drivers/IntNet-0/XmitProcessRing        0 count
00:01:28.467986 /Drivers/IntNet-0/XmitWakeup-R0         0 count
00:01:28.467991 /Drivers/IntNet-0/XmitWakeup-R3         0 count
00:01:28.467996 /Drivers/IntNet-0/YieldNok              0 count
00:01:28.468002 /Drivers/IntNet-0/YieldOk               0 count
00:01:28.468008 /EM/CPU0/ExitHashing/Step00-Hits   123489 times
00:01:28.468014 /EM/CPU0/ExitHashing/Step00-NewInserts      190 times
00:01:28.468019 /EM/CPU0/ExitHashing/Step01-Hits     2488 times
00:01:28.468025 /EM/CPU0/ExitHashing/Step01-NewInserts       30 times
00:01:28.468031 /EM/CPU0/ExitHashing/Step02-Hits      111 times
00:01:28.468036 /EM/CPU0/ExitHashing/Step02-NewInserts        5 times
00:01:28.468041 /EM/CPU0/ExitHashing/Step03-NewInserts        2 times
00:01:28.468047 /EM/CPU0/ExitHashing/Step04-NewInserts        1 times
00:01:28.468055 /EM/CPU0/ExitHashing/Used             228 times
00:01:28.468060 /EM/CPU0/ExitOpt/Exec              547903 ticks/call ( 33059947587 ticks,   60339 times, max 221221907, min   11588)
00:01:28.468067 /EM/CPU0/ExitOpt/ExecInstructions  4868977 times
00:01:28.468073 /EM/CPU0/ExitOpt/ExecSavedExit     112234 times
00:01:28.468078 /EM/CPU0/ExitOpt/Probe            2140825 ticks/call (    36394030 ticks,      17 times, max  11013134, min   62480)
00:01:28.468084 /EM/CPU0/ExitOpt/ProbeInstructions    41524 times
00:01:28.468090 /EM/CPU0/ExitOpt/ProbedExecWithMax        7 times
00:01:28.468095 /EM/CPU0/ExitOpt/ProbedNormal          10 times
00:01:28.468102 /EM/CPU0/ExitOpt/ProbedToRing3          0 times
00:01:28.468108 /EM/CPU1/ExitHashing/Step00-Hits    84661 times
00:01:28.468113 /EM/CPU1/ExitHashing/Step00-NewInserts       69 times
00:01:28.468119 /EM/CPU1/ExitHashing/Step01-Hits       90 times
00:01:28.468124 /EM/CPU1/ExitHashing/Step01-NewInserts       53 times
00:01:28.468129 /EM/CPU1/ExitHashing/Step02-NewInserts       20 times
00:01:28.468134 /EM/CPU1/ExitHashing/Step03-NewInserts        5 times
00:01:28.468140 /EM/CPU1/ExitHashing/Step04-NewInserts        2 times
00:01:28.468148 /EM/CPU1/ExitHashing/Used             149 times
00:01:28.468154 /EM/CPU1/ExitOpt/Exec              621424 ticks/call ( 12367591721 ticks,   19902 times, max 110787450, min   11562)
00:01:28.468160 /EM/CPU1/ExitOpt/ExecInstructions  3052861 times
00:01:28.468165 /EM/CPU1/ExitOpt/ExecSavedExit     108186 times
00:01:28.468171 /EM/CPU1/ExitOpt/Probe            7622943 ticks/call (    53360604 ticks,       7 times, max  26588900, min   65680)
00:01:28.468177 /EM/CPU1/ExitOpt/ProbeInstructions     1478 times
00:01:28.468182 /EM/CPU1/ExitOpt/ProbedExecWithMax        3 times
00:01:28.468188 /EM/CPU1/ExitOpt/ProbedNormal           4 times
00:01:28.468193 /EM/CPU1/ExitOpt/ProbedToRing3          0 times
00:01:28.468199 /EM/CPU2/ExitHashing/Step00-Hits   124210 times
00:01:28.468204 /EM/CPU2/ExitHashing/Step00-NewInserts       58 times
00:01:28.468210 /EM/CPU2/ExitHashing/Step01-Hits        2 times
00:01:28.468215 /EM/CPU2/ExitHashing/Step01-NewInserts       46 times
00:01:28.468220 /EM/CPU2/ExitHashing/Step02-NewInserts       11 times
00:01:28.468225 /EM/CPU2/ExitHashing/Step05-NewInserts        1 times
00:01:28.468233 /EM/CPU2/ExitHashing/Used             116 times
00:01:28.468238 /EM/CPU2/ExitOpt/Exec              408985 ticks/call (  6468507184 ticks,   15816 times, max  17659587, min   11890)
00:01:28.468245 /EM/CPU2/ExitOpt/ExecInstructions   271966 times
00:01:28.468254 /EM/CPU2/ExitOpt/ExecSavedExit      14872 times
00:01:28.468268 /EM/CPU2/ExitOpt/Probe            4372116 ticks/call (    30604815 ticks,       7 times, max  20699064, min   63400)
00:01:28.468280 /EM/CPU2/ExitOpt/ProbeInstructions      268 times
00:01:28.468288 /EM/CPU2/ExitOpt/ProbedExecWithMax        1 times
00:01:28.468295 /EM/CPU2/ExitOpt/ProbedNormal           6 times
00:01:28.468305 /EM/CPU2/ExitOpt/ProbedToRing3          0 times
00:01:28.468314 /EM/CPU3/ExitHashing/Step00-Hits   110569 times
00:01:28.468320 /EM/CPU3/ExitHashing/Step00-NewInserts       69 times
00:01:28.468325 /EM/CPU3/ExitHashing/Step01-NewInserts       21 times
00:01:28.468331 /EM/CPU3/ExitHashing/Step02-NewInserts        5 times
00:01:28.468339 /EM/CPU3/ExitHashing/Used              95 times
00:01:28.468344 /EM/CPU3/ExitOpt/Exec              510980 ticks/call ( 13362638147 ticks,   26151 times, max  22465679, min   11574)
00:01:28.468351 /EM/CPU3/ExitOpt/ExecInstructions  1111832 times
00:01:28.468356 /EM/CPU3/ExitOpt/ExecSavedExit      43696 times
00:01:28.468361 /EM/CPU3/ExitOpt/Probe            1436562 ticks/call (    14365628 ticks,      10 times, max   5758834, min   79530)
00:01:28.468367 /EM/CPU3/ExitOpt/ProbeInstructions     3494 times
00:01:28.468373 /EM/CPU3/ExitOpt/ProbedExecWithMax        4 times
00:01:28.468378 /EM/CPU3/ExitOpt/ProbedNormal           6 times
00:01:28.468383 /EM/CPU3/ExitOpt/ProbedToRing3          0 times
00:01:28.468389 /GMM/ChunkTlbHits                       0 times
00:01:28.468395 /GMM/ChunkTlbMisses                     0 times
00:01:28.468406 /GMM/VM/Allocated/cBasePages            0 pages
00:01:28.468412 /GMM/VM/Allocated/cFixedPages           0 pages
00:01:28.468417 /GMM/VM/Allocated/cShadowPages          0 pages
00:01:28.468423 /GMM/VM/Reserved/cBasePages       1572958 pages
00:01:28.468428 /GMM/VM/Reserved/cFixedPages         5140 pages
00:01:28.468434 /GMM/VM/Reserved/cShadowPages           1 pages
00:01:28.468439 /GMM/VM/cBalloonedPages                 0 pages
00:01:28.468444 /GMM/VM/cMaxBalloonedPages              0 pages
00:01:28.468494 /GMM/VM/cPrivatePages                   0 pages
00:01:28.468502 /GMM/VM/cReqActuallyBalloonedPages        0 pages
00:01:28.468508 /GMM/VM/cReqBalloonedPages              0 pages
00:01:28.468513 /GMM/VM/cReqDeflatePages                0 pages
00:01:28.468518 /GMM/VM/cShareableModules               0 count
00:01:28.468524 /GMM/VM/cSharedPages                    0 pages
00:01:28.468529 /GMM/VM/enmPolicy                       1 
00:01:28.468535 /GMM/VM/enmPriority                     2 
00:01:28.468541 /GMM/VM/fBallooningEnabled       false    
00:01:28.468546 /GMM/VM/fMayAllocate             true     
00:01:28.468551 /GMM/VM/fSharedPagingEnabled     false    
00:01:28.468556 /GMM/cAllocatedPages                    0 pages
00:01:28.468562 /GMM/cBalloonedPages                    0 pages
00:01:28.468567 /GMM/cChunks                            0 count
00:01:28.468573 /GMM/cDuplicatePages                    0 pages
00:01:28.468578 /GMM/cFreedChunks                       0 count
00:01:28.468584 /GMM/cLeftBehindSharedPages             0 pages
00:01:28.468589 /GMM/cMaxPages                   4294967295 pages
00:01:28.468595 /GMM/cOverCommittedPages                0 pages
00:01:28.468600 /GMM/cReservedPages               1578099 pages
00:01:28.468606 /GMM/cShareableModules                  0 count
00:01:28.468611 /GMM/cSharedPages                       0 pages
00:01:28.468617 /GMM/idFreeGeneration            4611686018427387775 
00:01:28.468754 /GVMM/EMTs                              4 calls
00:01:28.468761 /GVMM/HostCPUs                          8 calls
00:01:28.468767 /GVMM/HostCpus/0                        0 
00:01:28.468773 /GVMM/HostCpus/0/CurTimerHz             0 Hz
00:01:28.468778 /GVMM/HostCpus/0/DesiredHz              0 Hz
00:01:28.468783 /GVMM/HostCpus/0/PPTChanges             0 times
00:01:28.468789 /GVMM/HostCpus/0/PPTStarts              0 times
00:01:28.468794 /GVMM/HostCpus/0/idxCpuSet              0 
00:01:28.468799 /GVMM/HostCpus/1                        1 
00:01:28.468805 /GVMM/HostCpus/1/CurTimerHz             0 Hz
00:01:28.468810 /GVMM/HostCpus/1/DesiredHz              0 Hz
00:01:28.468815 /GVMM/HostCpus/1/PPTChanges             0 times
00:01:28.468820 /GVMM/HostCpus/1/PPTStarts              0 times
00:01:28.468826 /GVMM/HostCpus/1/idxCpuSet              1 
00:01:28.468831 /GVMM/HostCpus/2                        2 
00:01:28.468839 /GVMM/HostCpus/2/CurTimerHz             0 Hz
00:01:28.468844 /GVMM/HostCpus/2/DesiredHz              0 Hz
00:01:28.468854 /GVMM/HostCpus/2/PPTChanges             0 times
00:01:28.468866 /GVMM/HostCpus/2/PPTStarts              0 times
00:01:28.468872 /GVMM/HostCpus/2/idxCpuSet              2 
00:01:28.468877 /GVMM/HostCpus/3                        3 
00:01:28.468883 /GVMM/HostCpus/3/CurTimerHz             0 Hz
00:01:28.468888 /GVMM/HostCpus/3/DesiredHz              0 Hz
00:01:28.468893 /GVMM/HostCpus/3/PPTChanges             0 times
00:01:28.468898 /GVMM/HostCpus/3/PPTStarts              0 times
00:01:28.468904 /GVMM/HostCpus/3/idxCpuSet              3 
00:01:28.468909 /GVMM/HostCpus/4                        4 
00:01:28.468914 /GVMM/HostCpus/4/CurTimerHz             0 Hz
00:01:28.468920 /GVMM/HostCpus/4/DesiredHz              0 Hz
00:01:28.468925 /GVMM/HostCpus/4/PPTChanges             0 times
00:01:28.468930 /GVMM/HostCpus/4/PPTStarts              0 times
00:01:28.468935 /GVMM/HostCpus/4/idxCpuSet              4 
00:01:28.468941 /GVMM/HostCpus/5                        5 
00:01:28.468946 /GVMM/HostCpus/5/CurTimerHz             0 Hz
00:01:28.468951 /GVMM/HostCpus/5/DesiredHz              0 Hz
00:01:28.468957 /GVMM/HostCpus/5/PPTChanges             0 times
00:01:28.468962 /GVMM/HostCpus/5/PPTStarts              0 times
00:01:28.468967 /GVMM/HostCpus/5/idxCpuSet              5 
00:01:28.468974 /GVMM/HostCpus/6                        6 
00:01:28.468982 /GVMM/HostCpus/6/CurTimerHz             0 Hz
00:01:28.468988 /GVMM/HostCpus/6/DesiredHz              0 Hz
00:01:28.468993 /GVMM/HostCpus/6/PPTChanges             0 times
00:01:28.469000 /GVMM/HostCpus/6/PPTStarts              0 times
00:01:28.469006 /GVMM/HostCpus/6/idxCpuSet              6 
00:01:28.469011 /GVMM/HostCpus/7                        7 
00:01:28.469017 /GVMM/HostCpus/7/CurTimerHz             0 Hz
00:01:28.469022 /GVMM/HostCpus/7/DesiredHz              0 Hz
00:01:28.469027 /GVMM/HostCpus/7/PPTChanges             0 times
00:01:28.469032 /GVMM/HostCpus/7/PPTStarts              0 times
00:01:28.469038 /GVMM/HostCpus/7/idxCpuSet              7 
00:01:28.469043 /GVMM/Sum/HaltBlocking              68447 calls
00:01:28.469049 /GVMM/Sum/HaltCalls                 68445 calls
00:01:28.469054 /GVMM/Sum/HaltNotBlocking               1 calls
00:01:28.469060 /GVMM/Sum/HaltTimeouts              11187 calls
00:01:28.469065 /GVMM/Sum/HaltWakeUps                   0 calls
00:01:28.469071 /GVMM/Sum/PokeCalls                     0 calls
00:01:28.469076 /GVMM/Sum/PokeNotBusy                   0 calls
00:01:28.469081 /GVMM/Sum/PollCalls                    38 calls
00:01:28.469087 /GVMM/Sum/PollHalts                     0 calls
00:01:28.469092 /GVMM/Sum/PollWakeUps                   0 calls
00:01:28.469097 /GVMM/Sum/WakeUpCalls               57775 calls
00:01:28.469103 /GVMM/Sum/WakeUpNotHalted            1418 calls
00:01:28.469108 /GVMM/Sum/WakeUpWakeUps                 0 calls
00:01:28.469114 /GVMM/VM/HaltBlocking               68447 calls
00:01:28.469119 /GVMM/VM/HaltCalls                  68445 calls
00:01:28.469125 /GVMM/VM/HaltNotBlocking                1 calls
00:01:28.469130 /GVMM/VM/HaltTimeouts               11187 calls
00:01:28.469136 /GVMM/VM/HaltWakeUps                    0 calls
00:01:28.469141 /GVMM/VM/PokeCalls                      0 calls
00:01:28.469147 /GVMM/VM/PokeNotBusy                    0 calls
00:01:28.469152 /GVMM/VM/PollCalls                     38 calls
00:01:28.469158 /GVMM/VM/PollHalts                      0 calls
00:01:28.469163 /GVMM/VM/PollWakeUps                    0 calls
00:01:28.469168 /GVMM/VM/WakeUpCalls                57775 calls
00:01:28.469174 /GVMM/VM/WakeUpNotHalted             1418 calls
00:01:28.469179 /GVMM/VM/WakeUpWakeUps                  0 calls
00:01:28.469185 /GVMM/VMs                               1 calls
00:01:28.469190 /HGCM/FailedPageListLocking             0 count
00:01:28.469196 /HGCM/LargeCmdAllocs                    4 count
00:01:28.469201 /HGCM/MsgArrival                   111014 ticks/call (     5328680 ticks,      48 times, max    795589, min   15496)
00:01:28.469208 /HGCM/MsgCompletion                 75439 ticks/call (     3847428 ticks,      51 times, max    383722, min    7230)
00:01:28.469214 /HGCM/MsgTotal                     526675 ticks/call (    25280407 ticks,      48 times, max   1365456, min   51392)
00:01:28.469221 /IEM/CPU0/CodeTlb-Misses                0 count
00:01:28.469226 /IEM/CPU0/CodeTlb-PhysRev        ffffffffffff9c00 
00:01:28.469232 /IEM/CPU0/CodeTlb-Revision       fffff38000000000 
00:01:28.469238 /IEM/CPU0/CodeTlb-SlowReads             0 
00:01:28.469243 /IEM/CPU0/DataTlb-Misses                0 count
00:01:28.469249 /IEM/CPU0/DataTlb-PhysRev        ffffffffffff9c00 
00:01:28.469254 /IEM/CPU0/DataTlb-Revision       fffff38000000000 
00:01:28.469260 /IEM/CPU0/cInstructions           5425492 count
00:01:28.469265 /IEM/CPU0/cLongJumps                    0 bytes
00:01:28.469271 /IEM/CPU0/cPendingCommit                0 bytes
00:01:28.469276 /IEM/CPU0/cPotentialExits          296521 count
00:01:28.469282 /IEM/CPU0/cRetAspectNotImplemented        0 count
00:01:28.469287 /IEM/CPU0/cRetErrStatuses               0 count
00:01:28.469292 /IEM/CPU0/cRetInfStatuses               0 count
00:01:28.469297 /IEM/CPU0/cRetInstrNotImplemented        0 count
00:01:28.469303 /IEM/CPU0/cbWritten               5314428 bytes
00:01:28.469308 /IEM/CPU1/CodeTlb-Misses                0 count
00:01:28.469314 /IEM/CPU1/CodeTlb-PhysRev        ffffffffffff9c00 
00:01:28.469319 /IEM/CPU1/CodeTlb-Revision       fffff38000000000 
00:01:28.469325 /IEM/CPU1/CodeTlb-SlowReads             0 
00:01:28.469331 /IEM/CPU1/DataTlb-Misses                0 count
00:01:28.469337 /IEM/CPU1/DataTlb-PhysRev        ffffffffffff9c00 
00:01:28.469343 /IEM/CPU1/DataTlb-Revision       fffff38000000000 
00:01:28.469348 /IEM/CPU1/cInstructions           3117196 count
00:01:28.469354 /IEM/CPU1/cLongJumps                    0 bytes
00:01:28.469359 /IEM/CPU1/cPendingCommit                0 bytes
00:01:28.469364 /IEM/CPU1/cPotentialExits          210886 count
00:01:28.469370 /IEM/CPU1/cRetAspectNotImplemented        0 count
00:01:28.469375 /IEM/CPU1/cRetErrStatuses               0 count
00:01:28.469381 /IEM/CPU1/cRetInfStatuses               0 count
00:01:28.469386 /IEM/CPU1/cRetInstrNotImplemented        0 count
00:01:28.469391 /IEM/CPU1/cbWritten               3529185 bytes
00:01:28.469397 /IEM/CPU2/CodeTlb-Misses                0 count
00:01:28.469402 /IEM/CPU2/CodeTlb-PhysRev        ffffffffffff9c00 
00:01:28.469408 /IEM/CPU2/CodeTlb-Revision       fffff38000000000 
00:01:28.469413 /IEM/CPU2/CodeTlb-SlowReads             0 
00:01:28.469419 /IEM/CPU2/DataTlb-Misses                0 count
00:01:28.469424 /IEM/CPU2/DataTlb-PhysRev        ffffffffffff9c00 
00:01:28.469430 /IEM/CPU2/DataTlb-Revision       fffff38000000000 
00:01:28.469435 /IEM/CPU2/cInstructions            335077 count
00:01:28.469441 /IEM/CPU2/cLongJumps                    0 bytes
00:01:28.469446 /IEM/CPU2/cPendingCommit                0 bytes
00:01:28.469452 /IEM/CPU2/cPotentialExits          109357 count
00:01:28.469457 /IEM/CPU2/cRetAspectNotImplemented        0 count
00:01:28.469462 /IEM/CPU2/cRetErrStatuses               0 count
00:01:28.469468 /IEM/CPU2/cRetInfStatuses               0 count
00:01:28.469473 /IEM/CPU2/cRetInstrNotImplemented        0 count
00:01:28.469478 /IEM/CPU2/cbWritten                482316 bytes
00:01:28.469484 /IEM/CPU3/CodeTlb-Misses                0 count
00:01:28.469489 /IEM/CPU3/CodeTlb-PhysRev        ffffffffffff9c00 
00:01:28.469495 /IEM/CPU3/CodeTlb-Revision       fffff38000000000 
00:01:28.469500 /IEM/CPU3/CodeTlb-SlowReads             0 
00:01:28.469506 /IEM/CPU3/DataTlb-Misses                0 count
00:01:28.469511 /IEM/CPU3/DataTlb-PhysRev        ffffffffffff9c00 
00:01:28.469517 /IEM/CPU3/DataTlb-Revision       fffff38000000000 
00:01:28.469522 /IEM/CPU3/cInstructions           1197112 count
00:01:28.469528 /IEM/CPU3/cLongJumps                    0 bytes
00:01:28.469533 /IEM/CPU3/cPendingCommit                0 bytes
00:01:28.469538 /IEM/CPU3/cPotentialExits          177886 count
00:01:28.469544 /IEM/CPU3/cRetAspectNotImplemented        0 count
00:01:28.469549 /IEM/CPU3/cRetErrStatuses               0 count
00:01:28.469554 /IEM/CPU3/cRetInfStatuses               0 count
00:01:28.469560 /IEM/CPU3/cRetInstrNotImplemented        0 count
00:01:28.469565 /IEM/CPU3/cbWritten               1361024 bytes
00:01:28.469571 /IOM/MmioMappingsStale                  0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.469577 /MM/HyperHeap/cbFree              8534208 bytes
00:01:28.469583 /MM/HyperHeap/cbHeap              8650432 bytes
00:01:28.469588 /NEM/CPU0/BreakOnCancel                 0 times
00:01:28.469594 /NEM/CPU0/BreakOnFFPost              2461 times
00:01:28.469600 /NEM/CPU0/BreakOnFFPre                217 times
00:01:28.469605 /NEM/CPU0/BreakOnStatus             13917 times
00:01:28.469611 /NEM/CPU0/CancelAlertedEMT              0 times
00:01:28.469616 /NEM/CPU0/CancelChangedState            0 times
00:01:28.469621 /NEM/CPU0/ExitCpuId                  2799 times
00:01:28.469627 /NEM/CPU0/ExitException                 0 times
00:01:28.469632 /NEM/CPU0/ExitExceptionBp               0 times
00:01:28.469638 /NEM/CPU0/ExitExceptionDb               0 times
00:01:28.469643 /NEM/CPU0/ExitExceptionGp               0 times
00:01:28.469648 /NEM/CPU0/ExitExceptionGpMesa           0 times
00:01:28.469653 /NEM/CPU0/ExitExceptionUd               0 times
00:01:28.469659 /NEM/CPU0/ExitExceptionUdHandled        0 times
00:01:28.469664 /NEM/CPU0/ExitHalt                  13917 times
00:01:28.469671 /NEM/CPU0/ExitInterruptWindow        5616 times
00:01:28.469677 /NEM/CPU0/ExitMemIntercept              0 times
00:01:28.469682 /NEM/CPU0/ExitMemUnmapped           72956 times
00:01:28.469687 /NEM/CPU0/ExitMsr                       2 times
00:01:28.469694 /NEM/CPU0/ExitPortIo                50575 times
00:01:28.469699 /NEM/CPU0/ExitUnrecoverable             0 times
00:01:28.469704 /NEM/CPU0/GetMsgTimeout                 0 times
00:01:28.469710 /NEM/CPU0/ImportOnDemand            44164 times
00:01:28.469715 /NEM/CPU0/ImportOnReturn            14908 times
00:01:28.469721 /NEM/CPU0/ImportOnReturnSkipped      1687 times
00:01:28.469726 /NEM/CPU0/QueryCpuTick                  2 times
00:01:28.469731 /NEM/CPU0/StopCpuPending                0 times
00:01:28.469737 /NEM/CPU0/StopCpuPendingAlerts          0 times
00:01:28.469742 /NEM/CPU0/StopCpuPendingOdd             0 times
00:01:28.469747 /NEM/CPU0/StopCpuSuccess                0 times
00:01:28.469753 /NEM/CPU1/BreakOnCancel                 0 times
00:01:28.469758 /NEM/CPU1/BreakOnFFPost              2974 times
00:01:28.469764 /NEM/CPU1/BreakOnFFPre                301 times
00:01:28.469769 /NEM/CPU1/BreakOnStatus             14667 times
00:01:28.469774 /NEM/CPU1/CancelAlertedEMT              0 times
00:01:28.469780 /NEM/CPU1/CancelChangedState            0 times
00:01:28.469785 /NEM/CPU1/ExitCpuId                   329 times
00:01:28.469791 /NEM/CPU1/ExitException                 7 times
00:01:28.469796 /NEM/CPU1/ExitExceptionBp               7 times
00:01:28.469801 /NEM/CPU1/ExitExceptionDb               0 times
00:01:28.469807 /NEM/CPU1/ExitExceptionGp               0 times
00:01:28.469815 /NEM/CPU1/ExitExceptionGpMesa           0 times
00:01:28.469822 /NEM/CPU1/ExitExceptionUd               0 times
00:01:28.469829 /NEM/CPU1/ExitExceptionUdHandled        0 times
00:01:28.469836 /NEM/CPU1/ExitHalt                  14667 times
00:01:28.469844 /NEM/CPU1/ExitInterruptWindow        7318 times
00:01:28.469849 /NEM/CPU1/ExitMemIntercept              0 times
00:01:28.469855 /NEM/CPU1/ExitMemUnmapped           81528 times
00:01:28.469860 /NEM/CPU1/ExitMsr                       1 times
00:01:28.469866 /NEM/CPU1/ExitPortIo                 3043 times
00:01:28.469873 /NEM/CPU1/ExitUnrecoverable             0 times
00:01:28.469906 /NEM/CPU1/GetMsgTimeout                 0 times
00:01:28.469916 /NEM/CPU1/ImportOnDemand             2427 times
00:01:28.469922 /NEM/CPU1/ImportOnReturn            15580 times
00:01:28.469928 /NEM/CPU1/ImportOnReturnSkipped      2362 times
00:01:28.469933 /NEM/CPU1/QueryCpuTick                  2 times
00:01:28.469939 /NEM/CPU1/StopCpuPending                0 times
00:01:28.469944 /NEM/CPU1/StopCpuPendingAlerts          0 times
00:01:28.469963 /NEM/CPU1/StopCpuPendingOdd             0 times
00:01:28.469968 /NEM/CPU1/StopCpuSuccess                0 times
00:01:28.469974 /NEM/CPU2/BreakOnCancel                 0 times
00:01:28.469980 /NEM/CPU2/BreakOnFFPost              2725 times
00:01:28.469985 /NEM/CPU2/BreakOnFFPre                328 times
00:01:28.469997 /NEM/CPU2/BreakOnStatus             17260 times
00:01:28.470018 /NEM/CPU2/CancelAlertedEMT              0 times
00:01:28.470024 /NEM/CPU2/CancelChangedState            0 times
00:01:28.470030 /NEM/CPU2/ExitCpuId                   302 times
00:01:28.470035 /NEM/CPU2/ExitException                 5 times
00:01:28.470041 /NEM/CPU2/ExitExceptionBp               5 times
00:01:28.470047 /NEM/CPU2/ExitExceptionDb               0 times
00:01:28.470070 /NEM/CPU2/ExitExceptionGp               0 times
00:01:28.470097 /NEM/CPU2/ExitExceptionGpMesa           0 times
00:01:28.470103 /NEM/CPU2/ExitExceptionUd               0 times
00:01:28.470109 /NEM/CPU2/ExitExceptionUdHandled        0 times
00:01:28.470114 /NEM/CPU2/ExitHalt                  17260 times
00:01:28.470120 /NEM/CPU2/ExitInterruptWindow        7261 times
00:01:28.470125 /NEM/CPU2/ExitMemIntercept              0 times
00:01:28.470131 /NEM/CPU2/ExitMemUnmapped           78663 times
00:01:28.470138 /NEM/CPU2/ExitMsr                       1 times
00:01:28.470160 /NEM/CPU2/ExitPortIo                45363 times
00:01:28.470181 /NEM/CPU2/ExitUnrecoverable             0 times
00:01:28.470186 /NEM/CPU2/GetMsgTimeout                 0 times
00:01:28.470191 /NEM/CPU2/ImportOnDemand              617 times
00:01:28.470197 /NEM/CPU2/ImportOnReturn            18086 times
00:01:28.470202 /NEM/CPU2/ImportOnReturnSkipped      2227 times
00:01:28.470208 /NEM/CPU2/QueryCpuTick                  2 times
00:01:28.470213 /NEM/CPU2/StopCpuPending                0 times
00:01:28.470219 /NEM/CPU2/StopCpuPendingAlerts          0 times
00:01:28.470224 /NEM/CPU2/StopCpuPendingOdd             0 times
00:01:28.470229 /NEM/CPU2/StopCpuSuccess                0 times
00:01:28.470235 /NEM/CPU3/BreakOnCancel                 0 times
00:01:28.470240 /NEM/CPU3/BreakOnFFPost              7247 times
00:01:28.470246 /NEM/CPU3/BreakOnFFPre              12041 times
00:01:28.470264 /NEM/CPU3/BreakOnStatus             18080 times
00:01:28.470285 /NEM/CPU3/CancelAlertedEMT              0 times
00:01:28.470291 /NEM/CPU3/CancelChangedState            0 times
00:01:28.470296 /NEM/CPU3/ExitCpuId                   245 times
00:01:28.470301 /NEM/CPU3/ExitException                 5 times
00:01:28.470307 /NEM/CPU3/ExitExceptionBp               5 times
00:01:28.470312 /NEM/CPU3/ExitExceptionDb               0 times
00:01:28.470318 /NEM/CPU3/ExitExceptionGp               0 times
00:01:28.470323 /NEM/CPU3/ExitExceptionGpMesa           0 times
00:01:28.470328 /NEM/CPU3/ExitExceptionUd               0 times
00:01:28.470334 /NEM/CPU3/ExitExceptionUdHandled        0 times
00:01:28.470339 /NEM/CPU3/ExitHalt                  18080 times
00:01:28.470345 /NEM/CPU3/ExitInterruptWindow       11420 times
00:01:28.470350 /NEM/CPU3/ExitMemIntercept              0 times
00:01:28.470356 /NEM/CPU3/ExitMemUnmapped          107411 times
00:01:28.470361 /NEM/CPU3/ExitMsr                       1 times
00:01:28.470366 /NEM/CPU3/ExitPortIo                 3008 times
00:01:28.470372 /NEM/CPU3/ExitUnrecoverable             0 times
00:01:28.470377 /NEM/CPU3/GetMsgTimeout                 0 times
00:01:28.470383 /NEM/CPU3/ImportOnDemand             3828 times
00:01:28.470388 /NEM/CPU3/ImportOnReturn            32669 times
00:01:28.470393 /NEM/CPU3/ImportOnReturnSkipped      4699 times
00:01:28.470399 /NEM/CPU3/QueryCpuTick                  2 times
00:01:28.470404 /NEM/CPU3/StopCpuPending                0 times
00:01:28.470410 /NEM/CPU3/StopCpuPendingAlerts          0 times
00:01:28.470415 /NEM/CPU3/StopCpuPendingOdd             0 times
00:01:28.470420 /NEM/CPU3/StopCpuSuccess                0 times
00:01:28.470426 /NEM/PagesCurrentlyMapped               0 pages
00:01:28.470431 /NEM/PagesMapCalls                     14 pages
00:01:28.470437 /NEM/PagesMapFails                      0 pages
00:01:28.470442 /NEM/PagesMapGpaRange              519082 ticks/call (    21282366 ticks,      41 times, max   9022370, min   30584)
00:01:28.470449 /NEM/PagesMapGpaRangePage               0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.470455 /NEM/PagesUnmapCalls                   14 pages
00:01:28.470461 /NEM/PagesUnmapFails                    0 pages
00:01:28.470467 /NEM/PagesUnmapGpaRange            661185 ticks/call (    18513206 ticks,      28 times, max   2033626, min   32612)
00:01:28.470473 /NEM/PagesUnmapGpaRangePage         41145 ticks/call (      576032 ticks,      14 times, max    114214, min   27438)
00:01:28.470484 /NEM/R0Stats/cPagesAvailable            0 pages
00:01:28.470490 /NEM/R0Stats/cPagesInUse                0 pages
00:01:28.470495 /PDM/BlkCache/cbCached                  0 bytes
00:01:28.470501 /PDM/BlkCache/cbCachedFru               0 bytes
00:01:28.470506 /PDM/BlkCache/cbCachedMruIn             0 bytes
00:01:28.470512 /PDM/BlkCache/cbCachedMruOut            0 bytes
00:01:28.470517 /PDM/BlkCache/cbMax               5242880 bytes
00:01:28.470524 /PDM/CritSects/8237A#0Auto/ContentionR3        0 times
00:01:28.470531 /PDM/CritSects/8237A#0Auto/ContentionRZLock        0 times
00:01:28.470536 /PDM/CritSects/8237A#0Auto/ContentionRZUnlock        0 times
00:01:28.470541 /PDM/CritSects/GIMDev#0Auto/ContentionR3        0 times
00:01:28.470547 /PDM/CritSects/GIMDev#0Auto/ContentionRZLock        0 times
00:01:28.470552 /PDM/CritSects/GIMDev#0Auto/ContentionRZUnlock        0 times
00:01:28.470558 /PDM/CritSects/IntNetXmit_0/ContentionR3        0 times
00:01:28.470563 /PDM/CritSects/IntNetXmit_0/ContentionRZLock        0 times
00:01:28.470568 /PDM/CritSects/IntNetXmit_0/ContentionRZUnlock        0 times
00:01:28.470573 /PDM/CritSects/MM-HYPER/ContentionR3        0 times
00:01:28.470579 /PDM/CritSects/MM-HYPER/ContentionRZLock        0 times
00:01:28.470584 /PDM/CritSects/MM-HYPER/ContentionRZUnlock        0 times
00:01:28.470589 /PDM/CritSects/NOP/ContentionR3         0 times
00:01:28.470594 /PDM/CritSects/NOP/ContentionRZLock        0 times
00:01:28.470600 /PDM/CritSects/NOP/ContentionRZUnlock        0 times
00:01:28.470605 /PDM/CritSects/PDM/ContentionR3        17 times
00:01:28.470610 /PDM/CritSects/PDM/ContentionRZLock        0 times
00:01:28.470616 /PDM/CritSects/PDM/ContentionRZUnlock        0 times
00:01:28.470621 /PDM/CritSects/PGM/ContentionR3    794339 times
00:01:28.470626 /PDM/CritSects/PGM/ContentionRZLock        0 times
00:01:28.470632 /PDM/CritSects/PGM/ContentionRZUnlock        0 times
00:01:28.470637 /PDM/CritSects/TM Timer Lock/ContentionR3     1766 times
00:01:28.470642 /PDM/CritSects/TM Timer Lock/ContentionRZLock        0 times
00:01:28.470647 /PDM/CritSects/TM Timer Lock/ContentionRZUnlock        0 times
00:01:28.470652 /PDM/CritSects/TM VirtualSync Lock/ContentionR3     2083 times
00:01:28.470658 /PDM/CritSects/TM VirtualSync Lock/ContentionRZLock        0 times
00:01:28.470663 /PDM/CritSects/TM VirtualSync Lock/ContentionRZUnlock        0 times
00:01:28.470668 /PDM/CritSects/VMMDev#0/ContentionR3        0 times
00:01:28.470674 /PDM/CritSects/VMMDev#0/ContentionRZLock        0 times
00:01:28.470679 /PDM/CritSects/VMMDev#0/ContentionRZUnlock        0 times
00:01:28.470684 /PDM/CritSects/VNet0/ContentionR3        0 times
00:01:28.470689 /PDM/CritSects/VNet0/ContentionRZLock        0 times
00:01:28.470695 /PDM/CritSects/VNet0/ContentionRZUnlock        0 times
00:01:28.470700 /PDM/CritSects/acpi#0/ContentionR3        0 times
00:01:28.470705 /PDM/CritSects/acpi#0/ContentionRZLock        0 times
00:01:28.470710 /PDM/CritSects/acpi#0/ContentionRZUnlock        0 times
00:01:28.470716 /PDM/CritSects/fastpipe#0Auto/ContentionR3        3 times
00:01:28.470721 /PDM/CritSects/fastpipe#0Auto/ContentionRZLock        0 times
00:01:28.470726 /PDM/CritSects/fastpipe#0Auto/ContentionRZUnlock        0 times
00:01:28.470731 /PDM/CritSects/mc146818#0Auto/ContentionR3        0 times
00:01:28.470737 /PDM/CritSects/mc146818#0Auto/ContentionRZLock        0 times
00:01:28.470742 /PDM/CritSects/mc146818#0Auto/ContentionRZUnlock        0 times
00:01:28.470747 /PDM/CritSects/pcarch#0Auto/ContentionR3        0 times
00:01:28.470752 /PDM/CritSects/pcarch#0Auto/ContentionRZLock        0 times
00:01:28.470758 /PDM/CritSects/pcarch#0Auto/ContentionRZUnlock        0 times
00:01:28.470763 /PDM/CritSects/pcbios#0Auto/ContentionR3        0 times
00:01:28.470768 /PDM/CritSects/pcbios#0Auto/ContentionRZLock        0 times
00:01:28.470774 /PDM/CritSects/pcbios#0Auto/ContentionRZUnlock        0 times
00:01:28.470779 /PDM/CritSects/pckbd#0Auto/ContentionR3        1 times
00:01:28.470787 /PDM/CritSects/pckbd#0Auto/ContentionRZLock        0 times
00:01:28.470793 /PDM/CritSects/pckbd#0Auto/ContentionRZUnlock        0 times
00:01:28.470798 /PDM/CritSects/pit#0/ContentionR3        0 times
00:01:28.470803 /PDM/CritSects/pit#0/ContentionRZLock        0 times
00:01:28.470808 /PDM/CritSects/pit#0/ContentionRZUnlock        0 times
00:01:28.470814 /PDM/CritSects/virtio-scsi#0Auto/ContentionR3       56 times
00:01:28.470819 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZLock        0 times
00:01:28.470825 /PDM/CritSects/virtio-scsi#0Auto/ContentionRZUnlock        0 times
00:01:28.470831 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterExcl        0 times
00:01:28.470850 /PDM/CritSectsRw/IOM Lock/ContentionR3EnterShared        0 times
00:01:28.470855 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterExcl        0 times
00:01:28.470860 /PDM/CritSectsRw/IOM Lock/ContentionRZEnterShared        0 times
00:01:28.470865 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveExcl        0 times
00:01:28.470870 /PDM/CritSectsRw/IOM Lock/ContentionRZLeaveShared        0 times
00:01:28.470875 /PDM/CritSectsRw/IOM Lock/R3EnterExcl      114 times
00:01:28.470881 /PDM/CritSectsRw/IOM Lock/R3EnterShared   330368 times
00:01:28.470886 /PDM/CritSectsRw/IOM Lock/RZEnterExcl        0 times
00:01:28.470891 /PDM/CritSectsRw/IOM Lock/RZEnterShared        0 times
00:01:28.470897 /PDM/Queue/DevHlp/AllocFailures         0 times
00:01:28.470902 /PDM/Queue/DevHlp/Flush                 0 calls
00:01:28.470907 /PDM/Queue/DevHlp/FlushLeftovers        0 times
00:01:28.470912 /PDM/Queue/DevHlp/Insert                0 calls
00:01:28.470918 /PDM/Queue/DevHlp/cItems                8 count
00:01:28.470923 /PDM/Queue/DevHlp/cbItem               64 bytes
00:01:28.470929 /PDM/Queue/Keyboard/AllocFailures        0 times
00:01:28.470934 /PDM/Queue/Keyboard/Flush               0 calls
00:01:28.470939 /PDM/Queue/Keyboard/FlushLeftovers        0 times
00:01:28.470944 /PDM/Queue/Keyboard/Insert              0 calls
00:01:28.470949 /PDM/Queue/Keyboard/cItems             64 count
00:01:28.470954 /PDM/Queue/Keyboard/cbItem             32 bytes
00:01:28.470960 /PDM/Queue/Mouse/AllocFailures          0 times
00:01:28.470965 /PDM/Queue/Mouse/Flush                  0 calls
00:01:28.470970 /PDM/Queue/Mouse/FlushLeftovers         0 times
00:01:28.470975 /PDM/Queue/Mouse/Insert                 0 calls
00:01:28.470980 /PDM/Queue/Mouse/cItems               128 count
00:01:28.470986 /PDM/Queue/Mouse/cbItem                48 bytes
00:01:28.470991 /PDM/Queue/SCSI-Eject/AllocFailures        0 times
00:01:28.470997 /PDM/Queue/SCSI-Eject/Flush             0 calls
00:01:28.471002 /PDM/Queue/SCSI-Eject/FlushLeftovers        0 times
00:01:28.471007 /PDM/Queue/SCSI-Eject/Insert            0 calls
00:01:28.471012 /PDM/Queue/SCSI-Eject/cItems            1 count
00:01:28.471017 /PDM/Queue/SCSI-Eject/cbItem           40 bytes
00:01:28.471022 /PDM/Queue/SCSI-Eject_1/AllocFailures        0 times
00:01:28.471028 /PDM/Queue/SCSI-Eject_1/Flush           0 calls
00:01:28.471033 /PDM/Queue/SCSI-Eject_1/FlushLeftovers        0 times
00:01:28.471038 /PDM/Queue/SCSI-Eject_1/Insert          0 calls
00:01:28.471043 /PDM/Queue/SCSI-Eject_1/cItems          1 count
00:01:28.471048 /PDM/Queue/SCSI-Eject_1/cbItem         40 bytes
00:01:28.471053 /PDM/Queue/SCSI-Eject_2/AllocFailures        0 times
00:01:28.471058 /PDM/Queue/SCSI-Eject_2/Flush           0 calls
00:01:28.471064 /PDM/Queue/SCSI-Eject_2/FlushLeftovers        0 times
00:01:28.471069 /PDM/Queue/SCSI-Eject_2/Insert          0 calls
00:01:28.471074 /PDM/Queue/SCSI-Eject_2/cItems          1 count
00:01:28.471079 /PDM/Queue/SCSI-Eject_2/cbItem         40 bytes
00:01:28.471084 /PGM/CPU0/cA20Changes                   2 times
00:01:28.471090 /PGM/CPU0/cGuestModeChanges           182 times
00:01:28.471095 /PGM/CPU1/cA20Changes                   0 times
00:01:28.471100 /PGM/CPU1/cGuestModeChanges             2 times
00:01:28.471106 /PGM/CPU2/cA20Changes                   0 times
00:01:28.471111 /PGM/CPU2/cGuestModeChanges             2 times
00:01:28.471116 /PGM/CPU3/cA20Changes                   0 times
00:01:28.471121 /PGM/CPU3/cGuestModeChanges             2 times
00:01:28.471127 /PGM/ChunkR3Map/Mapped                  0 count
00:01:28.471132 /PGM/ChunkR3Map/Unmapped                0 count
00:01:28.471137 /PGM/ChunkR3Map/c                       0 count
00:01:28.471143 /PGM/ChunkR3Map/cMax             4294967295 count
00:01:28.471148 /PGM/LargePage/Recheck                  0 times
00:01:28.471154 /PGM/LargePage/Refused                  0 times
00:01:28.471160 /PGM/LargePage/Reused                   0 times
00:01:28.471166 /PGM/Mmio2QueryAndResetDirtyBitmap        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471185 /PGM/Page/cAllPages               1577974 count
00:01:28.471195 /PGM/Page/cBalloonedPages               0 count
00:01:28.471201 /PGM/Page/cHandyPages                   0 count
00:01:28.471207 /PGM/Page/cLargePages                   0 count
00:01:28.471213 /PGM/Page/cLargePagesDisabled           0 count
00:01:28.471218 /PGM/Page/cMonitoredPages               0 count
00:01:28.471223 /PGM/Page/cPrivatePages           1577970 count
00:01:28.471229 /PGM/Page/cPureMmioPages                4 count
00:01:28.471234 /PGM/Page/cReadLockedPages              0 count
00:01:28.471240 /PGM/Page/cReusedSharedPages            0 count
00:01:28.471245 /PGM/Page/cSharedPages                  0 count
00:01:28.471250 /PGM/Page/cWriteLockedPages             0 count
00:01:28.471256 /PGM/Page/cWrittenToPages               0 count
00:01:28.471261 /PGM/Page/cZeroPages                    0 count
00:01:28.471267 /PGM/Pool/Grow                          0 ticks (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471273 /PGM/ShMod/Check                        0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471280 /PGM/cRelocations                       0 times
00:01:28.471285 /PROF/CPU0/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471292 /PROF/CPU0/EM/ForcedActions         18408 times
00:01:28.471297 /PROF/CPU0/EM/Halted                13917 times
00:01:28.471303 /PROF/CPU0/EM/NEMExec             5658722 ticks/call ( 93906493810 ticks,   16595 times, max 4613189994, min  128536)
00:01:28.471310 /PROF/CPU0/EM/NEMExecuteCalled      14368 times
00:01:28.471315 /PROF/CPU0/EM/RAWTotal                  0 times
00:01:28.471321 /PROF/CPU0/EM/REMTotal                  1 times
00:01:28.471326 /PROF/CPU0/EM/RecordedExits        145865 times
00:01:28.471332 /PROF/CPU0/EM/Total              317035641341 ticks/call (317035641341 ticks,       1 times, max 317035641341, min 317035641341)
00:01:28.471339 /PROF/CPU0/VM/Halt/Block          5072774 ns/call ( 61426220767 ticks,   12109 times, max 499979459, min       1)
00:01:28.471346 /PROF/CPU0/VM/Halt/BlockInsomnia  5031899 ns/call ( 60926241308 ticks,   12108 times, max 256404320, min       1)
00:01:28.471352 /PROF/CPU0/VM/Halt/BlockOnTime   499979459 ns/call (   499979459 ticks,       1 times, max 499979459, min 499979459)
00:01:28.471359 /PROF/CPU0/VM/Halt/BlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471365 /PROF/CPU0/VM/Halt/R0HaltBlock          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471371 /PROF/CPU0/VM/Halt/R0HaltBlockInsomnia        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471377 /PROF/CPU0/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471383 /PROF/CPU0/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471389 /PROF/CPU0/VM/Halt/R0HaltExec           0 times
00:01:28.471394 /PROF/CPU0/VM/Halt/R0HaltExec/FromBlock        0 times
00:01:28.471399 /PROF/CPU0/VM/Halt/R0HaltExec/FromSpin        0 times
00:01:28.471405 /PROF/CPU0/VM/Halt/R0HaltHistoryCounter        0 times
00:01:28.471410 /PROF/CPU0/VM/Halt/R0HaltHistorySucceeded        0 times
00:01:28.471415 /PROF/CPU0/VM/Halt/R0HaltHistoryToRing3        0 times
00:01:28.471420 /PROF/CPU0/VM/Halt/R0HaltToR3           0 times
00:01:28.471426 /PROF/CPU0/VM/Halt/R0HaltToR3/FromSpin        0 times
00:01:28.471431 /PROF/CPU0/VM/Halt/R0HaltToR3/Other        0 times
00:01:28.471436 /PROF/CPU0/VM/Halt/R0HaltToR3/PendingFF        0 times
00:01:28.471481 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitNoInt        0 times
00:01:28.471489 /PROF/CPU0/VM/Halt/R0HaltToR3/PostWaitPendingFF        0 times
00:01:28.471494 /PROF/CPU0/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:01:28.471499 /PROF/CPU0/VM/Halt/Timers             307 ns/call (     7456144 ticks,   24238 times, max     24412, min       1)
00:01:28.471505 /PROF/CPU0/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471511 /PROF/CPU1/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471517 /PROF/CPU1/EM/ForcedActions         19535 times
00:01:28.471523 /PROF/CPU1/EM/Halted                14670 times
00:01:28.471528 /PROF/CPU1/EM/NEMExec             4593118 ticks/call ( 82409730525 ticks,   17942 times, max 651076578, min  124022)
00:01:28.471535 /PROF/CPU1/EM/NEMExecuteCalled      15064 times
00:01:28.471540 /PROF/CPU1/EM/RAWTotal                  0 times
00:01:28.471545 /PROF/CPU1/EM/REMTotal                  0 times
00:01:28.471551 /PROF/CPU1/EM/RecordedExits        106893 times
00:01:28.471556 /PROF/CPU1/EM/Total              317035058241 ticks/call (317035058241 ticks,       1 times, max 317035058241, min 317035058241)
00:01:28.471563 /PROF/CPU1/VM/Halt/Block          4913798 ns/call ( 64375673872 ticks,   13101 times, max 500597386, min       2)
00:01:28.471569 /PROF/CPU1/VM/Halt/BlockInsomnia  4459661 ns/call ( 58372502936 ticks,   13089 times, max 499938974, min       2)
00:01:28.471575 /PROF/CPU1/VM/Halt/BlockOnTime   499996543 ns/call (   999993087 ticks,       2 times, max 500013045, min 499980042)
00:01:28.471581 /PROF/CPU1/VM/Halt/BlockOverslept   318294 ns/call (     3182947 ticks,      10 times, max    597513, min  168570)
00:01:28.471587 /PROF/CPU1/VM/Halt/R0HaltBlock          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471593 /PROF/CPU1/VM/Halt/R0HaltBlockInsomnia        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471599 /PROF/CPU1/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471604 /PROF/CPU1/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471610 /PROF/CPU1/VM/Halt/R0HaltExec           0 times
00:01:28.471615 /PROF/CPU1/VM/Halt/R0HaltExec/FromBlock        0 times
00:01:28.471620 /PROF/CPU1/VM/Halt/R0HaltExec/FromSpin        0 times
00:01:28.471626 /PROF/CPU1/VM/Halt/R0HaltHistoryCounter        0 times
00:01:28.471631 /PROF/CPU1/VM/Halt/R0HaltHistorySucceeded        0 times
00:01:28.471636 /PROF/CPU1/VM/Halt/R0HaltHistoryToRing3        0 times
00:01:28.471641 /PROF/CPU1/VM/Halt/R0HaltToR3           0 times
00:01:28.471646 /PROF/CPU1/VM/Halt/R0HaltToR3/FromSpin        0 times
00:01:28.471651 /PROF/CPU1/VM/Halt/R0HaltToR3/Other        0 times
00:01:28.471657 /PROF/CPU1/VM/Halt/R0HaltToR3/PendingFF        0 times
00:01:28.471662 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitNoInt        0 times
00:01:28.471667 /PROF/CPU1/VM/Halt/R0HaltToR3/PostWaitPendingFF        0 times
00:01:28.471672 /PROF/CPU1/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:01:28.471677 /PROF/CPU1/VM/Halt/Timers             333 ns/call (     8739180 ticks,   26191 times, max     27906, min       1)
00:01:28.471683 /PROF/CPU1/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471689 /PROF/CPU2/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471695 /PROF/CPU2/EM/ForcedActions         22175 times
00:01:28.471700 /PROF/CPU2/EM/Halted                17263 times
00:01:28.471705 /PROF/CPU2/EM/NEMExec             3544615 ticks/call ( 72001778866 ticks,   20313 times, max 277176643, min  129492)
00:01:28.471712 /PROF/CPU2/EM/NEMExecuteCalled      17574 times
00:01:28.471717 /PROF/CPU2/EM/RAWTotal                  0 times
00:01:28.471723 /PROF/CPU2/EM/REMTotal                  0 times
00:01:28.471729 /PROF/CPU2/EM/RecordedExits        148855 times
00:01:28.471734 /PROF/CPU2/EM/Total              317035421535 ticks/call (317035421535 ticks,       1 times, max 317035421535, min 317035421535)
00:01:28.471741 /PROF/CPU2/VM/Halt/Block          4351337 ns/call ( 67115035518 ticks,   15424 times, max 500261455, min       1)
00:01:28.471747 /PROF/CPU2/VM/Halt/BlockInsomnia  4126222 ns/call ( 63613970210 ticks,   15417 times, max 499932107, min       1)
00:01:28.471753 /PROF/CPU2/VM/Halt/BlockOnTime          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471761 /PROF/CPU2/VM/Halt/BlockOverslept   152599 ns/call (     1068194 ticks,       7 times, max    261780, min   52146)
00:01:28.471783 /PROF/CPU2/VM/Halt/R0HaltBlock          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471793 /PROF/CPU2/VM/Halt/R0HaltBlockInsomnia        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471806 /PROF/CPU2/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471813 /PROF/CPU2/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471819 /PROF/CPU2/VM/Halt/R0HaltExec           0 times
00:01:28.471824 /PROF/CPU2/VM/Halt/R0HaltExec/FromBlock        0 times
00:01:28.471842 /PROF/CPU2/VM/Halt/R0HaltExec/FromSpin        0 times
00:01:28.471848 /PROF/CPU2/VM/Halt/R0HaltHistoryCounter        0 times
00:01:28.471853 /PROF/CPU2/VM/Halt/R0HaltHistorySucceeded        0 times
00:01:28.471858 /PROF/CPU2/VM/Halt/R0HaltHistoryToRing3        0 times
00:01:28.471863 /PROF/CPU2/VM/Halt/R0HaltToR3           0 times
00:01:28.471868 /PROF/CPU2/VM/Halt/R0HaltToR3/FromSpin        0 times
00:01:28.471873 /PROF/CPU2/VM/Halt/R0HaltToR3/Other        0 times
00:01:28.471878 /PROF/CPU2/VM/Halt/R0HaltToR3/PendingFF        0 times
00:01:28.471883 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitNoInt        0 times
00:01:28.471888 /PROF/CPU2/VM/Halt/R0HaltToR3/PostWaitPendingFF        0 times
00:01:28.471894 /PROF/CPU2/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:01:28.471899 /PROF/CPU2/VM/Halt/Timers             331 ns/call (    10230092 ticks,   30846 times, max     36810, min       1)
00:01:28.471905 /PROF/CPU2/VM/Halt/Yield                0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471910 /PROF/CPU3/EM/Capped                    0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471916 /PROF/CPU3/EM/ForcedActions         39570 times
00:01:28.471922 /PROF/CPU3/EM/Halted                18216 times
00:01:28.471927 /PROF/CPU3/EM/NEMExec             2025307 ticks/call ( 75681689795 ticks,   37368 times, max  71473277, min   62774)
00:01:28.471933 /PROF/CPU3/EM/NEMExecuteCalled      19702 times
00:01:28.471938 /PROF/CPU3/EM/RAWTotal                  0 times
00:01:28.471944 /PROF/CPU3/EM/REMTotal                  0 times
00:01:28.471949 /PROF/CPU3/EM/RecordedExits        140170 times
00:01:28.471954 /PROF/CPU3/EM/Total              317035340748 ticks/call (317035340748 ticks,       1 times, max 317035340748, min 317035340748)
00:01:28.471961 /PROF/CPU3/VM/Halt/Block          2359801 ns/call ( 65640248394 ticks,   27816 times, max 232357977, min       1)
00:01:28.471967 /PROF/CPU3/VM/Halt/BlockInsomnia  1740097 ns/call ( 29545123874 ticks,   16979 times, max 232357977, min       1)
00:01:28.471973 /PROF/CPU3/VM/Halt/BlockOnTime    2197242 ns/call (  3922078370 ticks,    1785 times, max 162106322, min    4359)
00:01:28.471979 /PROF/CPU3/VM/Halt/BlockOverslept   441857 ns/call (  3999695332 ticks,    9052 times, max   8177610, min   50017)
00:01:28.471985 /PROF/CPU3/VM/Halt/R0HaltBlock          0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471990 /PROF/CPU3/VM/Halt/R0HaltBlockInsomnia        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.471997 /PROF/CPU3/VM/Halt/R0HaltBlockOnTime        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.472004 /PROF/CPU3/VM/Halt/R0HaltBlockOverslept        0 ns/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.472009 /PROF/CPU3/VM/Halt/R0HaltExec           0 times
00:01:28.472014 /PROF/CPU3/VM/Halt/R0HaltExec/FromBlock        0 times
00:01:28.472020 /PROF/CPU3/VM/Halt/R0HaltExec/FromSpin        0 times
00:01:28.472025 /PROF/CPU3/VM/Halt/R0HaltHistoryCounter        0 times
00:01:28.472030 /PROF/CPU3/VM/Halt/R0HaltHistorySucceeded        0 times
00:01:28.472035 /PROF/CPU3/VM/Halt/R0HaltHistoryToRing3        0 times
00:01:28.472040 /PROF/CPU3/VM/Halt/R0HaltToR3           0 times
00:01:28.472045 /PROF/CPU3/VM/Halt/R0HaltToR3/FromSpin        0 times
00:01:28.472050 /PROF/CPU3/VM/Halt/R0HaltToR3/Other        0 times
00:01:28.472055 /PROF/CPU3/VM/Halt/R0HaltToR3/PendingFF        0 times
00:01:28.472060 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitNoInt        0 times
00:01:28.472065 /PROF/CPU3/VM/Halt/R0HaltToR3/PostWaitPendingFF        0 times
00:01:28.472070 /PROF/CPU3/VM/Halt/R0HaltToR3/SmallDelta        0 times
00:01:28.472075 /PROF/CPU3/VM/Halt/Timers            4772 ns/call (   221062863 ticks,   46324 times, max    364217, min       2)
00:01:28.472081 /PROF/CPU3/VM/Halt/Yield             3934 ns/call (      149514 ticks,      38 times, max     49499, min    1443)
00:01:28.472088 /Public/NetAdapter/0/BytesReceived    41499 bytes
00:01:28.472093 /Public/NetAdapter/0/BytesTransmitted    13446 bytes
00:01:28.472098 /Public/NetAdapter/0/virtio-net         0 
00:01:28.472104 /Public/Storage/VIRTIO-SCSI0/Port0/BytesRead 494983680 bytes
00:01:28.472109 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufAttempts        0 count
00:01:28.472114 /Public/Storage/VIRTIO-SCSI0/Port0/QueryBufSuccess        0 count
00:01:28.472120 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsRead     8595 count
00:01:28.472125 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSubmitted     8595 count
00:01:28.472130 /Public/Storage/VIRTIO-SCSI0/Port0/ReqsSucceeded     8595 count
00:01:28.472135 /Public/Storage/VIRTIO-SCSI0/Port1/BytesRead 51435520 bytes
00:01:28.472140 /Public/Storage/VIRTIO-SCSI0/Port1/BytesWritten 17707008 bytes
00:01:28.472145 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufAttempts        0 count
00:01:28.472151 /Public/Storage/VIRTIO-SCSI0/Port1/QueryBufSuccess        0 count
00:01:28.472156 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsRead     1743 count
00:01:28.472161 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSubmitted     2560 count
00:01:28.472166 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsSucceeded     2560 count
00:01:28.472171 /Public/Storage/VIRTIO-SCSI0/Port1/ReqsWrite      817 count
00:01:28.472177 /Public/Storage/VIRTIO-SCSI0/Port2/BytesRead   463872 bytes
00:01:28.472182 /Public/Storage/VIRTIO-SCSI0/Port2/BytesWritten    81920 bytes
00:01:28.472187 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufAttempts        0 count
00:01:28.472192 /Public/Storage/VIRTIO-SCSI0/Port2/QueryBufSuccess        0 count
00:01:28.472198 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsRead      114 count
00:01:28.472203 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSubmitted      130 count
00:01:28.472208 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsSucceeded      130 count
00:01:28.472213 /Public/Storage/VIRTIO-SCSI0/Port2/ReqsWrite       16 count
00:01:28.472218 /SELM/LoadHidSel/GstReadErrors          0 times
00:01:28.472223 /SELM/LoadHidSel/NoGoodGuest            0 times
00:01:28.472229 /TM/CPU/00/cNsExecuting          10800370809 ns
00:01:28.472234 /TM/CPU/00/cNsHalted             61445514864 ns
00:01:28.472240 /TM/CPU/00/cNsOther              15822197462 ns
00:01:28.472245 /TM/CPU/00/cNsTotal              88068083135 ns
00:01:28.472251 /TM/CPU/00/cPeriodsExecuting       161855 count
00:01:28.472256 /TM/CPU/00/cPeriodsHalted           12129 count
00:01:28.472261 /TM/CPU/00/pctExecuting                 0 %
00:01:28.472266 /TM/CPU/00/pctHalted                   99 %
00:01:28.472272 /TM/CPU/00/pctOther                     0 %
00:01:28.472278 /TM/CPU/01/cNsExecuting          11372452291 ns
00:01:28.472284 /TM/CPU/01/cNsHalted             64398691839 ns
00:01:28.472289 /TM/CPU/01/cNsOther              12296769747 ns
00:01:28.472295 /TM/CPU/01/cNsTotal              88067913877 ns
00:01:28.472300 /TM/CPU/01/cPeriodsExecuting       127384 count
00:01:28.472305 /TM/CPU/01/cPeriodsHalted           13090 count
00:01:28.472310 /TM/CPU/01/pctExecuting                 0 %
00:01:28.472316 /TM/CPU/01/pctHalted                   97 %
00:01:28.472321 /TM/CPU/01/pctOther                     2 %
00:01:28.472327 /TM/CPU/02/cNsExecuting          9468163824 ns
00:01:28.472332 /TM/CPU/02/cNsHalted             67142298376 ns
00:01:28.472337 /TM/CPU/02/cNsOther              11457544172 ns
00:01:28.472343 /TM/CPU/02/cNsTotal              88068006372 ns
00:01:28.472348 /TM/CPU/02/cPeriodsExecuting       166264 count
00:01:28.472353 /TM/CPU/02/cPeriodsHalted           15422 count
00:01:28.472359 /TM/CPU/02/pctExecuting                 0 %
00:01:28.472364 /TM/CPU/02/pctHalted                   99 %
00:01:28.472369 /TM/CPU/02/pctOther                     0 %
00:01:28.472375 /TM/CPU/03/cNsExecuting          7816057662 ns
00:01:28.472380 /TM/CPU/03/cNsHalted             65889948766 ns
00:01:28.472385 /TM/CPU/03/cNsOther              14361972168 ns
00:01:28.472391 /TM/CPU/03/cNsTotal              88067978596 ns
00:01:28.472396 /TM/CPU/03/cPeriodsExecuting       151187 count
00:01:28.472401 /TM/CPU/03/cPeriodsHalted           16444 count
00:01:28.472407 /TM/CPU/03/pctExecuting                 0 %
00:01:28.472412 /TM/CPU/03/pctHalted                   98 %
00:01:28.472417 /TM/CPU/03/pctOther                     0 %
00:01:28.472423 /TM/CPU/pctExecuting                    0 %
00:01:28.472428 /TM/CPU/pctHalted                      98 %
00:01:28.472433 /TM/CPU/pctOther                        0 %
00:01:28.472439 /TM/MaxHzHint                           0 Hz
00:01:28.472444 /TM/PIT/Handler                         0 ticks/call (           0 ticks,       0 times, max         0, min      -1)
00:01:28.472450 /TM/PIT/Irq                             0 times
00:01:28.472456 /TM/R3/1nsSteps                      1332 times
00:01:28.472462 /TM/TSC/offCPU0                         0 ticks
00:01:28.472467 /TM/TSC/offCPU1                         0 ticks
00:01:28.472472 /TM/TSC/offCPU2                         0 ticks
00:01:28.472478 /TM/TSC/offCPU3                         0 ticks
00:01:28.472483 /TM/VirtualSync/CurrentOffset           0 ns
00:01:28.472489 ********************* End of statistics **********************
00:01:28.474369 fastpipe: deviceDestruct g_bGuestPowerOff=1
00:01:28.476015 GIM: KVM: Resetting MSRs
00:01:28.476070 NEM: Destroying partition 00000000010e9cd0 with its 4 VCpus...
00:01:28.628644 Changing the VM state from 'DESTROYING' to 'TERMINATED'
00:01:28.630799 Console: Machine state changed to 'PoweredOff'
00:01:28.630897 VBoxHeadless: processEventQueue: VERR_INTERRUPTED, termination requested
00:01:28.636222 VBoxHeadless: exiting
