import uiautomator2 as u2
from paddleocr import PaddleOCR
from PIL import Image
import pandas as pd
import os, time, traceback

# 配置参数
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "no_photo_people.csv")
PROGRESS_FILE = os.path.join(RESULT_DIR, "progress.txt")
SCREENSHOT_DIR = os.path.join(RESULT_DIR, "screenshots")
ERROR_LOG = os.path.join(RESULT_DIR, "error.log")

# 初始化
os.makedirs(SCREENSHOT_DIR, exist_ok=True)
ocr = PaddleOCR(use_angle_cls=True, lang="ch")
d = u2.connect()

# 初始数据文件
if not os.path.exists(RESULT_CSV):
    pd.DataFrame(columns=["姓名", "身份证号", "联系电话", "更新时间", "房屋地址", "记录时间", "列表页地址"]).to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')

start_index = 0
if os.path.exists(PROGRESS_FILE):
    with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
        start_index = int(f.read().strip())

def save_progress(index):
    with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
        f.write(str(index))

def log_error(msg):
    with open(ERROR_LOG, 'a', encoding='utf-8') as f:
        f.write(time.strftime("[%Y-%m-%d %H:%M:%S] ") + msg + "\n")

def extract_text_from_image(image_path):
    result = ocr.ocr(image_path)
    lines = [line[1][0] for line in result[0]]
    return lines

def extract_person_info(image_path):
    lines = extract_text_from_image(image_path)
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}
    for txt in lines:
        if "姓名" in txt:
            info["姓名"] = txt.replace("姓名", "").strip(': ：')
        elif "身份证" in txt:
            info["身份证号"] = txt.replace("身份证号", "").strip(': ：')
        elif "联系电话" in txt:
            info["联系电话"] = txt.replace("联系电话", "").strip(': ：')
        elif "更新" in txt:
            info["更新时间"] = txt.replace("更新时间", "").strip(': ：')
    return info

def extract_house_address(image_path):
    lines = extract_text_from_image(image_path)
    for txt in lines:
        if txt.startswith("云南省") or "小组" in txt:
            return txt
    return "地址识别失败"

def process_house(index):
    try:
        # 定位所有房屋的图片视图
        house_nodes = [el for el in d.xpath('//android.widget.ImageView').all()
                       if el.info['bounds']['top'] > 200]

        if index >= len(house_nodes):
            return False

        # 截图识别房屋地址
        screenshot_all = f"{SCREENSHOT_DIR}/full_{index}_{int(time.time())}.png"
        d.screenshot(screenshot_all)
        #house_address = extract_house_address(screenshot_all)
        print(f"[INFO] 处理房屋 {index+1}")

        # 点击图片框进入详情
        house_nodes[index].click()
        time.sleep(2)
        if not d(text="实有人口").exists:
            d.press("back")
            return True

        d(text="实有人口").click()
        time.sleep(2)

        # 截图识别头像区域是否为空
        screenshot_name = f"{SCREENSHOT_DIR}/{index}_{int(time.time())}.png"
        d.screenshot(screenshot_name)
        img_crop = Image.open(screenshot_name).crop((40, 400, 250, 600))
        crop_path = screenshot_name.replace(".png", "_crop.png")
        img_crop.save(crop_path)

        if any("暂无图片" in line for line in extract_text_from_image(crop_path)):
            info = extract_person_info(screenshot_name)
            info["房屋地址"] = "自动抓取中"
            info["记录时间"] = time.strftime("%Y-%m-%d %H:%M:%S")
            info["列表页地址"] = house_address

            df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
            df.loc[len(df)] = info
            df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
            print(f"[SAVE] 已保存人员：{info['姓名']}，房屋：{house_address}")
        else:
            print("[SKIP] 有头像，跳过保存")

        d.press("back")
        time.sleep(1)
        d.press("back")
        time.sleep(1)
        return True

    except Exception as e:
        log_error(f"房屋 index {index} 出错：{str(e)}\n{traceback.format_exc()}")
        try:
            d.press("back"); d.press("back")
        except:
            pass
        return True


def process_house_node(house_node, index):
    try:
        screenshot_all = f"{SCREENSHOT_DIR}/full_{index}_{int(time.time())}.png"
        d.screenshot(screenshot_all)
        house_address = extract_house_address(screenshot_all)
        print(f"[INFO] 房屋地址：{house_address}")

        house_node.click()
        time.sleep(2)
        if not d(text="实有人口").exists:
            d.press("back")
            return True

        d(text="实有人口").click()
        time.sleep(2)

        screenshot_name = f"{SCREENSHOT_DIR}/{index}_{int(time.time())}.png"
        d.screenshot(screenshot_name)
        img_crop = Image.open(screenshot_name).crop((40, 400, 250, 600))
        crop_path = screenshot_name.replace(".png", "_crop.png")
        img_crop.save(crop_path)

        if any("暂无图片" in line for line in extract_text_from_image(crop_path)):
            info = extract_person_info(screenshot_name)
            info["房屋地址"] = "自动抓取中"
            info["记录时间"] = time.strftime("%Y-%m-%d %H:%M:%S")
            info["列表页地址"] = house_address

            df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
            df.loc[len(df)] = info
            df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
            print(f"[SAVE] 已保存人员：{info['姓名']}，房屋：{house_address}")
        else:
            print("[SKIP] 有头像，跳过保存")

        d.press("back")
        time.sleep(1)
        d.press("back")
        time.sleep(1)
        return True

    except Exception as e:
        log_error(f"处理房屋节点出错：{str(e)}\n{traceback.format_exc()}")
        try:
            d.press("back"); d.press("back")
        except:
            pass
        return True

def process_current_visible_houses():
    house_nodes = d.xpath('//android.widget.ImageView').all()
    print(f"[INFO] 当前页面房屋数量: {len(house_nodes)}")

    for i, node in enumerate(house_nodes):
        print(f"[INFO] 正在处理第 {i+1} 个房屋")
        success = process_house_node(node, i)
        time.sleep(1)

def main():
    print("[INFO] 启动应用...")
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(10)

    time.sleep(5)
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)

    processed_pages = 0
    while True:
        print(f"\n===== 处理第 {processed_pages + 1} 屏房屋 =====")
        process_current_visible_houses()
        d(scrollable=True).scroll.vert.forward(steps=30)
        time.sleep(3)
        processed_pages += 1
        save_progress(processed_pages)

if __name__ == '__main__':
    main()
