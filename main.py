import uiautomator2 as u2
from paddleocr import PaddleOCR
from PIL import Image
import pandas as pd
import os, time, traceback

# 配置参数
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "no_photo_people.csv")
PROGRESS_FILE = os.path.join(RESULT_DIR, "progress.txt")
SCREENSHOT_DIR = os.path.join(RESULT_DIR, "screenshots")
ERROR_LOG = os.path.join(RESULT_DIR, "error.log")

# 初始化
os.makedirs(SCREENSHOT_DIR, exist_ok=True)
ocr = PaddleOCR(use_angle_cls=True, lang="ch")
d = u2.connect()

# 初始数据文件
if not os.path.exists(RESULT_CSV):
    pd.DataFrame(columns=["姓名", "身份证号", "联系电话", "更新时间", "房屋地址", "记录时间", "列表页地址"]).to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')

start_index = 0
if os.path.exists(PROGRESS_FILE):
    with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
        start_index = int(f.read().strip())

def save_progress(index):
    with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
        f.write(str(index))

def log_error(msg):
    with open(ERROR_LOG, 'a', encoding='utf-8') as f:
        f.write(time.strftime("[%Y-%m-%d %H:%M:%S] ") + msg + "\n")

def extract_text_from_image(image_path):
    try:
        result = ocr.ocr(image_path)
        if result and result[0]:
            lines = [line[1][0] for line in result[0] if line and len(line) > 1]
            return lines
        return []
    except Exception as e:
        print(f"[ERROR] OCR识别失败: {e}")
        return []

def extract_person_info(image_path):
    lines = extract_text_from_image(image_path)
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

    # 合并所有文本行，便于查找
    all_text = " ".join(lines)

    for i, txt in enumerate(lines):
        # 提取姓名 - 查找"姓"字后面的内容
        if "姓" in txt and "名" in txt:
            # 找到姓名后面的值
            name_part = txt.split("姓")[1] if "姓" in txt else txt
            if "名" in name_part:
                name_part = name_part.split("名")[1]
            info["姓名"] = name_part.strip(': ：').strip()
        elif txt.strip() and len(txt.strip()) <= 10 and not any(char in txt for char in ["证", "电话", "更新", "时间"]):
            # 如果是短文本且不包含其他关键字，可能是姓名
            if not info["姓名"] and len(txt.strip()) >= 2:
                info["姓名"] = txt.strip()

        # 提取身份证号 - 18位数字
        if "身份证" in txt or len(txt.replace(" ", "")) == 18:
            # 提取18位数字
            import re
            id_match = re.search(r'\d{18}', txt.replace(" ", ""))
            if id_match:
                info["身份证号"] = id_match.group()

        # 提取联系电话 - 11位数字
        if "联系电话" in txt or "电话" in txt:
            import re
            phone_match = re.search(r'1[3-9]\d{9}', txt.replace(" ", ""))
            if phone_match:
                info["联系电话"] = phone_match.group()

        # 提取更新时间
        if "更新时间" in txt:
            time_part = txt.split("更新时间")[1] if "更新时间" in txt else ""
            info["更新时间"] = time_part.strip(': ：').strip()
        elif "2025-" in txt or "2024-" in txt:
            # 直接匹配时间格式
            import re
            time_match = re.search(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', txt)
            if time_match:
                info["更新时间"] = time_match.group()

    return info

def extract_house_address_from_ui():
    """从UI元素中提取房屋地址"""
    try:
        # 获取页面上所有文本元素
        all_texts = get_all_text_elements()

        # 查找包含地址关键词的文本
        for text in all_texts:
            # 优先查找以"云南省"开头的完整地址
            if text.startswith("云南省") and len(text) > 15:
                return text.strip()
            # 查找包含"小组"的地址
            elif "小组" in text and len(text) > 10:
                return text.strip()
            # 查找其他地址关键词
            elif any(keyword in text for keyword in ["村", "镇", "县", "市", "区", "路", "街", "号"]):
                # 过滤掉一些不相关的文本
                if len(text) > 10 and not any(skip in text for skip in ["管辖单位", "地址类型", "搜索", "地址描述"]):
                    return text.strip()

        return "地址识别失败"
    except Exception as e:
        print(f"[ERROR] 提取地址失败: {e}")
        return "地址识别失败"

def extract_house_address(image_path):
    """备用的OCR地址提取方法"""
    try:
        lines = extract_text_from_image(image_path)
        for txt in lines:
            if txt.startswith("云南省") or "小组" in txt:
                return txt
        return "地址识别失败"
    except:
        return "地址识别失败"




def get_all_text_elements():
    """获取页面上所有的文本元素"""
    try:
        # 方法1：通过XML解析获取文本
        xml = d.dump_hierarchy()
        import re
        texts = re.findall(r'text="([^"]*)"', xml)
        # 过滤空文本
        texts = [text for text in texts if text.strip()]
        return texts
    except Exception as e:
        print(f"[ERROR] 获取文本元素失败: {e}")
        return []

def find_person_cards():
    """查找页面上的人员卡片"""
    try:
        # 方法1：查找包含"暂无图片"的元素
        no_photo_elements = d.xpath('//*[contains(@text, "暂无图片")]').all()
        person_cards = []

        for element in no_photo_elements:
            print(f"[INFO] 找到暂无图片元素")

            # 获取父容器或相邻元素的信息
            try:
                # 尝试获取父容器
                parent = element.parent
                if parent:
                    # 获取父容器内的所有文本
                    parent_texts = []
                    for child in parent.xpath('.//android.widget.TextView').all():
                        text = child.get_text()
                        if text and text.strip():
                            parent_texts.append(text.strip())

                    if parent_texts:
                        person_cards.append(parent_texts)
                        print(f"[DEBUG] 人员卡片文本: {parent_texts}")
            except:
                pass

        # 方法2：如果方法1没找到，尝试直接查找特定的文本模式
        if not person_cards:
            print("[INFO] 尝试直接查找人员信息文本")
            all_texts = get_all_text_elements()

            # 查找包含姓名、身份证等关键信息的文本组合
            current_person_texts = []
            for i, text in enumerate(all_texts):
                # 如果找到姓名格式的文本
                if "名" in text and (":" in text or "：" in text):
                    current_person_texts = []
                    # 收集这个人员的相关信息（前后几行）
                    start_idx = max(0, i - 2)
                    end_idx = min(len(all_texts), i + 8)
                    for j in range(start_idx, end_idx):
                        current_person_texts.append(all_texts[j])

                    # 检查是否包含"暂无图片"
                    if any("暂无图片" in t for t in current_person_texts):
                        person_cards.append(current_person_texts)
                        print(f"[DEBUG] 通过文本模式找到人员: {current_person_texts}")

        return person_cards
    except Exception as e:
        print(f"[ERROR] 查找人员卡片失败: {e}")
        return []

def extract_person_info_from_card(card_texts):
    """从人员卡片文本中提取信息"""
    person_info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

    import re

    for text in card_texts:
        # 提取姓名 - 查找"名："后面的内容
        if "名" in text and (":" in text or "：" in text):
            name_match = re.search(r'名\s*[：:]\s*([^\s]+)', text)
            if name_match:
                person_info["姓名"] = name_match.group(1).strip()

        # 提取身份证号 - 18位数字
        id_match = re.search(r'(\d{18})', text)
        if id_match:
            person_info["身份证号"] = id_match.group(1)

        # 提取联系电话 - 11位手机号
        phone_match = re.search(r'(1[3-9]\d{9})', text)
        if phone_match:
            person_info["联系电话"] = phone_match.group(1)

        # 提取更新时间
        time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
        if time_match:
            person_info["更新时间"] = time_match.group(1)

    return person_info

def process_people_in_house(house_address):
    """处理房屋内的所有人员信息"""
    saved_count = 0
    scroll_attempts = 0
    max_scroll_attempts = 10
    processed_people = set()  # 避免重复处理同一个人

    while scroll_attempts < max_scroll_attempts:
        print(f"[INFO] 正在处理人员信息，尝试次数: {scroll_attempts + 1}")

        # 方法1：直接查找特定的UI元素
        people_found_this_round = False

        try:
            # 查找所有包含"暂无图片"的元素
            if d(text="暂无图片").exists:
                print("[INFO] 发现暂无图片元素")

                # 获取页面上所有文本
                all_texts = get_all_text_elements()
                print(f"[DEBUG] 页面文本数量: {len(all_texts)}")

                # 查找人员信息模式
                import re
                person_info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

                for text in all_texts:
                    # 查找姓名 - 格式：姓名：李爱芬 或 名：李爱芬
                    name_match = re.search(r'(?:姓\s*)?名\s*[：:]\s*([^\s\d]+)', text)
                    if name_match and not person_info["姓名"]:
                        person_info["姓名"] = name_match.group(1).strip()
                        print(f"[DEBUG] 找到姓名: {person_info['姓名']}")

                    # 查找身份证号 - 18位数字
                    id_match = re.search(r'(\d{18})', text)
                    if id_match and not person_info["身份证号"]:
                        person_info["身份证号"] = id_match.group(1)
                        print(f"[DEBUG] 找到身份证: {person_info['身份证号']}")

                    # 查找联系电话 - 11位手机号
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match and not person_info["联系电话"]:
                        person_info["联系电话"] = phone_match.group(1)
                        print(f"[DEBUG] 找到电话: {person_info['联系电话']}")

                    # 查找更新时间
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match and not person_info["更新时间"]:
                        person_info["更新时间"] = time_match.group(1)
                        print(f"[DEBUG] 找到更新时间: {person_info['更新时间']}")

                # 如果找到了有效的人员信息且未处理过
                person_key = f"{person_info['姓名']}_{person_info['身份证号']}"
                if person_info["姓名"] and person_info["身份证号"] and person_key not in processed_people:
                    processed_people.add(person_key)

                    person_info["房屋地址"] = house_address
                    person_info["记录时间"] = time.strftime("%Y-%m-%d %H:%M:%S")
                    person_info["列表页地址"] = house_address

                    # 保存到CSV
                    df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
                    df.loc[len(df)] = person_info
                    df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
                    print(f"[SAVE] 已保存人员：{person_info['姓名']} - {person_info['身份证号']} (暂无图片)")
                    saved_count += 1
                    people_found_this_round = True
                else:
                    print(f"[INFO] 人员信息不完整或已处理过: 姓名={person_info['姓名']}, 身份证={person_info['身份证号']}")

        except Exception as e:
            print(f"[ERROR] 处理人员信息时出错: {e}")

        # 如果这轮没有找到新的人员，尝试下拉
        if not people_found_this_round:
            scroll_attempts += 1
            if scroll_attempts < max_scroll_attempts:
                print(f"[INFO] 下拉查看更多人员 (尝试 {scroll_attempts}/{max_scroll_attempts})")
                try:
                    d(scrollable=True).scroll.vert.forward(steps=3)
                    time.sleep(2)
                except:
                    print("[INFO] 滚动失败，结束查找")
                    break
            else:
                print("[INFO] 已达到最大滚动次数，结束查找")
                break
        else:
            # 找到人员后，继续下拉查看是否还有更多
            try:
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_attempts += 1
            except:
                print("[INFO] 滚动失败，结束查找")
                break

    return saved_count

def extract_person_info_from_text(text):
    """从文本中提取人员信息"""
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

    import re

    # 提取姓名
    name_match = re.search(r'姓\s*名?\s*[：:]\s*([^\s]+)', text)
    if name_match:
        info["姓名"] = name_match.group(1).strip()
    else:
        # 尝试其他模式
        name_match = re.search(r'名\s*[：:]\s*([^\s]+)', text)
        if name_match:
            info["姓名"] = name_match.group(1).strip()

    # 提取身份证号
    id_match = re.search(r'(\d{18})', text)
    if id_match:
        info["身份证号"] = id_match.group(1)

    # 提取联系电话
    phone_match = re.search(r'(1[3-9]\d{9})', text)
    if phone_match:
        info["联系电话"] = phone_match.group(1)

    # 提取更新时间
    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
    if time_match:
        info["更新时间"] = time_match.group(1)

    return info

def get_house_info_from_list():
    """从房屋列表中获取房屋信息"""
    try:
        # 获取页面上所有文本，查找房屋地址
        all_texts = get_all_text_elements()
        houses = []

        current_house = {}
        for text in all_texts:
            # 查找姓名（房主）
            if len(text) >= 2 and len(text) <= 4 and all('\u4e00' <= c <= '\u9fff' for c in text):
                if not any(skip in text for skip in ["自住房", "出租房", "全部", "地址", "人员", "单位"]):
                    current_house["房主"] = text

            # 查找地址
            elif text.startswith("云南省") or "小组" in text:
                current_house["地址"] = text
                if "房主" in current_house:
                    houses.append(current_house.copy())
                    current_house = {}

        return houses
    except Exception as e:
        print(f"[ERROR] 获取房屋信息失败: {e}")
        return []

def process_house_node(house_node, index):
    try:
        # 先尝试从UI获取地址信息
        house_address = extract_house_address_from_ui()

        # 如果UI获取失败，使用OCR备用方案
        if house_address == "地址识别失败":
            screenshot_all = f"{SCREENSHOT_DIR}/full_{index}_{int(time.time())}.png"
            d.screenshot(screenshot_all)
            house_address = extract_house_address(screenshot_all)

        print(f"[INFO] 房屋地址：{house_address}")

        # 点击房屋节点
        house_node.click()
        time.sleep(3)

        # 检查是否成功进入房屋详情页
        if not d(text="实有人口").exists:
            print("[INFO] 未找到实有人口按钮，可能未成功进入房屋详情页")
            # 只返回一次，避免回到首页
            d.press("back")
            time.sleep(2)
            return True

        # 点击实有人口按钮
        print("[INFO] 点击实有人口按钮")
        d(text="实有人口").click()
        time.sleep(3)

        # 检查是否成功进入人员列表页
        if not (d(text="暂无图片").exists or d.xpath('//*[contains(@text, "暂无图片")]').exists):
            print("[INFO] 未找到人员信息，可能是空房屋")
            # 返回到房屋列表页（两次back）
            d.press("back")
            time.sleep(1)
            d.press("back")
            time.sleep(2)
            return True

        # 处理房屋内的所有人员
        saved_count = process_people_in_house(house_address)
        print(f"[INFO] 本房屋共保存 {saved_count} 个暂无图片人员")

        # 返回到房屋列表页（两次back）
        print("[INFO] 返回房屋列表页")
        d.press("back")
        time.sleep(1)
        d.press("back")
        time.sleep(2)

        # 验证是否成功返回到房屋列表页
        if not (d(text="搜索").exists or d(text="共有").exists):
            print("[WARNING] 可能未正确返回到房屋列表页")

        return True

    except Exception as e:
        log_error(f"处理房屋节点出错：{str(e)}\n{traceback.format_exc()}")
        try:
            # 尝试返回到房屋列表页
            for _ in range(3):
                if d(text="搜索").exists or d(text="共有").exists:
                    break
                d.press("back")
                time.sleep(1)
        except:
            pass
        return True

def find_house_items():
    """查找页面上的房屋项目"""
    try:
        # 方法1：查找包含房主姓名的元素
        house_items = []

        # 查找所有可能的房屋容器
        # 尝试不同的选择器
        selectors = [
            '//android.widget.LinearLayout[contains(@resource-id, "item")]',
            '//android.widget.RelativeLayout[contains(@resource-id, "item")]',
            '//android.view.ViewGroup[contains(@resource-id, "item")]',
            '//android.widget.FrameLayout[contains(@resource-id, "item")]'
        ]

        for selector in selectors:
            items = d.xpath(selector).all()
            if items:
                print(f"[DEBUG] 使用选择器 {selector} 找到 {len(items)} 个项目")
                house_items = items
                break

        # 如果上述方法都没找到，尝试查找图片元素
        if not house_items:
            image_elements = d.xpath('//android.widget.ImageView').all()
            # 过滤掉太小的图片（可能是图标）
            house_items = [img for img in image_elements if img.bounds[2] - img.bounds[0] > 50]
            print(f"[DEBUG] 通过ImageView找到 {len(house_items)} 个可能的房屋项目")

        return house_items
    except Exception as e:
        print(f"[ERROR] 查找房屋项目失败: {e}")
        return []

def process_current_visible_houses():
    """处理当前页面可见的房屋"""
    house_items = find_house_items()
    print(f"[INFO] 当前页面房屋数量: {len(house_items)}")

    if not house_items:
        print("[WARNING] 未找到房屋项目，可能页面结构发生变化")
        return

    for i, item in enumerate(house_items):
        print(f"\n[INFO] 正在处理第 {i+1}/{len(house_items)} 个房屋")

        try:
            # 获取项目的边界信息
            bounds = item.bounds
            print(f"[DEBUG] 房屋项目边界: {bounds}")

            success = process_house_node(item, i)
            if not success:
                print(f"[WARNING] 处理第 {i+1} 个房屋失败")

            time.sleep(2)  # 增加间隔时间，避免操作过快

        except Exception as e:
            print(f"[ERROR] 处理第 {i+1} 个房屋时出错: {e}")
            continue

def debug_page_structure():
    """调试页面结构"""
    try:
        print("[DEBUG] 分析页面结构...")

        # 获取所有文本元素
        all_texts = get_all_text_elements()
        print(f"[DEBUG] 页面文本元素数量: {len(all_texts)}")
        print(f"[DEBUG] 前10个文本: {all_texts[:10]}")

        # 查找可能的房屋容器
        containers = d.xpath('//android.widget.LinearLayout').all()
        print(f"[DEBUG] LinearLayout数量: {len(containers)}")

        # 查找图片元素
        images = d.xpath('//android.widget.ImageView').all()
        print(f"[DEBUG] ImageView数量: {len(images)}")

        # 查找包含地址的文本
        address_texts = [text for text in all_texts if any(keyword in text for keyword in ["云南省", "小组", "村", "镇"])]
        print(f"[DEBUG] 包含地址的文本: {address_texts}")

    except Exception as e:
        print(f"[ERROR] 调试页面结构失败: {e}")

def main():
    print("[INFO] 启动应用...")
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(10)

    time.sleep(5)
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)

    # 调试页面结构
    debug_page_structure()

    processed_pages = 0
    while True:
        print(f"\n===== 处理第 {processed_pages + 1} 屏房屋 =====")
        process_current_visible_houses()

        # 检查是否还有更多页面
        try:
            d(scrollable=True).scroll.vert.forward(steps=30)
            time.sleep(3)
        except:
            print("[INFO] 无法继续滚动，可能已到达页面底部")
            break

        processed_pages += 1
        save_progress(processed_pages)

        # 避免无限循环，设置最大处理页数
        if processed_pages >= 50:
            print("[INFO] 已处理50页，停止处理")
            break

if __name__ == '__main__':
    main()
