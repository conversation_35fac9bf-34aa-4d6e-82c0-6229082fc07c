# 导入必要库
from airtest.core.api import *
from airtest.aircv import imread
import csv
import json
import os
import time
from paddleocr import PaddleOCR

# 配置路径和参数
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
PROGRESS_FILE = "progress.json"
RESULT_CSV = "no_photo_people.csv"
SCREENSHOT_DIR = "screenshots"
os.makedirs(SCREENSHOT_DIR, exist_ok=True)

# 初始化OCR
ocr = PaddleOCR(use_angle_cls=True, lang="ch")  # 中文OCR

# 读取进度文件，获取断点继续位置
def load_progress():
    if os.path.exists(PROGRESS_FILE):
        with open(PROGRESS_FILE, 'r') as f:
            return json.load(f)
    return {"last_index": 0}

# 保存进度
def save_progress(index):
    with open(PROGRESS_FILE, 'w') as f:
        json.dump({"last_index": index}, f)

# 保存结果到 CSV 文件
def save_to_csv(data):
    is_new = not os.path.exists(RESULT_CSV)
    with open(RESULT_CSV, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        if is_new:
            writer.writerow(["姓名", "身份证号", "联系电话", "更新时间"])
        writer.writerow(data)

# 采集截图保存
def save_screenshot(name, index):
    screenshot_path = os.path.join(SCREENSHOT_DIR, f"{name}_{index}_{int(time.time())}.png")
    snapshot(filename=screenshot_path)
    return screenshot_path

# OCR 提取人员信息
def extract_person_info(image_path):
    result = ocr.ocr(image_path)
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}
    for line in result[0]:
        txt = line[1][0]
        if "姓名" in txt:
            info["姓名"] = txt.replace("姓名", "").strip(': ：')
        elif "身份证" in txt:
            info["身份证号"] = txt.replace("身份证号", "").strip(': ：')
        elif "联系电话" in txt:
            info["联系电话"] = txt.replace("联系电话", "").strip(': ：')
        elif "更新" in txt:
            info["更新时间"] = txt.replace("更新时间", "").strip(': ：')
    return info

# 判断“暂无图片”的人员
def is_no_image(ocr_result):
    for line in ocr_result:
        for box in line:
            text = box[1][0]
            if "暂无图片" in text:
                return True
    return False

# 点击三实查询按钮
def click_three_actual():
    touch(Template(r"三实查询按钮.png"))
    time.sleep(5)

# 点击搜索按钮
def click_search():
    touch(Template(r"搜索按钮.png"))
    time.sleep(5)

# 处理单个房屋，判断是否有“暂无图片”的人员
def process_house(index):
    house_nodes = find_all(Template(r"房屋列表.png"))
    if index >= len(house_nodes):
        return False

    # 点击当前房屋
    house_nodes[index].click()
    time.sleep(2)

    # 点击“实有人口”
    if exists(Template(r"实有人口标签.png")):
        touch(Template(r"实有人口标签.png"))
        time.sleep(2)

        # 截图并保存
        screenshot_path = save_screenshot(f"房屋{index}", index)

        # OCR 判断“暂无图片”
        result = ocr.ocr(screenshot_path)
        if is_no_image(result):
            info = extract_person_info(screenshot_path)
            save_to_csv([info["姓名"], info["身份证号"], info["联系电话"], info["更新时间"]])

        # 返回上一级
        back()
        time.sleep(2)
        return True
    return False

# 主函数，执行采集
def main():
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    # 点击三实查询，等按钮出现
    click_three_actual()

    # 点击搜索，等待加载
    click_search()

    # 获取上次进度，开始继续采集
    progress = load_progress()
    index = progress["last_index"]

    # 采集所有房屋数据
    while True:
        success = process_house(index)
        if not success:
            swipe(500, 1500, 500, 500, 1000)  # 滑动加载更多数据
            time.sleep(2)
            index = 0
        else:
            index += 1
        save_progress(index)  # 保存进度

if __name__ == '__main__':
    main()
