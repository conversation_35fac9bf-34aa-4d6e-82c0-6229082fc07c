import uiautomator2 as u2
from paddleocr import PaddleOCR
from PIL import Image
import pandas as pd
import os, time, traceback

# 配置参数
APP_PACKAGE = "ynga.com.HBuilder.UniPlugin"
RESULT_DIR = "Noxx_sscj"
RESULT_CSV = os.path.join(RESULT_DIR, "no_photo_people.csv")
PROGRESS_FILE = os.path.join(RESULT_DIR, "progress.txt")
SCREENSHOT_DIR = os.path.join(RESULT_DIR, "screenshots")
ERROR_LOG = os.path.join(RESULT_DIR, "error.log")

# 初始化
os.makedirs(SCREENSHOT_DIR, exist_ok=True)
ocr = PaddleOCR(use_angle_cls=True, lang="ch")
d = u2.connect()

# 初始数据文件
if not os.path.exists(RESULT_CSV):
    pd.DataFrame(columns=["姓名", "身份证号", "联系电话", "更新时间", "房屋地址", "记录时间", "列表页地址"]).to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')

start_index = 0
if os.path.exists(PROGRESS_FILE):
    with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
        start_index = int(f.read().strip())

def save_progress(index):
    with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
        f.write(str(index))

def log_error(msg):
    with open(ERROR_LOG, 'a', encoding='utf-8') as f:
        f.write(time.strftime("[%Y-%m-%d %H:%M:%S] ") + msg + "\n")

def extract_text_from_image(image_path):
    result = ocr.ocr(image_path)
    lines = [line[1][0] for line in result[0]]
    return lines

def extract_person_info(image_path):
    lines = extract_text_from_image(image_path)
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

    # 合并所有文本行，便于查找
    all_text = " ".join(lines)

    for i, txt in enumerate(lines):
        # 提取姓名 - 查找"姓"字后面的内容
        if "姓" in txt and "名" in txt:
            # 找到姓名后面的值
            name_part = txt.split("姓")[1] if "姓" in txt else txt
            if "名" in name_part:
                name_part = name_part.split("名")[1]
            info["姓名"] = name_part.strip(': ：').strip()
        elif txt.strip() and len(txt.strip()) <= 10 and not any(char in txt for char in ["证", "电话", "更新", "时间"]):
            # 如果是短文本且不包含其他关键字，可能是姓名
            if not info["姓名"] and len(txt.strip()) >= 2:
                info["姓名"] = txt.strip()

        # 提取身份证号 - 18位数字
        if "身份证" in txt or len(txt.replace(" ", "")) == 18:
            # 提取18位数字
            import re
            id_match = re.search(r'\d{18}', txt.replace(" ", ""))
            if id_match:
                info["身份证号"] = id_match.group()

        # 提取联系电话 - 11位数字
        if "联系电话" in txt or "电话" in txt:
            import re
            phone_match = re.search(r'1[3-9]\d{9}', txt.replace(" ", ""))
            if phone_match:
                info["联系电话"] = phone_match.group()

        # 提取更新时间
        if "更新时间" in txt:
            time_part = txt.split("更新时间")[1] if "更新时间" in txt else ""
            info["更新时间"] = time_part.strip(': ：').strip()
        elif "2025-" in txt or "2024-" in txt:
            # 直接匹配时间格式
            import re
            time_match = re.search(r'20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}', txt)
            if time_match:
                info["更新时间"] = time_match.group()

    return info

def extract_house_address(image_path):
    lines = extract_text_from_image(image_path)
    for txt in lines:
        if txt.startswith("云南省") or "小组" in txt:
            return txt
    return "地址识别失败"




def process_people_in_house(house_address):
    """处理房屋内的所有人员信息"""
    saved_count = 0
    scroll_attempts = 0
    max_scroll_attempts = 10
    processed_people = set()  # 避免重复处理同一个人

    while scroll_attempts < max_scroll_attempts:
        # 截图识别当前页面的人员
        screenshot_name = f"{SCREENSHOT_DIR}/people_{int(time.time())}.png"
        d.screenshot(screenshot_name)

        # 获取所有文本行
        lines = extract_text_from_image(screenshot_name)
        print(f"[DEBUG] 识别到的文本行: {lines}")

        # 查找人员信息
        people_found_this_round = False
        i = 0

        while i < len(lines):
            line = lines[i]

            # 查找"暂无图片"标识
            if "暂无图片" in line:
                print(f"[INFO] 发现暂无图片人员")

                # 向前查找人员信息（姓名通常在暂无图片前面）
                person_info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

                # 在当前位置前后查找人员信息
                search_range = range(max(0, i-10), min(len(lines), i+10))

                for j in search_range:
                    text = lines[j]

                    # 提取姓名 - 查找"名："后面的内容
                    if "名" in text and (":" in text or "：" in text):
                        name_part = text.split("名")[1] if "名" in text else ""
                        name_part = name_part.replace(":", "").replace("：", "").strip()
                        if name_part and len(name_part) <= 10:
                            person_info["姓名"] = name_part

                    # 提取身份证号 - 18位数字
                    import re
                    id_match = re.search(r'(\d{18})', text)
                    if id_match:
                        person_info["身份证号"] = id_match.group(1)

                    # 提取联系电话 - 11位手机号
                    phone_match = re.search(r'(1[3-9]\d{9})', text)
                    if phone_match:
                        person_info["联系电话"] = phone_match.group(1)

                    # 提取更新时间
                    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
                    if time_match:
                        person_info["更新时间"] = time_match.group(1)

                # 如果找到了有效的人员信息且未处理过
                person_key = f"{person_info['姓名']}_{person_info['身份证号']}"
                if person_info["姓名"] and person_key not in processed_people:
                    processed_people.add(person_key)

                    person_info["房屋地址"] = house_address
                    person_info["记录时间"] = time.strftime("%Y-%m-%d %H:%M:%S")
                    person_info["列表页地址"] = house_address

                    # 保存到CSV
                    df = pd.read_csv(RESULT_CSV, encoding='utf-8-sig')
                    df.loc[len(df)] = person_info
                    df.to_csv(RESULT_CSV, index=False, encoding='utf-8-sig')
                    print(f"[SAVE] 已保存人员：{person_info['姓名']} - {person_info['身份证号']} (暂无图片)")
                    saved_count += 1
                    people_found_this_round = True

            i += 1

        # 如果这轮没有找到新的人员，尝试下拉
        if not people_found_this_round:
            scroll_attempts += 1
            if scroll_attempts < max_scroll_attempts:
                print(f"[INFO] 下拉查看更多人员 (尝试 {scroll_attempts}/{max_scroll_attempts})")
                try:
                    d(scrollable=True).scroll.vert.forward(steps=3)
                    time.sleep(2)
                except:
                    break
            else:
                break
        else:
            # 找到人员后，继续下拉查看是否还有更多
            try:
                d(scrollable=True).scroll.vert.forward(steps=3)
                time.sleep(2)
                scroll_attempts += 1
            except:
                break

    return saved_count

def extract_person_info_from_text(text):
    """从文本中提取人员信息"""
    info = {"姓名": "", "身份证号": "", "联系电话": "", "更新时间": ""}

    import re

    # 提取姓名
    name_match = re.search(r'姓\s*名?\s*[：:]\s*([^\s]+)', text)
    if name_match:
        info["姓名"] = name_match.group(1).strip()
    else:
        # 尝试其他模式
        name_match = re.search(r'名\s*[：:]\s*([^\s]+)', text)
        if name_match:
            info["姓名"] = name_match.group(1).strip()

    # 提取身份证号
    id_match = re.search(r'(\d{18})', text)
    if id_match:
        info["身份证号"] = id_match.group(1)

    # 提取联系电话
    phone_match = re.search(r'(1[3-9]\d{9})', text)
    if phone_match:
        info["联系电话"] = phone_match.group(1)

    # 提取更新时间
    time_match = re.search(r'(20\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})', text)
    if time_match:
        info["更新时间"] = time_match.group(1)

    return info

def process_house_node(house_node, index):
    try:
        screenshot_all = f"{SCREENSHOT_DIR}/full_{index}_{int(time.time())}.png"
        d.screenshot(screenshot_all)
        house_address = extract_house_address(screenshot_all)
        print(f"[INFO] 房屋地址：{house_address}")

        house_node.click()
        time.sleep(2)

        # 检查是否有实有人口按钮
        if not d(text="实有人口").exists:
            print("[INFO] 未找到实有人口按钮，跳过")
            d.press("back")
            return True

        # 点击实有人口按钮
        print("[INFO] 点击实有人口按钮")
        d(text="实有人口").click()
        time.sleep(3)

        # 处理房屋内的所有人员
        saved_count = process_people_in_house(house_address)
        print(f"[INFO] 本房屋共保存 {saved_count} 个暂无图片人员")

        # 返回上级页面
        d.press("back")
        time.sleep(1)
        d.press("back")
        time.sleep(1)
        return True

    except Exception as e:
        log_error(f"处理房屋节点出错：{str(e)}\n{traceback.format_exc()}")
        try:
            d.press("back"); d.press("back")
        except:
            pass
        return True

def process_current_visible_houses():
    house_nodes = d.xpath('//android.widget.ImageView').all()
    print(f"[INFO] 当前页面房屋数量: {len(house_nodes)}")

    for i, node in enumerate(house_nodes):
        print(f"[INFO] 正在处理第 {i+1} 个房屋")
        success = process_house_node(node, i)
        time.sleep(1)

def main():
    print("[INFO] 启动应用...")
    d.app_start(APP_PACKAGE)
    time.sleep(20)

    for _ in range(3):
        if d(text="三实查询").exists:
            d(text="三实查询").click()
            break
        time.sleep(10)

    time.sleep(5)
    if d(text="搜索").exists:
        d(text="搜索").click()
        time.sleep(3)

    processed_pages = 0
    while True:
        print(f"\n===== 处理第 {processed_pages + 1} 屏房屋 =====")
        process_current_visible_houses()
        d(scrollable=True).scroll.vert.forward(steps=30)
        time.sleep(3)
        processed_pages += 1
        save_progress(processed_pages)

if __name__ == '__main__':
    main()
